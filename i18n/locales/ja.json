{"operator": "オペレータ", "red": "赤", "orange": "オレンジ", "amber": "アンバー", "yellow": "黄", "lime": "ライム", "green": "緑", "emerald": "エメラルド", "teal": "ティール", "cyan": "シアン", "sky": "スカイ", "blue": "青", "indigo": "インディゴ", "violet": "バイオレット", "purple": "紫", "fuchsia": "紫", "pink": "ピンク", "rose": "ローズ", "errors": {"login": {"9901": "ログインに失敗しました。アカウントはロックされます。", "9902": "アカウントがロックされているため、ログインできません。", "9993": "テナントIDが無効です。", "9995": "無効なユーザ、またはテナントIDが空です。", "9996": "AWSからのログインチャレンジへの応答に失敗しました。"}, "api": {"400": "リクエストが正しくありません", "401": "認証が必要です", "403": "アクセスが拒否されました", "404": "リソースが見つかりません", "409": "データの競合が発生しました", "422": "入力データに問題があります", "429": "リクエストが多すぎます。しばらく待ってから再試行してください", "500": "サーバーエラーが発生しました", "502": "サーバーに接続できません", "503": "サービスが一時的に利用できません", "504": "サーバーの応答がタイムアウトしました", "network": "ネットワークエラーが発生しました", "timeout": "リクエストがタイムアウトしました", "unknown": "予期しないエラーが発生しました", "default": "エラーが発生しました。しばらく待ってから再試行してください", "indexer_updating": "インデクサー更新中のため、この操作は実行できません", "version_mismatch": "環境のバージョンが一致しないため、この操作は実行できません", "insufficient_permissions": "この操作を実行する権限がありません", "resource_locked": "リソースがロックされているため、操作できません", "validation_failed": "入力データの検証に失敗しました"}}, "customone": "カスタマー1", "guest": "ゲスト", "staff": "スタッフ", "admin": "管理者", "Request Entity Too Large": "ファイルサイズが大きすぎます。5MB以下にしてください。", "url": "URL", "title": "タイトル", "label": "ラベル", "content": "コンテンツ", "file": "ファイル", "chunk_number": "チャンク番号", "labels": "ラベル", "timestamp": "タイムスタンプ", "extra_info": "追加情報", "name": "名前", "description": "説明", "source": "ソース", "metadata": "メタデータ", "created_at": "作成日時", "updated_at": "更新日時", "created_username": "作成者", "updated_username": "更新者", "priority": "優先度", "enabled": "有効", "id": "ID", "env_id": "環境ID", "tenant_id": "テナントID", "original_filename": "元ファイル名", "source_url": "ソースURL", "blob_path": "Blobパス", "dynamic": "動的", "ignore_tags": "無視タグ", "ignore_classes": "無視クラス", "Content cannot be updated as environment version is old.": "検証環境のバージョンが商用環境より小さいため更新不可（環境の同期を実施してください）", "Validation error.": "入力データの検証に失敗しました", "Please reset your password": "パスワードをリセットしてください", "User not found for {username}": "ユーザ「{username}」が見つかりません", "User {username} is already registered.": "ユーザ「{username}」はすでに登録されています", "No permission to update target user.": "ターゲットユーザの更新権限がありません。", "Users not found for {username_list}.": "ユーザ「{username_list}」が見つかりません", "You have reached the maximum allowed password changes per hour. Please try again later.": "1時間あたりのパスワード変更の最大数に達しました。しばらく待ってから再試行してください。", "User password cannot be reset as not yet verified.": "ユーザのパスワードはまだ確認されていないためリセットできません。", "Account will now be locked due to too many failed attempts. Please contact PNL administrators to release the account.": "アカウントはロックされます。管理者に連絡してアカウントを解除してください。", "Account is locked due to too many failed attempts. Please contact PNL administrators to release the account.": "アカウントはロックされています。管理者に連絡してアカウントを解除してください。", "Your IP address will now be locked due to too many failed attempts. Please contact PNL administrators to release the account.": "IPアドレスはロックされます。管理者に連絡してアカウントを解除してください。", "Your IP address is locked due to too many failed attempts. Please contact PNL administrators to release the account.": "IPアドレスはロックされています。管理者に連絡してアカウントを解除してください。", "Your IP address is blocked.": "IPアドレスはブロックされています。", "Your IP address is not allowed.": "IPアドレスは許可されていません。", "Tenant not found for {tenant_id}": "テナント「{tenant_id}」が見つかりません", "Tenant {tenant_id} is already registered.": "テナント「{tenant_id}」はすでに登録されています", "Tenant {tenant_id} cannot be deleted as active environments assigned.": "テナント「{tenant_id}」はアクティブな環境が割り当てられているため削除できません", "Tenant {tenant_id} cannot be deleted as active users assigned.": "テナント「{tenant_id}」はアクティブなユーザが割り当てられているため削除できません", "Environment not found for {env_id}": "環境「{env_id}」が見つかりません", "Environment {env_id} is already registered.": "環境「{env_id}」はすでに登録されています", "Production environment for {tenant_id} exist and only 1 production environment is allowed for each tenant.": "テナント「{tenant_id}」の本番環境が存在し、各テナントには1つの本番環境のみ許可されます", "Environment {env_id} is already set to be production environment.": "環境「{env_id}」はすでに本番環境として設定されています", "Environment {env_id} is disabled.": "環境「{env_id}」は無効になっています", "No production environment for {tenant_id}.": "テナント「{tenant_id}」の本番環境が存在しません", "Invalid deployment.": "無効なデプロイメントです。", "Unable to cancel latest deployment.": "最新のデプロイメントをキャンセルできません。", "No deployment record for {tenant_id}.": "テナント「{tenant_id}」のデプロイメントレコードがありません", "Target version is already latest version.": "ターゲットバージョンはすでに最新バージョンです。", "There is a deployment record that is not yet finished.": "完了していないデプロイメントレコードがあります。", "Unable to revoke as target version is older.": "ターゲットバージョンが古いため取り消せません。", "Deployment record not found for {id}": "デプロイメントレコード「{id}」が見つかりません", "Deployment log is not available for this record.": "このレコードのデプロイメントログは利用できません。", "User Group not found for {group_id}.": "ユーザグループ「{group_id}」が見つかりません", "User Group {name} is already registered.": "ユーザグループ「{name}」はすでに登録されています", "User Group is currently disabled.": "ユーザグループは現在無効になっています。", "User {username} is already assigned to the specific user group": "ユーザ「{username}」はすでに特定のユーザグループに割り当てられています", "User Group relation not found for {username} with group {group_id}.": "ユーザ「{username}」はユーザグループ「{group_id}」に割り当てられていません", "User {username} do not belong to the specific user group": "ユーザ「{username}」は特定のユーザグループに属していません", "User Groups not found for {group_id_list}.": "ユーザグループ「{group_id_list}」が見つかりません", "User Group {name} cannot be deleted as there are users assigned to the group": "ユーザグループ「{name}」はユーザが割り当てられているため削除できません", "Some of the User Group relations are already registered.": "ユーザグループ関係の一部はすでに登録されています", "Some of the User Group relations do not exist.": "ユーザグループ関係の一部は存在しません", "Settings of ID {id} not found for {tenant_id}": "設定「{id}」はテナント「{tenant_id}」に見つかりません", "Settings of IP address {ip_addr} is already registered for {tenant_id}": "IPアドレス「{ip_addr}」の設定はテナント「{tenant_id}」にすでに登録されています", "Infra main setting not found for {env_id}": "「{env_id}」ではインフラメイン設定が見つかりません", "Infra main setting is already registered.": "インフラメイン設定はすでに登録されています", "Target model {model} cannot be found in Azure environment.": "ターゲットモデル「{model}」はAzure環境に見つかりません", "Bing Custom Search setting not found for {env_id}": "「{env_id}」ではBingカスタム検索設定が見つかりません", "Bing Custom Search is already registered.": "Bingカスタム検索はすでに登録されています", "Azure Storage setting not found for {env_id}": "「{env_id}」ではAzureストレージ設定が見つかりません", "Azure Storage setting is already registered.": "Azureストレージ設定はすでに登録されています", "Target container {container} cannot be found in Azure environment.": "ターゲットコンテナ「{container}」はAzure環境に見つかりません", "Azure AI Search setting not found for {env_id}": "「{env_id}」ではAzure AI検索設定が見つかりません", "Azure AI Search setting is already registered.": "Azure AI検索設定はすでに登録されています", "Target AI Search Index {name} cannot be found in Azure environment.": "ターゲットAI検索インデックス「{name}」はAzure環境に見つかりません", "Target AI Search Indexer {name} cannot be found in Azure environment.": "ターゲットAI検索インデクサー「{name}」はAzure環境に見つかりません", "Filetype {filetype} is not supported.": "ファイルタイプ「{filetype}」はサポートされていません", "Uploaded file is too big.": "アップロードファイルが大きすぎます。5MB以下にしてください。", "Rag Document not found for {id}": "Ragドキュメント「{id}」が見つかりません", "Rag Content not found for {id}": "Ragコンテンツ「{id}」が見つかりません", "Request document name {name} is already registered.": "リクエストドキュメント名「{name}」はすでに登録されています", "Specific PDF contains unsupported fonts which cannot be imported. Please try another PDF with fonts embedded: , or standard PDF.": "特定のPDFにはサポートされていないフォントが含まれています。フォントが埋め込まれた別のPDFを試すか、標準のPDFを試してください。", "Unexpected error when scraping webpage. Please try again later.": "ウェブページのスクレーピング中に予期しないエラーが発生しました。後でもう一度試してください。", "Unexpected error when uploading files. Please try again later.": "ファイルのアップロード中に予期しないエラーが発生しました。後でもう一度試してください。", "Preview is not available.": "プレビューは利用できません。", "Search Client is currently not available. Please try again later.": "検索クライアントは現在利用できません。後でもう一度試してください。", "Search Indexer is currently not available. Please try again later.": "インデックス更新中のため、操作できません。後でもう一度お試しください。", "Indexing requests are limited to one every 3 minutes. To avoid errors: , please wait a few minutes before trying again.": "インデックスリクエストは3分に1回に制限されています。エラーを避けるために、再試行する前に数分待ってください。", "No Rag Contents are pending for update.": "更新待機中のRagコンテンツがありません。", "Specific content cannot be found.": "特定のコンテンツが見つかりません。", "Target Rag Content is updating.": "ターゲットRagコンテンツは更新中です。", "Target Rag Content is not plain text, cannot be updated.": "ターゲットRagコンテンツはプレーンテキストではないため、更新できません。", "Unexpected error when downloading source file. Please try again later.": "ソースファイルのダウンロード中に予期しないエラーが発生しました。後でもう一度試してください。", "Specific source file not found in S3.": "特定のソースファイルがS3に見つかりません。", "Weather setting not found for {env_id}": "「{env_id}」では天気設定が見つかりません", "Weather setting is already registered.": "天気設定はすでに登録されています。", "Language setting not found of {language} for {env_id}": "「{env_id}」では言語設定「{language}」が見つかりません", "Language setting of {language} is already registered.": "言語設定「{language}」はすでに登録されています", "Language {language} is not supported.": "言語「{language}」はサポートされていません", "Prompt Template of type {prompt_type} not found for {env_id}": "「{env_id}」ではプロンプトテンプレート「{prompt_type}」が見つかりません", "Prompt Template of type {prompt_type} is already registered.": "プロンプトテンプレート「{prompt_type}」はすでに登録されています", "Prompt builder for type {prompt_type} is not supported.": "プロンプトビルダー「{prompt_type}」はサポートされていません", "Prompt builder is not able to build the template by given value. Please check the input value.": "プロンプトビルダーは指定された値でテンプレートを構築できません。入力値を確認してください。", "Statistical Category {id} not found for {env_id}": "「{env_id}」では統計カテゴリ「{id}」が見つかりません", "Statistical Category {name} is already registered.": "統計カテゴリ「{name}」はすでに登録されています", "Target Statistical Category is in usage and cannot be deleted.": "ターゲット統計カテゴリは使用中のため削除できません。", "RAG knowledge label {key} not found for {env_id}": "「{env_id}」ではRAGナレッジラベル「{key}」が見つかりません", "RAG knowledge label {name} is already registered.": "RAGナレッジラベル「{name}」はすでに登録されています", "Unable to create RAG knowledge label. Please try again later.": "RAGナレッジラベルを作成できません。後でもう一度試してください。", "Target label is in usage and cannot be deleted.": "ターゲットラベルは使用中のため削除できません。", "Basic setting not found for {env_id}": "「{env_id}」では基本設定が見つかりません", "Basic setting is already registered.": "基本設定はすでに登録されています。", "Survey option setting of value {value} not found for {env_id}": "「{env_id}」ではアンケートオプション設定「{value}」が見つかりません", "Survey option setting of value {value} is already registered.": "アンケートオプション設定「{value}」はすでに登録されています", "Maximum count of survey option settings exceed.": "アンケートオプション設定の最大数を超えています。", "Error message setting of error code {code} not found for {env_id}": "「{env_id}」ではエラーメッセージ設定「{code}」が見つかりません", "Error message setting of error code {code} is already registered.": "エラーメッセージ設定「{code}」はすでに登録されています", "Feature setting not found for {env_id}": "「{env_id}」では機能設定が見つかりません", "Feature setting is already registered.": "機能設定はすでに登録されています。", "Chatbot custom setting not found for {env_id}": "「{env_id}」ではチャットボットカスタム設定が見つかりません", "Chatbot custom setting is already registered.": "チャットボットカスタム設定はすでに登録されています。", "Custom Message of {trigger_source} not found for {tenant_id}": "テナント「{tenant_id}」ではカスタムメッセージ「{trigger_source}」が見つかりません", "Custom Message of {trigger_source} is already registered.": "カスタムメッセージ「{trigger_source}」はすでに登録されています", "Invalid trigger source {trigger_source}.": "無効なトリガーソース「{trigger_source}」です", "Invalid Custom Message: reason: {error_message}": "無効なカスタムメッセージ: 理由: {error_message}", "Content cannot be updated as production environment": "本番環境のためコンテンツを更新できません", "Method not allowed.": "メソッドが許可されていません。", "Unknown request.": "不明なリクエストです。", "Unauthorized.": "認証されていません。", "Invalid request.": "無効なリクエストです。", "Login Error.": "認証が失敗しました。", "Response challenge error. Please try again later.": "レスポンスチャレンジエラーです。後でもう一度試してください。", "Fatal Error.": "致命的なエラーです。"}
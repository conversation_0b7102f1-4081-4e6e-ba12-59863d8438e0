import { defineConfig } from '@playwright/test'

export default defineConfig({
  testDir: './tests/e2e',
  retries: 0,
  timeout: 60000,
  use: {
    baseURL: 'https://chat.dev.pnl-ai-concierge.com/auth/operator',
    headless: true,
    viewport: { width: 1920, height: 1080 },
    ignoreHTTPSErrors: true,
    trace: 'on-first-retry',
    // screenshot: 'on',
    // video: 'on',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    launchOptions: {
      slowMo: 500, // ★ slowMoをここに
      args: ['--disable-web-security', '--disable-features=IsolateOrigins,site-per-process']
    }
  },
  // fullyParallel: false,
  projects: [
    { name: 'Chrome', use: { browserName: 'chromium', channel: 'chrome' } }
    // { name: 'Edge', use: { browserName: 'chromium', channel: 'msedge' } },
    // { name: 'Firefox', use: { browserName: 'firefox' } },
    // { name: '<PERSON>fari', use: { browserName: 'webkit' } },
  ]
})

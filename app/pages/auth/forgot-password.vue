<script setup lang="ts">
definePageMeta({
  layout: 'auth'
})

useSeoMeta({
  title: 'Login'
})
const authStore = useAuthStore()
const { loadings, errors } = storeToRefs(authStore)

const fields = [
  {
    name: 'username',
    type: 'text',
    label: 'ユーザ名',
    placeholder: 'Ex: username123'
  }
]

const validate = (state: any) => {
  const errors = []
  if (!state.username)
    errors.push({ path: 'username', message: 'ユーザ名は必須です' })
  return errors
}

async function onSubmit(data: any) {
  const result = await authStore.forgetPassword(data.username)
  if (result) {
    // navigate to the reset password page
    navigateTo('/auth/reset-password')
  }
}
</script>

<!-- eslint-disable vue/multiline-html-element-content-newline -->
<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
  <UCard
    data-tour="forgot-password-form"
    class="max-w-sm w-full bg-white/75 dark:bg-white/5 backdrop-blur"
  >
    <UAuthForm
      :fields="fields"
      :validate="validate"
      title="パスワードを忘れた場合"
      align="top"
      icon="hugeicons:forgot-password"
      :ui="{ base: 'text-center', footer: 'text-center' }"
      :submit-button="{ trailingIcon: 'tabler:send', label: '送信' }"
      :loading="loadings.forgetPassword"
      @submit="onSubmit"
    >
      <template #description>
        <div class="text-sm text-left pt-4">
          パスワードをリセットするためにメールアドレスにパスワードリセットリンクを送信します。
        </div>
      </template>
      <template #password-hint>
        <NuxtLink
          to="/auth/forgot-password"
          class="text-primary font-medium"
        >
          パスワードをお忘れですか？
        </NuxtLink>
      </template>
      <template #validation>
        <UAlert
          v-if="errors.forgetPassword"
          color="red"
          icon="i-heroicons-information-circle-20-solid"
          title="エラーが発生しました。"
          :description="errors.forgetPassword?.error_code"
        />
      </template>
      <template #footer>
        <div class="text-sm text-left pb-6">
          <UButton
            icon="i-heroicons-arrow-left-20-solid"
            size="lg"
            color="gray"
            variant="solid"
            label="ログイン画面に戻る"
            :trailing="false"
            :block="true"
            to="/auth/login"
          />
        </div>
        <div>PlayNext Lab @ {{ new Date().getFullYear() }}</div>
      </template>
    </UAuthForm>
  </UCard>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { FormError } from '#ui/types'

const { t, locale, locales } = useI18n()
definePageMeta({
  layout: 'auth',
  notRequiresAuth: true
})

useSeoMeta({
  title: 'PNL管理者のLogin'
})

const authStore = useAuthStore()
const { loadings, errors, authenticated } = storeToRefs(authStore)
const fields = [
  {
    name: 'username',
    type: 'text',
    label: 'ユーザ名',
    placeholder: 'Ex: john.doe',
    tabindex: 2
  },
  {
    name: 'password',
    label: 'パスワード',
    type: 'password',
    placeholder: 'パスワードを入力してください',
    tabindex: 3
  }
]
// 最小 8 文字
// 少なくとも 1 つの数字を含む
// 少なくとも 1 つの小文字を含む
// 少なくとも 1 つの大文字を含む
const schema = z.object({
  // tenant_id is not required
  tenant_id: z.string().optional(),
  username: z.string().nonempty('ユーザ名は必須です'),
  password: z
    .string()
    .nonempty('パスワードは必須です')
    .min(8, 'パスワードは8文字以上で入力してください')
})

const validate = async (state: any) => {
  const errors: FormError[] = []
  if (!state.username)
    errors.push({ path: 'username', message: 'ユーザ名は必須です' })
  if (!state.password)
    errors.push({ path: 'password', message: 'パスワードは必須です' })

  if (state.password?.length < 8) {
    errors.push({
      path: 'password',
      message: 'パスワードは8文字以上で入力してください'
    })
  }

  if (!/[A-Z]/.test(state.password)) {
    errors.push({
      path: 'password',
      message: 'パスワードには少なくとも1つの大文字を含めてください'
    })
  }

  if (!/[a-z]/.test(state.password)) {
    errors.push({
      path: 'password',
      message: 'パスワードには少なくとも1つの小文字を含めてください'
    })
  }

  if (!/[0-9]/.test(state.password)) {
    errors.push({
      path: 'password',
      message: 'パスワードには少なくとも1つの数字を含めてください'
    })
  }

  const specialChars = `^$*.[]{}()?-"!@#%&/\\,><':;|_~\`+=`
  const specialCharPattern = new RegExp(
    `[${specialChars.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')}]`
  )
  const spacePattern = /^(?! ).*(?<! )$/ // 先頭・末尾のスペースを禁止
  if (!specialCharPattern.test(state.password)) {
    errors.push({
      path: 'password',
      message: 'パスワードには少なくとも1つの特殊文字を含めてください'
    })
  }

  if (!spacePattern.test(state.password)) {
    errors.push({
      path: 'password',
      message: 'パスワードには先頭・末尾のスペースを含めないでください'
    })
  }
  return errors
}

async function onSubmit(data: {
  tenant_id: string
  username: string
  password: string
}) {
  const result = await authStore.login(
    data.tenant_id,
    data.username,
    data.password
  )
  if (result) {
    navigateTo('/')
  }
}

onMounted(() => {
  if (authenticated.value) {
    navigateTo('/')
  }
})

const errorDescription = computed(() => {
  // Get the error code from the response
  const errorCode = errors.value.login?.error_code
  // Try to get the translation
  const translation = t(`errors.login.${errorCode}`)

  // If we have a translation, use it
  if (translation && translation !== `errors.login.${errorCode}`) {
    return translation
  }

  // Otherwise, use the error message from the response or a generic message
  return errors.value.login?.error_message
    || errors.value.login?.message
    || `エラーコード: ${errorCode}`
    || 'ログインに失敗しました。'
})
</script>

<!-- eslint-disable vue/multiline-html-element-content-newline -->
<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
  <UCard class="max-w-sm w-full bg-white/75 dark:bg-white/5 backdrop-blur">
    <UAuthForm
      :fields="fields"
      :schema="schema"
      :validate="validate"
      title="おかえりなさい"
      align="top"
      icon="i-heroicons-lock-closed"
      :ui="{ base: 'text-center', footer: 'text-center' }"
      :submit-button="{
        trailingIcon: 'i-heroicons-arrow-right-20-solid',
        label: 'ログイン'
      }"
      :loading="loadings.login"
      @submit="onSubmit"
    >
      <template #description>
        <div class="text-sm">スマート公共ラボ AIコンシェルジュへログイン</div>
      </template>
      <template #validation>
        <UAlert
          v-if="errors.login"
          color="red"
          icon="i-heroicons-information-circle-20-solid"
          title="ログイン失敗"
          :description="errorDescription"
        />
      </template>
      <template #password-hint>
        <NuxtLink
          to="/auth/forgot-password"
          class="text-primary font-medium"
        >
          パスワードをお忘れですか？
        </NuxtLink>
      </template>
      <template #footer>
        <BaseFooter class="justify-center" />
      </template>
    </UAuthForm>
  </UCard>
</template>

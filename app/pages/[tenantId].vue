<script setup lang="ts">
// middleware authentication
definePageMeta({
  middleware: 'authentication'
})
const route = useRoute()
const environmentsStore = useEnvironmentsStore()
const { selectedEnvId, environments } = storeToRefs(environmentsStore)
onMounted(() => {
  if (!route.params.env) {
    const isExistEnv = environments.value[route.params.tenantId as string]?.find(env => env.id === selectedEnvId.value)
    if (!isExistEnv) {
      selectedEnvId.value = environments.value[route.params.tenantId as string]?.[0]?.id
    }
    navigateTo(`/${route.params.tenantId}/${selectedEnvId.value}`)
  }
})
</script>

<template>
  <NuxtPage />
</template>

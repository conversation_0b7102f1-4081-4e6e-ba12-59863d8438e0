<script setup lang="ts">
definePageMeta({
  layout: 'blank',
  middleware: 'authentication'
})
const authStore = useAuthStore()
const app = useApp()

onMounted(async () => {
  await authStore.fetchUser()
  app.initialApp()
})
</script>

<template>
  <div class="flex flex-col items-center justify-center gap-0">
    <BaseLoader class="w-32" />
    <div class="text-center text-white flex flex-col items-center gap-2">
      <div class="text-xl font-semibold text-primary-800 dark:text-primary-500">
        スマート公共ラボ AIコンシェルジュへようこそ！
      </div>
      <div class="text-sm text-black dark:text-gray-200">
        データの読み込み中です。お待ちください...
      </div>
    </div>
  </div>
</template>

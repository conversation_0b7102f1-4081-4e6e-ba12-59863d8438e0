<script setup lang="ts">
definePageMeta({
  layout: 'liff'
})
const route = useRoute()
const newChatbotInstance = ref(null)

onMounted(() => {
  const { tenantId, env, liffId } = route.query
  console.log('LINE LIFF page loaded with params:', { tenantId, env, liffId })
  if (window?.LLMRagChatbot) {
    newChatbotInstance.value = new window.LLMRagChatbot.Class({
      liffId,
      tenantId,
      env
    })
  }
})
</script>

<template>
  <div class="flex flex-col items-center justify-center h-screen w-screen">
    <div
      v-if="!newChatbotInstance"
      class="text-center p-4"
    >
      <h1
        class="text-xl font-semibold text-primary-800 dark:text-primary-500 mb-2"
      >
        LINE Liffチャットページ
      </h1>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        チャットボットを読み込んでいます...
      </p>
    </div>
  </div>
</template>

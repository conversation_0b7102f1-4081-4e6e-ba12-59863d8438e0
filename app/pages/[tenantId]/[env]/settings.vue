<script setup lang="ts">
// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: [
    'view_basic_settings',
    'view_system_settings',
    'view_users',
    'view_labels',
    'view_categories',
    'view_tenants'
  ]
})

const route = useRoute()
const {
  settingsGroupNavigators,
  basicSettingsNavigators,
  knowledgeSettingsNavigators,
  statisticsSettingsNavigators,
  deploymentNavigators,
  ragSettingsNavigators,
  pnlOperatorSettingsNavigators
} = useNavigators()

// Use role-based navigators
const { roleBasedAdminSettingsNavigators } = useRoleBasedNavigators()

// Import permissions
const { PERMISSIONS } = useAppPermissions()

const breadcrumb = computed(() => {
  // Search for current setting navigator across all navigator types
  let currentSettingNavigator = null

  // Create an array of all navigator functions to search through
  const allNavigatorFunctions = [
    () => basicSettingsNavigators.value(true),
    () => knowledgeSettingsNavigators.value(true),
    () => statisticsSettingsNavigators.value(true),
    () => roleBasedAdminSettingsNavigators.value,
    () => deploymentNavigators.value,
    () => ragSettingsNavigators.value(true),
    () => pnlOperatorSettingsNavigators.value(true)
  ]

  // Search through all navigator types to find the current page
  for (const getNavigators of allNavigatorFunctions) {
    try {
      const navigators = getNavigators()
      const found = navigators.find((nav: any) => route.name === nav.id)
      if (found) {
        currentSettingNavigator = found
        break
      }
    } catch (error) {
      // Skip if navigator function throws an error (e.g., permission issues)
      continue
    }
  }

  return [
    {
      label: settingsGroupNavigators.value?.[0]?.label || '設定',
      icon:
        settingsGroupNavigators.value?.[0]?.icon || 'i-heroicons-cog-6-tooth',
      to: `/${route.params.tenantId}/${route.params.env}/settings`
    },
    {
      label: currentSettingNavigator?.label,
      icon: currentSettingNavigator?.icon
    }
  ] as any[]
})
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar>
        <template #title>
          <UBreadcrumb :links="breadcrumb" />
        </template>
      </UDashboardNavbar>

      <UDashboardPanelContent class="flex flex-row p-0 scrollbar-thin">
        <UDashboardPanel
          :width="250"
          :resizable="{ min: 200, max: 400 }"
        >
          <UDashboardSidebar class="pt-4">
            <div class="flex flex-col gap-4">
              <div>
                <div>
                  <div class="text-[11px] font-bold text-gray-500 pb-2 pl-3">
                    基本設定
                  </div>
                </div>
                <UDashboardSidebarLinks
                  :links="basicSettingsNavigators(true) as any[]"
                />
              </div>

              <!-- Only show knowledge settings if user has permission -->
              <PermissionGuard :permission="PERMISSIONS.VIEW_LABELS">
                <div>
                  <div>
                    <div class="text-[11px] font-bold text-gray-500 pb-2 pl-3">
                      ナレッジ設定
                    </div>
                  </div>
                  <UDashboardSidebarLinks
                    :links="knowledgeSettingsNavigators(true) as any[]"
                  />
                </div>
              </PermissionGuard>

              <div>
                <div>
                  <div class="text-[11px] font-bold text-gray-500 pb-2 pl-3">
                    統計設定
                  </div>
                </div>
                <UDashboardSidebarLinks
                  :links="statisticsSettingsNavigators(true) as any[]"
                />
              </div>

              <!-- Only show admin settings if user has admin permissions -->
              <PermissionGuard
                :permissions="[
                  PERMISSIONS.VIEW_USERS,
                  PERMISSIONS.VIEW_USER_GROUPS
                ]"
              >
                <div>
                  <div>
                    <div class="text-[11px] font-bold text-gray-500 pb-2 pl-3">
                      管理者設定
                    </div>
                  </div>
                  <UDashboardSidebarLinks
                    :links="roleBasedAdminSettingsNavigators as any[]"
                  />
                </div>
              </PermissionGuard>

              <div>
                <div>
                  <div class="text-[11px] font-bold text-gray-500 pb-2 pl-3">
                    デプロイメント
                  </div>
                </div>
                <UDashboardSidebarLinks
                  :links="deploymentNavigators as any[]"
                />
              </div>

              <!-- Only show RAG settings if user has permission -->
              <PermissionGuard :permission="PERMISSIONS.VIEW_RAG_SETTINGS">
                <div>
                  <div>
                    <div class="text-[11px] font-bold text-gray-500 pb-2 pl-3">
                      PNL管理者専用
                    </div>
                  </div>
                  <UDashboardSidebarLinks
                    :links="pnlOperatorSettingsNavigators(true) as any[]"
                  />
                </div>
              </PermissionGuard>
            </div>
          </UDashboardSidebar>
        </UDashboardPanel>

        <NuxtPage />
      </UDashboardPanelContent>
    </UDashboardPanel>
  </UDashboardPage>
</template>

<script setup lang="ts">
const route = useRoute()
const { supportNavigators, supportOptionsNavigators } = useNavigators()

const breadcrumb = computed(() => {
  const currentSupportNavigator = supportOptionsNavigators.value.find(
    (nav: any) => route.name === nav.id
  )

  const items = [
    {
      label: 'サポート',
      icon: 'material-symbols:help-outline',
      to: `/${route.params.tenantId}/${route.params.env}/support`
    }
  ]

  if (currentSupportNavigator?.label) {
    items.push({
      label: currentSupportNavigator.label,
      icon: currentSupportNavigator.icon || 'material-symbols:help-outline'
    })
  }

  return items
})
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar>
        <template #title>
          <UBreadcrumb :links="breadcrumb" />
        </template>
      </UDashboardNavbar>

      <UDashboardPanelContent class="flex flex-row p-0 scrollbar-thin">
        <UDashboardPanel
          :width="250"
          :resizable="{ min: 200, max: 400 }"
        >
          <UDashboardSidebar class="pt-4">
            <div class="flex flex-col gap-4">
              <div>
                <div>
                  <div class="text-[11px] font-bold text-gray-500 pb-2 pl-3">
                    ヘルプ＆サポート
                  </div>
                </div>
                <UDashboardSidebarLinks :links="supportOptionsNavigators" />
              </div>
            </div>
          </UDashboardSidebar>
        </UDashboardPanel>

        <NuxtPage />
      </UDashboardPanelContent>
    </UDashboardPanel>
  </UDashboardPage>
</template>

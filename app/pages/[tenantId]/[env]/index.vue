<script setup lang="ts">
// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_dashboard']
})
const ragsStore = useRagsStore()
const reportsStore = useReportsStore()
const { range, periodType, selectedCategories, loadings }
  = storeToRefs(reportsStore)
const categoriesStore = useCategoriesStore()
const settingsSurveyStore = useSettingsSurveyStore()
const { selectedTenantId, selectedEnvId } = useApp()
const { tenantTokens } = storeToRefs(ragsStore)
watchEffect(async () => {
  await ragsStore.ragTenantLogin()
  await categoriesStore.fetchCategories(
    selectedTenantId.value,
    selectedEnvId.value
  )
  refresh()
})

const refresh = () => {
  reportsStore.getDailySessions()
  reportsStore.getUnansweredReport()
  reportsStore.getDailyResponseRate()
  reportsStore.getReportSummary()
  reportsStore.getSurveyReport()
  settingsSurveyStore.fetchAllSettingsSurveyOptions(
    selectedTenantId.value,
    selectedEnvId.value
  )
}

// Check if any of the main dashboard data is loading
const isRefreshing = computed(() => {
  return (
    loadings.value.getDailySessions
    || loadings.value.getUnansweredReport
    || loadings.value.getDailyResponseRate
    || loadings.value.getReportSummary
    || loadings.value.getSurveyReport
  )
})
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar title="ダッシュボード" />
      <UDashboardToolbar>
        <template #left>
          <div
            class="flex items-center"
            data-tour="dashboard-toolbar"
          >
            <BaseDateRangePicker
              v-model="range"
              :exclude-today="true"
              data-tour="date-range-picker"
              @update:model-value="refresh"
            />
            <BasePeriodSelect
              v-model="periodType"
              :range="range"
            />
            <CategoriesSelect
              v-model="selectedCategories"
              variant="none"
              size="md"
            />
          </div>
        </template>
        <template #right>
          <UButton
            icon="prime:sync"
            color="gray"
            size="sm"
            :loading="isRefreshing"
            data-tour="refresh-button"
            @click="refresh"
          />
        </template>
      </UDashboardToolbar>

      <UDashboardPanelContent>
        <DashboardStatistics
          class="pb-6"
          :range="range"
          data-tour="dashboard-statistics"
        />
        <!-- ~/components/home/<USER>
        <DashboardSessionsChart
          data-tour="dashboard-sessions-chart"
          :period="periodType"
          :range="range"
        />

        <DashboardResponseRateChart
          data-tour="dashboard-response-rate-chart"
          class="mt-8"
          :period="periodType"
          :range="range"
        />
      </UDashboardPanelContent>
    </UDashboardPanel>
  </UDashboardPage>
</template>

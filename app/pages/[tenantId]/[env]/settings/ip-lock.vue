<script lang="ts" setup>
import { debounce } from 'lodash'

const confirm = useConfirm()
const toast = useToast()
const authStore = useAuthStore()
const { lockedIpAddresses, loadings } = storeToRefs(authStore)
const inputIpAddress = ref<string>('')

onMounted(async () => {
  await authStore.getLockedIpAddresses(inputIpAddress.value)
})

const refresh = debounce(async () => {
  await authStore.getLockedIpAddresses(inputIpAddress.value)
}, 500)

watch(
  () => inputIpAddress.value,
  () => {
    refresh()
  }
)
const page = ref(1)
const pageTotal = computed(() => lockedIpAddresses.value.length)
const pageCount = ref(5)

const dataPaginated = computed(() => {
  const mapped = lockedIpAddresses.value?.map(ip => ({
    ip_address: ip,
    id: ip
  })) || []

  return mapped.slice(
    (page.value - 1) * pageCount.value,
    page.value * pageCount.value
  )
})
watch(pageCount, () => {
  page.value = 1
})
const rowMenus = (row: any) => {
  return [
    [
      {
        label: 'ロック解除',
        icon: 'ph:lock-simple-open-fill',
        click: () => {
          confirm.show({
            title: `解除の確認`,
            description: `IPアドレス「${row.ip_address}」を解除しますか？`,
            confirmText: '解除',
            onConfirm: async () => {
              const response = await authStore.releaseIpLock(row.ip_address)
              if (!response) {
                toast.add({
                  id: 'error',
                  title: 'エラー',
                  description: 'IPアドレス解除に失敗しました。',
                  color: 'red'
                })
              } else {
                toast.add({
                  title: '解除完了',
                  description: 'IPアドレスを解除しました。',
                  color: 'green'
                })
              }
            }
          })
        }
      }
    ]
  ]
}

const defaultColumns = [
  {
    key: 'ip_address',
    label: 'IPアドレス',
    sortable: true
  },
  {
    key: 'id',
    label: '',
    sortable: false
  }
]
</script>

<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-3 !py-3' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700'
      },
      footer: { padding: 'p-4' }
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight">
          ロックされたIPアドレス
          <UBadge
            v-if="lockedIpAddresses?.length"
            :label="lockedIpAddresses.length"
          />
        </h2>
      </div>
    </template>

    <div class="flex items-center justify-between gap-3 px-4 py-3">
      <div class="flex items-center gap-3">
        <UInput
          v-model="inputIpAddress"
          icon="i-heroicons-magnifying-glass-20-solid"
          placeholder="IPアドレス..."
          @keydown.esc="$event.target.blur()"
        >
          <template #trailing>
            <UKbd value="/" />
          </template>
        </UInput>
      </div>
      <div class="flex items-center gap-1.5">
        <div />
        <UButton
          icon="prime:sync"
          color="gray"
          size="sm"
          @click="refresh"
        />
      </div>
    </div>

    <UTable
      v-if="dataPaginated"
      :rows="dataPaginated"
      :columns="defaultColumns"
      :loading="loadings.getLockedIps"
      class="w-full"
      :ui="{
        divide: 'divide-gray-200 dark:divide-gray-800',
        tr: { base: 'group' }
      }"
    >
      <template #username-data="{ row }">
        <div class="text-xs text-gray-500 dark:text-gray-400">
          {{ row.username }}
        </div>
      </template>

      <template #id-data="{ row }">
        <div class="flex items-center gap-3 justify-end">
          <UDropdown
            class="invisible group-hover:visible"
            :items="rowMenus(row)"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              class="row-menu"
              color="white"
              icon="charm:menu-meatball"
              size="xs"
              square
              :loading="false"
            />
          </UDropdown>
        </div>
      </template>
    </UTable>

    <template #footer>
      <div class="flex flex-wrap justify-between items-center">
        <div class="flex items-center gap-1.5">
          <span class="text-sm leading-5">表示件数:</span>
          <USelect
            v-model="pageCount"
            :options="[3, 5, 10, 20, 30, 40]"
            class="w-20"
          />
        </div>

        <UPagination
          v-model="page"
          :page-count="pageCount"
          :total="pageTotal"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline'
              }
            }
          }"
        />
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_categories']
})

const { selectedTenantId, selectedEnvId } = useApp()


const categoriesStore = useCategoriesStore()
const { loadings, categories, errors, categoryForm } = storeToRefs(categoriesStore)
const confirm = useConfirm()
const isFormOpen = ref(categoryForm)
onMounted(() => {
  categoriesStore.fetchCategories(selectedTenantId.value, selectedEnvId.value)
})

const newCategory = ref(null)
const onSubmit = async (data: any) => {
  if (selectedCategory.value) {
    const result = await categoriesStore.updateCategory(
      selectedTenantId.value,
      selectedEnvId.value,
      {
        category_id: selectedCategory.value.category_id,
        category: data?.category
      }
    )
    if (result) {
      isFormOpen.value = false
    }
  } else {
    const result = await categoriesStore.createCategory(
      selectedTenantId.value,
      selectedEnvId.value,
      {
        category: data?.category
      }
    )
    if (result) {
      newCategory.value = result
      isFormOpen.value = false
    }
  }
}

const selectedCategory = ref(null) as Ref<any>

const onCreate = () => {
  selectedCategory.value = null
  isFormOpen.value = true
}

const onUpdate = (row: any) => {
  selectedCategory.value = row
  isFormOpen.value = true
}

const onDelete = (row: any) => {
  confirm.show({
    title: `カテゴリ削除の確認`,
    description: `カテゴリ「${row.category || row.category_id}」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      await categoriesStore.deleteCategory(
        selectedTenantId.value,
        selectedEnvId.value,
        row.category_id
      )
    }
  })
}
</script>

<template>
  <UDashboardPanelContent class="p-0">
    <BaseEmptyList
      v-if="!categories.length && !loadings.fetchCategories"
      data-tour="no_categories"
      text="カテゴリがありません"
      init-button
      @init="onCreate"
    />
    <CategoriesTable
      v-else
      data-tour="categories"
      :categories="categories"
      :loading="loadings.fetchCategories"
      @edit="onUpdate"
      @delete="onDelete"
      @create="onCreate"
      @refresh="categoriesStore.fetchCategories(selectedTenantId, selectedEnvId, true)"
    />
    <UDashboardModal
      v-model="isFormOpen"
      data-tour="category-modify-dialog"
      :title="selectedCategory ? 'カテゴリを編集' : '新規カテゴリ作成'"
      :description="
        selectedCategory
          ? 'カテゴリの情報を編集します。'
          : '新しいカテゴリを作成します。'
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <!-- ~/components/settings/MembersForm.vue -->
      <CategoryForm
        :loading="loadings.createCategory || loadings.updateCategory"
        v-bind="selectedCategory"
        :error="errors.createCategory || errors.updateCategory"
        @close="isFormOpen = false"
        @submit="onSubmit"
      />
    </UDashboardModal>
  </UDashboardPanelContent>
</template>

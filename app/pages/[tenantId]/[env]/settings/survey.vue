<script setup lang="ts">
// Define required permissions for this page
import { cloneDeep, debounce, orderBy } from 'lodash'

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_survey_settings']
})

const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()
const settingsSurveyStore = useSettingsSurveyStore()

const settingsStore = useSettingsStore()
const { basicSettings, loadings, survey_option_count }
  = storeToRefs(settingsStore)
const confirm = useConfirm()

const ui = {
  formGroup: { container: 'col-span-7', inner: 'col-span-5' }
}

const classNames = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const {
  settingsSurveyOptions,
  selectedSurveyOption,
  loadings: surveyLoadings
} = storeToRefs(settingsSurveyStore)
const updatedSuccess = ref({} as Record<string, boolean>)
const surveyOptionCountError = ref('')
const isInitialLoading = ref(false)

onMounted(async () => {
  isInitialLoading.value = true
  try {
    // Load basic settings if not already loaded
    if (!basicSettings.value?.[selectedTenantId.value]?.[selectedEnvId.value]) {
      await settingsStore.fetchBasicSettings(
        selectedTenantId.value,
        selectedEnvId.value
      )
    }

    await settingsSurveyStore.fetchAllSettingsSurveyOptions(
      selectedTenantId.value,
      selectedEnvId.value
    )
    // Clear any existing error on mount
    surveyOptionCountError.value = ''
  } finally {
    isInitialLoading.value = false
  }
})

// Computed property to track existing survey options count
const existingSurveyOptionsCount = computed(() => {
  return settingsSurveyOptions.value.filter(option => !option.isNew).length
})

// Form state for UForm component
const formState = ref({})

const onUpdateBasicSettings = debounce(async (key: string, value: any) => {
  // Special handling for survey_option_count
  if (key === 'survey_option_count') {
    if (value < existingSurveyOptionsCount.value) {
      surveyOptionCountError.value = `現在 ${existingSurveyOptionsCount.value} 個のアンケート選択肢が設定されています。先に不要な選択肢を削除してください。`
      return
    }
    surveyOptionCountError.value = ''
  }

  const result = await settingsStore.updateBasicSetting(
    selectedTenantId.value,
    selectedEnvId.value,
    key,
    value
  )
  if (result) {
    updatedSuccess.value[key] = true
    setTimeout(() => {
      updatedSuccess.value[key] = false
    }, 2000)
  }
}, 1000)

const unsetOptionObject = { text: '未設定', value: -1, isNew: true }
const settingsSurveyOptionsCanSetting = computed(() => {
  const options = [] as any[];
  [0, 1, 2, 3, 4].forEach((value) => {
    const option = settingsSurveyOptions.value.find(
      option => option.value === value
    )
    if (option) {
      options.push(cloneDeep(option))
    } else {
      options.push({
        ...unsetOptionObject,
        value
      })
    }
  })
  return orderBy(options, 'value')
})

const resetSurveyOption = () => {
  // find index of selectedSurveyOption in settingsSurveyOptionsCanSetting and reset it
  const index = settingsSurveyOptionsCanSetting.value.findIndex(
    option => option.value === selectedSurveyOption.value?.value
  )
  console.log('🚀 ~ resetSurveyOption ~ index:', index)
  if (index !== -1) {
    selectedSurveyOption.value = cloneDeep(
      settingsSurveyOptionsCanSetting.value[index]
    )
  } else {
    selectedSurveyOption.value = cloneDeep(unsetOptionObject)
  }
}

const deleteSurveyOption = async () => {
  confirm.show({
    title: '確認',
    description: 'このアンケート選択を削除しますか？',
    confirmText: '削除',
    onConfirm: async () => {
      const result = await settingsSurveyStore.deleteSurveyOption(
        selectedTenantId.value,
        selectedEnvId.value,
        selectedSurveyOption.value?.value
      )
      if (result) {
        resetSurveyOption()

        // Auto-update survey option count after deletion
        // Wait for the store to update before checking the count
        await nextTick()
        const currentCount = basicSettings.value[selectedTenantId.value][selectedEnvId.value].survey_option_count

        if (existingSurveyOptionsCount.value < currentCount) {
          await settingsStore.updateBasicSetting(
            selectedTenantId.value,
            selectedEnvId.value,
            'survey_option_count',
            existingSurveyOptionsCount.value
          )
        }

        // Clear any existing error
        surveyOptionCountError.value = ''
      }
    }
  })
}

const onUpdateOrCreateSurveyOption = async () => {
  // check if selectedSurveyOption is new
  if (selectedSurveyOption.value?.isNew) {
    // create new survey option
    const result = await settingsSurveyStore.createSurveyOption(
      selectedTenantId.value,
      selectedEnvId.value,
      selectedSurveyOption.value
    )
    if (result) {
      selectedSurveyOption.value = cloneDeep(result)
    }
  } else {
    // update survey option
    await settingsSurveyStore.updateSurveyOption(
      selectedTenantId.value,
      selectedEnvId.value,
      selectedSurveyOption.value
    )
    // selectedSurveyOption.value = cloneDeep(result)
  }
}

const onInitSetting = () => {
  settingsStore.createBasicSetting(
    selectedTenantId.value,
    selectedEnvId.value,
    {
      name: 'のAIチャットボット',
      welcome_message:
        'こんにちは！ 私はAIチャットボットです。何かお手伝いできますか？',
      survey_option_count: 2
    }
  )
}
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <!-- Loading state -->
    <div v-if="isInitialLoading || loadings.fetchBasicSettings || surveyLoadings.fetchAllSettingsSurveyOptions" class="space-y-4">
      <div class="flex items-center justify-between mb-4">
        <div>
          <USkeleton class="h-6 w-48 mb-2" />
          <USkeleton class="h-4 w-64" />
        </div>
      </div>

      <div class="space-y-6">
        <UFormGroup
          v-for="field in 3"
          :key="field"
          class="!items-baseline w-full mt-10"
        >
          <template #label>
            <USkeleton class="h-4 w-32 mb-2" />
          </template>
          <template #description>
            <USkeleton class="h-3 w-48 mb-3" />
          </template>
          <USkeleton class="h-9 w-full" />
        </UFormGroup>
      </div>
    </div>

    <!-- Content when loaded -->
    <UForm
      v-else-if="
        basicSettings?.[selectedTenantId]?.[selectedEnvId]
          && settingsSurveyOptions
      "
      :state="formState"
      :validate-on="['submit']"
    >
      <UDashboardSection
        title="アンケート設定"
        description="チャットボットの回答後に表示するアンケートの設定を行います。"
      >
        <UFormGroup
          data-tour="survey_cast_number"
          name="survey_option_count"
          label="アンケート選択肢数"
          required
          :class="classNames.formGroup"
          :ui="ui.formGroup"
        >
          <template #description>
            <div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                アンケートの選択肢を設定します。
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                設定したアンケート選択肢数を超える
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                選択肢の設定はできません。
              </div>
            </div>
          </template>
          <BaseSelect
            v-model="
              basicSettings[selectedTenantId][selectedEnvId].survey_option_count
            "
            icon="teenyicons:button-outline"
            :options="[1, 2, 3, 4, 5]"
            :updated-success="updatedSuccess.survey_option_count"
            :loading="loadings.updateBasicSetting['survey_option_count']"
            size="md"
            :disabled="isSelectedEnvIsProd"
            @update:model-value="
              onUpdateBasicSettings('survey_option_count', $event)
            "
          />
          <div
            v-if="surveyOptionCountError"
            class="text-red-500 text-sm mt-1 col-start-1 col-span-12"
          >
            {{ surveyOptionCountError }}
          </div>
        </UFormGroup>
        <div>
          <UFormGroup
            data-tour="survey_option_count"
            name="survey_option_count"
            label="アンケート選択"
            description="アンケート選択肢を設定します。"
            required
            :class="classNames.formGroup"
            :ui="ui.formGroup"
            class="!items-baseline"
          >
            <div>
              <div class="grid grid-cols-5 gap-3">
                <div
                  v-for="(option, index) in settingsSurveyOptionsCanSetting"
                  :key="option.value + index"
                  class="flex flex-col gap-2 justify-start items-center"
                >
                  <div
                    class="text-sm text-gray-500 dark:text-gray-400"
                    :class="{
                      'text-primary dark:text-primary font-bold':
                        selectedSurveyOption?.value === option.value
                    }"
                  >
                    {{ option.value }}
                  </div>
                  <UButton
                    :variant="
                      selectedSurveyOption?.value === option.value
                        ? 'outline'
                        : 'solid'
                    "
                    :color="
                      selectedSurveyOption?.value === option.value
                        ? 'primary'
                        : option.isNew
                          ? 'gray'
                          : 'white'
                    "
                    :ui="{
                      color: {
                        gray: {
                          solid:
                            'border border-dashed border-gray-400 dark:border-gray-500'
                        }
                      }
                    }"
                    class="w-full"
                    @click="selectedSurveyOption = cloneDeep(option)"
                  >
                    <span class="truncate">{{ option.text }}</span>
                    <span v-if="!option.text">&lt;なし&gt;</span>
                  </UButton>
                  <div>
                    <UIcon
                      v-if="selectedSurveyOption?.value === option.value"
                      name="line-md:arrow-down"
                      class="text-primary-500 dark:text-primary-500 font-bold text-2xl"
                    />
                  </div>
                </div>
              </div>
            </div>
          </UFormGroup>
          <UCard
            v-if="selectedSurveyOption"
            data-tour="survey_editor"
          >
            <div class="flex flex-col gap-4">
              <UFormGroup
                data-tour="survey_label"
                name="text"
                label="選択ラベル"
                description="アンケートの選択肢の内容を設定します。"
                required
                :class="classNames.formGroup"
                :ui="ui.formGroup"
              >
                <BaseInput
                  v-model="selectedSurveyOption.text"
                  size="md"
                  :disabled="isSelectedEnvIsProd"
                />
              </UFormGroup>
              <UFormGroup
                data-tour="survey_reply_message"
                name="reply_message"
                label="返信メッセージ"
                description="アンケートの選択肢を選択後に返信するメッセージを設定します。"
                required
                :class="classNames.formGroup"
                :ui="ui.formGroup"
                class="!items-baseline"
              >
                <template #description>
                  <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      アンケートの選択肢を選択後に返信する
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      メッセージを設定します。
                    </div>
                  </div>
                </template>
                <BaseMessageEditor
                  v-model="selectedSurveyOption.reply_message"
                  data-tour="survey_reply_message_editor"
                  :disabled="isSelectedEnvIsProd"
                />
              </UFormGroup>
              <UFormGroup
                v-if="!isSelectedEnvIsProd"
                label=" "
                :class="classNames.formGroup"
                :ui="ui.formGroup"
              >
                <div class="flex justify-between">
                  <BasicFormButtonGroup
                    data-tour="survey_update"
                    cancel-label="リセット"
                    submit-label="更新"
                    :loading="
                      surveyLoadings.updateSurveyOption
                        || surveyLoadings.createSurveyOption
                    "
                    @close="resetSurveyOption"
                    @submit="onUpdateOrCreateSurveyOption"
                  />

                  <div>
                    <UButton
                      v-if="!selectedSurveyOption.isNew"
                      data-tour="survey_delete"
                      label="削除"
                      color="red"
                      variant="outline"
                      @click="deleteSurveyOption"
                    />
                  </div>
                </div>
              </UFormGroup>
            </div>
          </UCard>
        </div>
      </UDashboardSection>
    </UForm>
    <BaseEmptyList
      v-else-if="!isInitialLoading && !loadings.fetchBasicSettings && !surveyLoadings.fetchAllSettingsSurveyOptions"
      id="survey_is_empty"
      icon="fluent:bot-28-regular"
      text="チャットボットの設定がありません"
      init-button
      init-button-label="初期設定"
      @init="onInitSetting"
    />
  </UDashboardPanelContent>
</template>

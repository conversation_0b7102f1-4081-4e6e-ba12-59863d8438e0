<script setup lang="ts">
// Define required permissions for this page
import { debounce } from 'lodash'
import { z } from 'zod'
import type { FormError, FormSubmitEvent } from '#ui/types'
import { DEFAULT_ERROR_MESSAGES } from '~/constants/supported-languages'

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_error_messages']
})

const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()
const settingsStore = useSettingsStore()
const { errorSettings, loadings, allRagErrorSettings, isNewErrorModalOpen }
  = storeToRefs(settingsStore)
const confirm = useConfirm()

const ui = {
  formGroup: { container: 'col-span-7', inner: 'col-span-5' }
}

const classNames = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const state = reactive({
  name: '',
  avatar: '',
  bio: '',
  password_current: '',
  password_new: ''
})

const toast = useToast()

function validate(state: any): FormError[] {
  const errors = []
  if (!state.name)
    errors.push({ path: 'name', message: 'Please enter your name.' })
  if (!state.email)
    errors.push({ path: 'email', message: 'Please enter your email.' })
  if (
    (state.password_current && !state.password_new)
    || (!state.password_current && state.password_new)
  )
    errors.push({
      path: 'password',
      message: 'Please enter a valid password.'
    })
  return errors
}
async function onSubmit(event: FormSubmitEvent<any>) {
  // Do something with data
  console.log(event.data)

  toast.add({ title: 'Profile updated', icon: 'i-heroicons-check-circle' })
}

onMounted(async () => {
  await settingsStore.fetchAllRagErrorSettings()
  settingsStore.fetchErrorSettings(selectedTenantId.value, selectedEnvId.value)
})

const updatedSuccess = ref({} as Record<string, boolean>)
const onUpdateErrorSettings = debounce(
  async (error_code: string, message: any) => {
    const result = await settingsStore.updateErrorSetting(
      selectedTenantId.value,
      selectedEnvId.value,
      error_code,
      message
    )
    if (result) {
      updatedSuccess.value[error_code] = true
      setTimeout(() => {
        updatedSuccess.value[error_code] = false
      }, 2000)
    }
  },
  1000
)

const errorDescriptionByCode = (code: string) => {
  const error = allRagErrorSettings.value.find(e => e.error_code === code)
  return error?.description_jp || error?.description
}

const newErrorSchema = z.object({
  error_code: z.string().min(1, 'エラーコードは必須です'),
  message: z.string().min(1, 'メッセージは必須です')
})

const newErrorState = reactive({
  error_code: '',
  message: '',
  description: '' // 説明文を保存するためのフィールドを追加
})

// 既存のエラーコードを取得
const existingErrorCodes = computed(() => {
  if (!errorSettings.value[selectedTenantId.value]?.[selectedEnvId.value]) {
    return []
  }
  return errorSettings.value[selectedTenantId.value][selectedEnvId.value].map(
    (error: any) => error.error_code
  )
})

// 追加可能なエラーコードのオプションを取得
const availableErrorCodeOptions = computed(() => {
  if (!allRagErrorSettings.value) {
    return []
  }

  return allRagErrorSettings.value
    .filter(
      (error: any) => !existingErrorCodes.value.includes(error.error_code)
    )
    .map((error: any) => ({
      value: error.error_code.toString(), // 文字列に変換
      label: error.error_code.toString(), // コードのみをラベルとして表示
      description: error.description_jp || error.description || '説明なし' // 説明文を追加
    }))
})

// 選択されたエラーコードのデフォルトメッセージと説明文を設定
const onSelectErrorCode = (code: string) => {
  // DEFAULT_ERROR_MESSAGESからデフォルトメッセージを取得
  const defaultErrorMessages = DEFAULT_ERROR_MESSAGES as Record<string, string>
  const defaultMessage = defaultErrorMessages[code]

  // 説明文を取得
  const selectedError = allRagErrorSettings.value.find(
    e => e.error_code.toString() === code
  )
  const description
    = selectedError?.description_jp || selectedError?.description || ''

  // 説明文を保存
  newErrorState.description = description

  if (defaultMessage) {
    newErrorState.message = defaultMessage
  } else {
    // デフォルトメッセージがない場合は空にする
    newErrorState.message = ''
  }
}

const onAddNewError = () => {
  newErrorState.error_code = ''
  newErrorState.message = ''
  newErrorState.description = ''

  // 追加可能なエラーコードがない場合は通知
  if (availableErrorCodeOptions.value.length === 0) {
    toast.add({
      title: '情報',
      description: '追加可能なエラーコードがありません',
      color: 'blue'
    })
    return
  }

  isNewErrorModalOpen.value = true
}

const onCreateError = async (event: FormSubmitEvent<any>) => {
  // Ensure error_code is a string
  const error_code
    = typeof event.data.error_code === 'object' && event.data.error_code !== null
      ? event.data.error_code.value.toString()
      : event.data.error_code.toString()

  const result = await settingsStore.createErrorSetting(
    selectedTenantId.value,
    selectedEnvId.value,
    error_code,
    event.data.message
  )

  if (result) {
    // Refresh error settings and descriptions
    await settingsStore.fetchAllRagErrorSettings()

    toast.add({
      title: '成功',
      description: 'エラーコードとメッセージを追加しました',
      color: 'green'
    })
    isNewErrorModalOpen.value = false
    newErrorState.error_code = ''
    newErrorState.message = ''
  } else {
    toast.add({
      title: 'エラー',
      description: 'エラーコードとメッセージの追加に失敗しました',
      color: 'red'
    })
  }
}

const onDeleteError = (error_code: string) => {
  confirm.show({
    title: 'エラーコード削除の確認',
    description: `エラーコード「${error_code}」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      const result = await settingsStore.deleteErrorSetting(
        selectedTenantId.value,
        selectedEnvId.value,
        error_code
      )

      if (result) {
        // Refresh error settings and descriptions
        await settingsStore.fetchAllRagErrorSettings()

        toast.add({
          title: '成功',
          description: 'エラーコードとメッセージを削除しました',
          color: 'green'
        })
      } else {
        toast.add({
          title: 'エラー',
          description: 'エラーコードとメッセージの削除に失敗しました',
          color: 'red'
        })
      }
    }
  })
}

const onResetErrors = () => {
  confirm.show({
    title: 'エラーコードリセットの確認',
    description:
      'すべてのエラーコードとメッセージをデフォルト設定にリセットしますか？',
    confirmText: 'リセット',
    onConfirm: async () => {
      const result = await settingsStore.resetErrorSettings(
        selectedTenantId.value,
        selectedEnvId.value
      )

      if (result) {
        toast.add({
          title: '成功',
          description: 'エラーコードとメッセージをリセットしました',
          color: 'green'
        })
      } else {
        toast.add({
          title: 'エラー',
          description: 'エラーコードとメッセージのリセットに失敗しました',
          color: 'red'
        })
      }
    }
  })
}
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <UForm
      v-if="errorSettings[selectedTenantId]?.[selectedEnvId]"
      :state="state"
      :validate="validate"
      :validate-on="['submit']"
      class="space-y-4"
      @submit="onSubmit"
    >
      <UDashboardSection
        data-tour="error-message-settings"
        title="エラーメッセージ設定"
        description="エラー発生時に表示されるメッセージの設定を行います。"
      >
        <template #links>
          <div
            v-if="!isSelectedEnvIsProd"
            class="flex gap-2"
          >
            <UButton
              data-tour="error_message_reset"
              label="デフォルト設定にリセット"
              icon="i-heroicons-arrow-path"
              color="gray"
              size="sm"
              variant="ghost"
              @click="onResetErrors"
            />
            <UButton
              data-tour="error_message_create"
              label="新規エラーコード追加"
              icon="formkit:add"
              color="gray"
              size="sm"
              @click="onAddNewError"
            />
          </div>
        </template>

        <UFormGroup
          v-for="row in errorSettings[selectedTenantId][selectedEnvId]?.sort(
            (a: any, b: any) => a.error_code - b.error_code
          )"
          :key="row?.error_code"
          data-tour="settings-error-messages"
          :label="`エラーコード: ${row.error_code}`"
          :class="classNames.formGroup"
          :ui="ui.formGroup"
          class="!items-baseline"
          :description="errorDescriptionByCode(row.error_code)"
        >
          <div class="flex flex-col w-full">
            <BaseMessageEditor
              :key="row?.error_code"
              v-model="row.message"
              data-tour="error-message-editor"
              :updated-success="updatedSuccess[row.error_code]"
              :loading="loadings.updateErrorSetting[row.error_code]"
              :disabled="isSelectedEnvIsProd"
              @update:model-value="
                onUpdateErrorSettings(row.error_code, $event)
              "
            />
            <div
              v-if="!isSelectedEnvIsProd"
              class="flex justify-end mt-2"
            >
              <UButton
                data-tour="delete-error-message"
                icon="i-heroicons-trash"
                color="red"
                variant="ghost"
                size="xs"
                @click="onDeleteError(row.error_code)"
              />
            </div>
          </div>
        </UFormGroup>
      </UDashboardSection>
    </UForm>

    <!-- 新規エラーコード追加モーダル -->
    <UDashboardModal
      v-model="isNewErrorModalOpen"
      title="新規エラーコード追加"
      description="新しいエラーコードとメッセージを追加します"
      data-tour="error-message-create-modal"
    >
      <UForm
        :schema="newErrorSchema"
        :state="newErrorState"
        class="space-y-4"
        @submit="onCreateError"
      >
        <UFormGroup
          label="エラーコード"
          name="error_code"
          required
        >
          <USelectMenu
            v-model="newErrorState.error_code"
            data-tour="error_code_list"
            :options="availableErrorCodeOptions"
            placeholder="エラーコードを選択してください"
            searchable
            searchable-placeholder="エラーコードを検索"
            option-attribute="value"
            @update:model-value="
              (val) => {
                // Ensure we're setting a string value
                if (typeof val === 'object' && val !== null) {
                  newErrorState.error_code = val.value.toString();
                  onSelectErrorCode(val.value.toString());
                }
                else if (val !== null) {
                  newErrorState.error_code = val.toString();
                  onSelectErrorCode(val.toString());
                }
              }
            "
          >
            <template #option="{ option }">
              <div class="flex flex-col">
                <div class="font-medium">
                  {{ option.label }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ option.description }}
                </div>
              </div>
            </template>
            <template #selected-option="{ option }">
              <div class="flex flex-col">
                <div class="font-medium">
                  {{ option.label }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ option.description }}
                </div>
              </div>
            </template>
          </USelectMenu>
        </UFormGroup>

        <UFormGroup
          data-tour="error-message-input"
          label="エラーメッセージ"
          name="message"
          required
        >
          <BaseMessageEditor
            v-model="newErrorState.message"
            placeholder="エラー時に表示するメッセージ"
            :disabled="false"
          />
        </UFormGroup>

        <div class="flex justify-end gap-3 mt-4">
          <UButton
            label="キャンセル"
            color="gray"
            variant="ghost"
            @click="isNewErrorModalOpen = false"
          />
          <UButton
            data-tour="error-message-create"
            type="submit"
            label="追加"
            color="primary"
            :loading="loadings.createErrorSetting"
          />
        </div>
      </UForm>
    </UDashboardModal>
  </UDashboardPanelContent>
</template>

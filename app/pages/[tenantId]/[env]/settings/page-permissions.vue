<script setup lang="ts">
// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_admin_settings', 'view_users'] // Allow both operators and admins
})

const { selectedTenantId, selectedEnvId } = useApp()
const pagePermissionsStore = usePagePermissionsStore()
const { selectedRole, roleOptions } = storeToRefs(pagePermissionsStore)

// Initialize page permissions
onMounted(() => {
  pagePermissionsStore.initializePagePermissions()
})
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar class="w-full">
        <template #title>
          <div class="flex items-center justify-between w-full">
            <h2
              class="flex w-full items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
            >
              ページ権限管理
            </h2>
          </div>
        </template>
        <!-- <template #right>
          <USelect
            v-model="selectedRole"
            :options="roleOptions"
            placeholder="ロールを選択"
            class="w-40 mr-2"
          />
        </template> -->
      </UDashboardNavbar>

      <UDashboardPanelContent>
        <PagePermissionManager />
      </UDashboardPanelContent>
    </UDashboardPanel>
  </UDashboardPage>
</template>

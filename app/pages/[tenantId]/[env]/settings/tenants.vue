<script setup lang="ts">
// Define required permissions for this page
import type {
  TenantWithEnvironments,
  CreateTenantPayload,
  UpdateTenantPayload
} from '~/types/tenant'

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_tenants']
})

const isFormOpen = ref(false)
const isCreationProcessOpen = ref(false)

const toast = useToast()
const tenantsStore = useTenantsStore()
const environmentsStore = useEnvironmentsStore()
const { loadings, tenantsWithEnvironments } = storeToRefs(tenantsStore)
const { environments } = storeToRefs(environmentsStore)
const confirm = useConfirm()

onMounted(() => {
  tenantsStore.fetchTenants()
})

const newTenant = ref<CreateTenantPayload | null>(null)
const onSubmit = (data: CreateTenantPayload | UpdateTenantPayload) => {
  if (selectedTenant.value) {
    tenantsStore.updateTenant(
      selectedTenant.value.id,
      data as UpdateTenantPayload
    )
  } else {
    // tenantsStore.createTenant(data)
    newTenant.value = data as CreateTenantPayload
    isCreationProcessOpen.value = true
  }
  isFormOpen.value = false
}

const selectedTenant = ref<TenantWithEnvironments | null>(null)

const onCreate = () => {
  selectedTenant.value = null
  isFormOpen.value = true
}

const onUpdate = (tenant: TenantWithEnvironments) => {
  selectedTenant.value = tenant
  isFormOpen.value = true
}

const onDelete = async (tenant: TenantWithEnvironments) => {
  // First fetch all tenants
  await environmentsStore.fetchAllEnvs(tenant.id)
  confirm.show({
    title: `テナント削除の確認`,
    description: `テナント「${
      tenant.description || tenant.id
    }」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      let isDeleteSuccessfully = true
      if (environments.value[tenant.id]?.length > 0) {
        // Delete all existing environments
        for (const env of environments.value[tenant.id]) {
          isDeleteSuccessfully = await environmentsStore.deleteEnvironment(tenant.id, env.id)
          if (!isDeleteSuccessfully) break
        }
      }
      // All environments are deleted successfully
      if (isDeleteSuccessfully) {
        isDeleteSuccessfully = await tenantsStore.deleteTenant(tenant.id)
      }
      if (isDeleteSuccessfully) {
        toast.add({
          title: '削除しました',
          description: 'テナントを削除しました',
          color: 'green'
        })
      }
    }
  })
}
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <UDashboardSection
      title="テナント一覧"
      description="テナントの一覧を表示します。"
    >
      <template #links>
        <UButton
          label="新規作成"
          color="black"
          @click="onCreate"
        />
      </template>
      <!-- ~/components/settings/MembersList.vue -->
      <TenantsList
        :tenants="tenantsWithEnvironments"
        @edit="onUpdate"
        @delete="onDelete"
      />
    </UDashboardSection>

    <UDashboardModal
      v-model="isFormOpen"
      :title="selectedTenant ? 'テナントを編集' : '新規テナント作成'"
      :description="
        selectedTenant
          ? 'テナントの情報を編集します。'
          : '新しいテナントを作成します。'
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <!-- ~/components/settings/MembersForm.vue -->
      <TenantsForm
        :loading="loadings.createTenant || loadings.updateTenant"
        v-bind="selectedTenant"
        @close="isFormOpen = false"
        @submit="onSubmit"
      />
    </UDashboardModal>

    <UDashboardModal
      v-model="isCreationProcessOpen"
      title="新規テナント作成中"
      description="
        テナントを作成しています。しばらくお待ちください。
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <!-- ~/components/settings/MembersForm.vue -->
      <TenantsCreationProcess
        v-bind="newTenant"
        @close="isCreationProcessOpen = false"
      />
    </UDashboardModal>
  </UDashboardPanelContent>
</template>

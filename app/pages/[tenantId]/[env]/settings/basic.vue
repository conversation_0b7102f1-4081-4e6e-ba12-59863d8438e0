<script setup lang="ts">
import type { FormError, FormSubmitEvent } from '#ui/types'

const fileRef = ref<HTMLInputElement>()

const state = reactive({
  name: '',
  avatar: '',
  bio: '',
  password_current: '',
  password_new: ''
})

const toast = useToast()

function validate(state: any): FormError[] {
  const errors = []
  if (!state.name)
    errors.push({ path: 'name', message: 'Please enter your name.' })
  if (!state.email)
    errors.push({ path: 'email', message: 'Please enter your email.' })
  if (
    (state.password_current && !state.password_new)
    || (!state.password_current && state.password_new)
  )
    errors.push({
      path: 'password',
      message: 'Please enter a valid password.'
    })
  return errors
}

function onFileChange(e: Event) {
  const input = e.target as HTMLInputElement

  if (!input.files?.length) {
    return
  }

  state.avatar = URL.createObjectURL(input.files[0])
}

function onFileClick() {
  fileRef.value?.click()
}

async function onSubmit(event: FormSubmitEvent<any>) {
  // Do something with data
  console.log(event.data)

  toast.add({ title: 'Profile updated', icon: 'i-heroicons-check-circle' })
}

const defaultErrors = {
  1001: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？',
  1002: '会話が長くなってきましたね。いったんリセットしますので、再度お尋ねしてもらえますか？',
  8001: 'すみませんが、その質問は答えづらいですね。他におたずねはありますか？',
  8002: '一度にたくさん質問されると説明しづらいです。一つずつ聞いてもらえますか？',
  8003: 'すみませんが、その質問は答えづらいですね。他におたずねはありますか？',
  8004: 'すみませんが、その質問は答えづらいですね。他におたずねはありますか？',
  9990: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？',
  9991: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？',
  9992: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？',
  9994: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？',
  9998: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？',
  9999: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？'
}
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <UForm
      :state="state"
      :validate="validate"
      :validate-on="['submit']"
      @submit="onSubmit"
    >
      <UDashboardSection
        title="チャットボットの設定"
        description="チャットボットの基本設定を行います。"
      >
        <UFormGroup
          name="name"
          label="チャットボット名"
          description="チャットボットのヘッダーに表示される名前を設定します。"
          required
          class="grid grid-cols-2 gap-2 items-center"
          :ui="{ container: '' }"
        >
          <UInput
            v-model="state.name"
            autocomplete="off"
            icon="fluent:bot-16-filled"
            size="md"
          />
        </UFormGroup>

        <!-- <UFormGroup
          name="name"
          label="チャットボットアバター"
          description="チャットボットのアバター画像を設定します。"
          class="grid grid-cols-2 gap-2 items-center"
          :ui="{ container: '' }"
        >
          <UInput
            v-model="state.name"
            autocomplete="off"
            icon="fluent:bot-16-filled"
            size="md"
          />
        </UFormGroup> -->
      </UDashboardSection>
    </UForm>
    <UDivider class="mb-4" />

    <UDashboardSection
      title="テーマ"
      description="アプリの外観と操作性をカスタマイズします。"
    >
      <template #links>
        <UColorModeSelect color="gray" />
      </template>
    </UDashboardSection>
  </UDashboardPanelContent>
</template>

<script setup lang="ts">
// Define required permissions for this page
import { debounce } from 'lodash'
import type { FormError, FormSubmitEvent } from '#ui/types'
import { SUPPORTED_LANGUAGES } from '~/constants/supported-languages'

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_basic_settings']
})

const appConfig = useAppConfig()

const ui = {
  formGroup: { container: 'col-span-7', inner: 'col-span-5' }
}

const classNames = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}
const settingsStore = useSettingsStore()
const { basicSettings, supportLanguages, customSettings, loadings } = storeToRefs(
  settingsStore
)
const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()

const toast = useToast()

// Add loading state for the entire settings page
const isLoading = ref(false)

function validate(basicSettings: any): FormError[] {
  const errors = []
  if (!basicSettings.name)
    errors.push({ path: 'name', message: 'Please enter your name.' })
  if (!basicSettings.email)
    errors.push({ path: 'email', message: 'Please enter your email.' })
  if (
    (basicSettings.password_current && !basicSettings.password_new)
    || (!basicSettings.password_current && basicSettings.password_new)
  )
    errors.push({
      path: 'password',
      message: 'Please enter a valid password.'
    })
  return errors
}

async function onSubmit(event: FormSubmitEvent<any>) {
  // Do something with data
  console.log(event.data)

  toast.add({ title: 'Profile updated', icon: 'i-heroicons-check-circle' })
}

const supportedLanguages = computed(() => {
  const _supportedLanguages = [SUPPORTED_LANGUAGES[0]] as any[]
  supportLanguages.value[selectedTenantId.value]?.[selectedEnvId.value]?.forEach((row) => {
    _supportedLanguages.push({
      id: row.language,
      label: row.language,
      icon: languageObject(row.language)?.icon,
      description: row.description
    })
  })
  return _supportedLanguages
})

const currentSupportedLanguages = computed(() => {
  return (
    supportLanguages.value[selectedTenantId.value]?.[selectedEnvId.value]?.map(
      row => row.language
    ) || []
  ).concat([SUPPORTED_LANGUAGES[0].id])
})

const showLanguagesPalette = ref(false)
const showWeatherLocationPalette = ref(false)

onMounted(() => {
  loadChatbotSettings()
})

// Function to load all chatbot settings
const loadChatbotSettings = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      settingsStore.fetchBasicSettings(selectedTenantId.value, selectedEnvId.value),
      settingsStore.fetchAllSupportLanguages(selectedTenantId.value, selectedEnvId.value),
      settingsStore.fetchCustomSettings(selectedTenantId.value, selectedEnvId.value),
      settingsStore.fetchWeatherSettings(selectedTenantId.value, selectedEnvId.value)
    ])
  } catch (error) {
    console.error('Error loading chatbot settings:', error)
    toast.add({
      title: 'エラー',
      description: 'チャットボット設定の読み込みに失敗しました。',
      color: 'red'
    })
  } finally {
    isLoading.value = false
  }
}

// Function to reload all chatbot settings
const reloadChatbotSettings = async () => {
  await loadChatbotSettings()
  toast.add({
    title: 'リロード完了',
    description: 'チャットボット設定を再読み込みしました。',
    color: 'green'
  })
}

const onAddSupportLanguage = (language: any) => {
  showLanguagesPalette.value = false
  settingsStore.addSupportLanguage(
    selectedTenantId.value,
    selectedEnvId.value,
    language?.id
  )
}

const onAddWeatherSetting = (location: any) => {
  showWeatherLocationPalette.value = false
  settingsStore.addWeatherSetting(selectedTenantId.value, selectedEnvId.value, location)
}

const onRemoveSupportLanguage = (language: any) => {
  settingsStore.removeSupportLanguage(
    selectedTenantId.value,
    selectedEnvId.value,
    language?.id
  )
}

const onDeleteWeatherSetting = (location: any) => {
  settingsStore.deleteWeatherSetting(
    selectedTenantId.value,
    selectedEnvId.value,
    location?.location
  )
}

const onSetDefaultLanguage = (language: any) => {
  settingsStore.setDefaultLanguage(
    selectedTenantId.value,
    selectedEnvId.value,
    language?.id
  )
}

const onCloseLanguagesPalette = () => {
  showLanguagesPalette.value = false
}

const onCloseWeathersPalette = () => {
  showWeatherLocationPalette.value = false
}

const updatedSuccess = ref({} as Record<string, boolean>)
const onUpdateCustomSettings = debounce(async (key: string, value: any) => {
  const result = await settingsStore.updateCustomSetting(
    selectedTenantId.value,
    selectedEnvId.value,
    key,
    value
  )
  if (result) {
    updatedSuccess.value[key] = true
    setTimeout(() => {
      updatedSuccess.value[key] = false
    }, 2000)
  }
}, 1000)

const onUpdateBasicSettings = debounce(async (key: string, value: any) => {
  const result = await settingsStore.updateBasicSetting(
    selectedTenantId.value,
    selectedEnvId.value,
    key,
    value
  )
  if (result) {
    updatedSuccess.value[key] = true
    setTimeout(() => {
      updatedSuccess.value[key] = false
    }, 2000)
  }
}, 1000)

const colorsOptions = ref(
  appConfig.ui.colors
    .filter(color => color !== 'primary')
    .map(color => ({
      label: color,
      id: color
    }))
)

const currentColor = computed({
  get: () =>
    colorsOptions.value.find(
      c =>
        c.id
        === customSettings.value?.[selectedTenantId.value]?.[selectedEnvId.value]
          ?.color_primary
    ) || colorsOptions.value[0],
  set: (value: any) => {
    settingsStore.updateCustomSetting(
      selectedTenantId.value,
      selectedEnvId.value,
      'color_primary',
      value.id
    )
    // appConfig.ui.primary = value.id
  }
})

const basicSettingExist = computed(
  () => !!basicSettings.value?.[selectedTenantId.value]?.[selectedEnvId.value]
)
const customSettingExist = computed(
  () => !!customSettings.value?.[selectedTenantId.value]?.[selectedEnvId.value]
)

const onInitSetting = () => {
  if (!basicSettingExist.value) {
    settingsStore.createBasicSetting(selectedTenantId.value, selectedEnvId.value, {
      name: 'のAIチャットボット',
      welcome_message: 'こんにちは！ 私はAIチャットボットです。何かお手伝いできますか？',
      survey_option_count: 2
    })
  }
  if (!customSettingExist.value) {
    settingsStore.createCustomSetting(selectedTenantId.value, selectedEnvId.value, {
      settings: {
        color_primary: 'sky'
      }
    })
  }
}
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <!-- Loading state -->
    <div
      v-if="isLoading"
      class="space-y-4"
    >
      <div class="flex items-center justify-between mb-4">
        <div>
          <USkeleton class="h-6 w-48 mb-2" />
          <USkeleton class="h-4 w-64" />
        </div>
        <USkeleton class="h-9 w-10" />
      </div>

      <div class="space-y-6">
        <UFormGroup
          v-for="field in 5"
          :key="field"
          class="!items-baseline w-full mt-10"
        >
          <template #label>
            <USkeleton class="h-4 w-32 mb-2" />
          </template>
          <template #description>
            <USkeleton class="h-3 w-48 mb-3" />
          </template>
          <USkeleton class="h-9 w-full" />
        </UFormGroup>
      </div>
    </div>

    <!-- Content when loaded -->
    <UForm
      v-else-if="basicSettingExist && customSettingExist"
      :state="basicSettings[selectedTenantId][selectedEnvId]"
      :validate="validate"
      :validate-on="['submit']"
      @submit="onSubmit"
    >
      <UDashboardSection
        title="チャットボットの設定"
        description="チャットボットの基本設定を行います。"
      >
        <template #links>
          <UButton
            icon="prime:sync"
            color="gray"
            size="sm"
            :loading="isLoading"
            @click="reloadChatbotSettings"
          />
        </template>
        <UFormGroup
          id="settings-chatbot-name"
          name="name"
          label="チャットボット名"
          description="チャットボットのヘッダーに表示される名前を設定します。"
          required
          :class="classNames.formGroup"
          :ui="ui.formGroup"
        >
          <BaseInput
            v-model="basicSettings[selectedTenantId][selectedEnvId].name"
            icon="fluent:bot-16-filled"
            :updated-success="updatedSuccess.name"
            :loading="loadings.updateBasicSetting['name'] || isLoading"
            size="md"
            :disabled="isSelectedEnvIsProd || isLoading"
            @update:model-value="onUpdateBasicSettings('name', $event)"
          />
        </UFormGroup>
        <UFormGroup
          id="settings-chatbot-avatar"
          name="avatar_url"
          label="チャットボットアバター"
          description="チャットボットのアバター画像を設定します。"
          :class="classNames.formGroup"
          :ui="ui.formGroup"
        >
          <BaseInput
            v-model="customSettings[selectedTenantId][selectedEnvId].avatar_url"
            :icon="'material-symbols:image-outline'"
            :updated-success="updatedSuccess.avatar_url"
            :loading="loadings.updateCustomSetting['avatar_url'] || isLoading"
            size="md"
            :disabled="isSelectedEnvIsProd || isLoading"
            @update:model-value="onUpdateCustomSettings('avatar_url', $event)"
          />
        </UFormGroup>
        <UFormGroup
          id="settings-chatbot-color"
          name="primaryColor"
          label="一次色"
          description="チャットボットの一次色を設定します。"
          :class="classNames.formGroup"
          :ui="ui.formGroup"
        >
          <USelectMenu
            v-model="currentColor"
            :options="colorsOptions"
            option-attribute="label"
            :disabled="isSelectedEnvIsProd || isLoading"
            :loading="isLoading"
          >
            <template #option="{ option }">
              <span
                class="h-3 w-3 rounded-full"
                :class="`bg-${option.id}-500 dark:bg-${option.id}-400`"
              />
              <span class="truncate">{{ $t(option.label) }}</span>
            </template>

            <template #label>
              <span
                class="h-3 w-3 rounded-full"
                :class="`bg-${currentColor?.id}-500 dark:bg-${currentColor?.id}-400`"
              />
              <span class="truncate">{{ $t(currentColor?.label || "") }}</span>
            </template>
          </USelectMenu>
        </UFormGroup>
        <UFormGroup
          id="settings-chatbot-supported-languages"
          name="supportedLanguages"
          label="対応言語"
          required
          :class="classNames.formGroup"
          :ui="ui.formGroup"
          class="!items-baseline"
        >
          <template #description>
            <div class="text-sm text-gray-500 dark:text-gray-400 flex flex-col gap-1">
              <div>チャットボットが対応する言語を設定します。</div>
              <div class="text-xs text-gray-500 dark:text-gray-500">
                <div>翻訳はChatGPTの翻訳機能を使用しております。</div>
                <div>翻訳の品質については調整はできませんのでご了承ください。</div>
              </div>
            </div>
          </template>
          <LanguagesList
            v-if="basicSettings[selectedTenantId] && !isLoading"
            :languages="supportedLanguages"
            :default-language="
              basicSettings[selectedTenantId][selectedEnvId].default_language
            "
            :disabled="isSelectedEnvIsProd || isLoading"
            @add="showLanguagesPalette = true"
            @delete="onRemoveSupportLanguage"
            @set-default="onSetDefaultLanguage"
          />

          <LanguagesPalette
            :show="showLanguagesPalette"
            :excludes="currentSupportedLanguages"
            @select="onAddSupportLanguage"
            @close="onCloseLanguagesPalette"
          />
        </UFormGroup>
        <UFormGroup
          id="settings-chatbot-welcome-message"
          name="name"
          label="開始時メッセージ"
          required
          :class="classNames.formGroup"
          :ui="ui.formGroup"
          class="!items-baseline"
        >
          <template #description>
            <div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                チャットボットが開始時に表示する
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                メッセージを設定します。
              </div>
            </div>
          </template>
          <BaseMessageEditor
            id="welcome-message-editor"
            v-model="basicSettings[selectedTenantId][selectedEnvId].welcome_message"
            :updated-success="updatedSuccess['welcome_message']"
            :loading="loadings.updateBasicSetting['welcome_message'] || isLoading"
            :disabled="isSelectedEnvIsProd || isLoading"
            @update:model-value="onUpdateBasicSettings('welcome_message', $event)"
          />
        </UFormGroup>

        <!-- <UFormGroup
          name="weather"
          label="天気情報"
          description="チャットボットが天気情報を取得するか設定します。"
          :class="classNames.formGroup"
          :ui="ui.formGroup"
          class="!items-baseline"
        >
          <WeathersList
            v-if="weatherSettings[selectedTenantId]"
            :locations="weatherSettings[selectedTenantId][selectedEnvId]"
            :loading="loadings.fetchWeatherSettings"
            @add="showWeatherLocationPalette = true"
            @delete="onDeleteWeatherSetting"
          />
          <WeathersPalette
            :show="showWeatherLocationPalette"
            @select="onAddWeatherSetting"
            @close="onCloseWeathersPalette"
          />
        </UFormGroup> -->
      </UDashboardSection>
    </UForm>
    <BaseEmptyList
      v-else-if="!isLoading"
      icon="fluent:bot-28-regular"
      text="チャットボットの設定がありません"
      init-button
      init-button-label="初期設定"
      @init="onInitSetting"
    />
  </UDashboardPanelContent>
</template>

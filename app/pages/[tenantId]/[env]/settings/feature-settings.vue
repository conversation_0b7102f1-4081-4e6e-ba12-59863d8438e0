<template>
  <UDashboardPanelContent class="pb-24">
    <UForm
      v-if="featureSettings?.[selectedTenantId]?.[selectedEnvId]"
      :schema="schema"
      :state="featureSettings[selectedTenantId][selectedEnvId]"

      @submit="onSubmit"
    >
      <UDashboardSection
        title="RAGの設定"
        description="チャットボットのRAG設定を行います。"
      >
        <UFormGroup
          v-for="field in editableSettingFields"
          :key="field.key"
          :label="field.label"
          :name="field.key"
          :description="field.description"
          :required="field.required"
          :class="classNames.formGroup"
          :ui="ui.formGroup"
        >
          <UToggle
            v-if="field.type == 'bool'"
            v-model="
              featureSettings[selectedTenantId][selectedEnvId][field.key]
            "
            size="md"
          />
          <UInput
            v-else
            v-model="
              featureSettings[selectedTenantId][selectedEnvId][field.key]
            "
            :type="field.type"
            size="md"
          />
        </UFormGroup>
      </UDashboardSection>

      <BasicFormButtonGroup
        cancel-label="リセット"
        :loading="loadings.updateFeatureSettings"
        @close="onCancel"
      />
    </UForm>
  </UDashboardPanelContent>
</template>

<script setup lang="ts">
// Define required permissions for this page
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_rag_settings']
})

const ui = {
  formGroup: { container: 'col-span-7', inner: 'col-span-5' }
}
const classNames = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const settingsStore = useSettingsStore()
const { featureSettings, loadings } = storeToRefs(settingsStore)
const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()

const confirm = useConfirm()
const toast = useToast()
const schema = z.object({
  max_turns: z
    .number({ message: '数値で入力してください。' })
    .int({ message: '整数で入力してください。' })
    .min(1, '1 以上で入力してください。')
    .max(100, '100 以下で入力してください。'),
  max_questions_in_query: z
    .number({ message: '数値で入力してください。' })
    .int({ message: '整数で入力してください。' })
    .min(1, '1 以上で入力してください。')
    .max(10, '10 以下で入力してください。'),
  max_accesses_per_day: z
    .number({ message: '数値で入力してください。' })
    .int({ message: '整数で入力してください。' })
    .min(0, '0 以上で入力してください。')
    .max(1000, '1000 以下で入力してください。'),
  max_sessions_per_day: z
    .number({ message: '数値で入力してください。' })
    .int({ message: '整数で入力してください。' })
    .min(0, '0 以上で入力してください。')
    .max(1000, '1000 以下で入力してください。'),
  knowledge_count: z
    .number({ message: '数値で入力してください。' })
    .int({ message: '整数で入力してください。' })
    .min(1, '1 以上で入力してください。')
    .max(10, '10 以下で入力してください。'),
  relevance_limit: z
    .number({ message: '数値で入力してください。' })
    .min(0.0, '0 以上で入力してください。')
    .max(4.0, '4 以下で入力してください。'),
  trust_relevance_limit: z
    .number({ message: '数値で入力してください。' })
    .min(0.0, '0 以上で入力してください。')
    .max(4.0, '4 以下で入力してください。')
})
const editableSettingFields = [
  {
    key: 'max_turns',
    label: '最大ターン数',
    description: '同じセッションでの最大ターン（ユーザとAIのやり取り）数。',
    required: true,
    type: 'number'
  },
  {
    key: 'max_questions_in_query',
    label: '許容質問の最大数',
    description: '同じクエリに聞ける情報の最大値。',
    required: true,
    type: 'number'
  },
  {
    key: 'max_accesses_per_day',
    label: 'ゲストのアクセス数上限',
    description: '1日あたりのゲストIPのアクセス数上限。0 は無制限と意味する。',
    required: true,
    type: 'number'
  },
  {
    key: 'max_sessions_per_day',
    label: 'ゲストのセッション数上限',
    description: '1日あたりのゲストIPのセッション数上限。0 は無制限と意味する。',
    required: true,
    type: 'number'
  },
  {
    key: 'knowledge_count',
    label: '使用ナレッジの最大数',
    description: '一つクエリで、ナレッジの使用数の上限。多ければ精度が情報量が増えるが、トークン数も同時に増えてきます。',
    required: true,
    type: 'number'
  },
  {
    key: 'relevance_limit',
    label: 'ナレッジの最低信頼スコア値',
    description: 'このスコア以上のナレッジのみ採用され、以下のものは切り捨てる。',
    required: true,
    type: 'number'
  },
  {
    key: 'trust_relevance_limit',
    label: 'ナレッジの理想信頼スコア値',
    description: 'このスコア以上のナレッジが存在すると、スコア以下のもの（最低信頼スコア値以上でも）は全部切り捨てる。',
    required: true,
    type: 'number'
  },
  {
    key: 'is_input_check_enabled',
    label: '入力クエリのブラックリスト分析の有効化',
    description: '入力クエリのチェック、又はブラックリスト分析を行うかどうかの設定。',
    required: true,
    type: 'bool'
  },
  {
    key: 'is_translate_enabled',
    label: '翻訳の有効化',
    description: 'クエリの翻訳を対応するかどうかの設定。',
    required: true,
    type: 'bool'
  },
  {
    key: 'is_web_search_enabled',
    label: 'ウェブ検索の有効化',
    description: 'ウェブ検索を対応するかどうかの設定。',
    required: true,
    type: 'bool'
  },
  {
    key: 'is_guest_allowed',
    label: 'ゲストの有効化',
    description: 'ゲストを対応するかどうかの設定。',
    required: true,
    type: 'bool'
  },
  {
    key: 'is_rag_semantic_cache_enabled',
    label: 'RAGキャッシュの有効化',
    description: 'RAGキャッシュを対応するかどうかの設定。',
    required: true,
    type: 'bool'
  }
]

type Schema = z.output<typeof schema>

onMounted(() => {
  settingsStore.fetchFeatureSettings(
    selectedTenantId.value,
    selectedEnvId.value
  )
})

const onCancel = () => {
  settingsStore.resetFeatureSettings(
    selectedTenantId.value,
    selectedEnvId.value
  )
}

async function onSubmit(event: FormSubmitEvent<Schema>) {
  confirm.show({
    title: `設定更新の確認`,
    description: `設定を更新しますか？`,
    confirmText: '更新',
    onConfirm: async () => {
      // set to null if any field is empty
      const result = await settingsStore.updateFeatureSettings(
        selectedTenantId.value,
        selectedEnvId.value,
        event.data
      )
      if (result) {
        toast.add({
          id: 'setting-update',
          title: '設定更新',
          description: '設定を更新しました。',
          color: 'green'
        })
      }
    }
  })
}
</script>

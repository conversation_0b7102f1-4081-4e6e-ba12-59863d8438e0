<template>
  <UDashboardPanelContent class="p-0">
    <BaseEmptyList
      v-if="userGroupTotalCount < 1 && !loadings.fetchUserGroups && isFilterEmpty"
      icon="lets-icons:user-scan-duotone"
      text="まだユーザグループがいません"
      init-button-label="ユーザグループを追加"
      init-button
      @init="onCreate"
    />
    <UCard
      v-else
      class="w-full"
      :ui="{
        base: '',
        ring: '',
        divide: 'divide-y divide-gray-200 dark:divide-gray-700',
        header: { padding: '!px-3 !py-3' },
        body: {
          padding: '',
          base: 'divide-y divide-gray-200 dark:divide-gray-700'
        },
        footer: { padding: 'p-4' }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h2
            class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
          >
            ユーザグループ一覧
            <UBadge :label="userGroupTotalCount" />
          </h2>
          <UButton
            label="ユーザグループ追加"
            icon="i-heroicons-plus"
            color="gray"
            size="sm"
            @click="onCreate"
          />
        </div>
      </template>

      <!-- Filters -->
      <div class="flex items-center justify-between gap-3 px-4 py-3">
        <USelectMenu
          v-model="userGroupsFilter.status"
          icon="i-heroicons-check-circle"
          placeholder="ステータス"
          class="w-36"
          multiple
          :options="defaultStatuses"
          :ui-menu="{ option: { base: 'capitalize' } }"
        >
          <template #label>
            <div v-if="userGroupsFilter.status?.length">
              {{
                userGroupsFilter.status
                  ?.map((obj: any) => obj.label)
                  .join(", ")
              }}
            </div>
            <div v-else>
              ステータス
            </div>
          </template>
        </USelectMenu>
        <UInput
          ref="input"
          v-model="userGroupsFilter.name"
          icon="i-heroicons-funnel"
          autocomplete="off"
          placeholder="ユーザグループの検索"
          class="hidden lg:block"
          @keydown.esc="$event.target.blur()"
        >
          <template #trailing>
            <UKbd value="/" />
          </template>
        </UInput>
      </div>

      <UTable
        v-model:sort="sort"
        sort-mode="manual"
        :rows="userGroups"
        :columns="columns"
        :loading="loadings.fetchUserGroups"
        class="w-full"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #name-data="{ row }">
          {{ row.name }}
        </template>

        <template #users_count-data="{ row }">
          <div
            class="max-w-52 text-xs truncate cursor-pointer hover:underline hover:text-primary"
            @click="onEditGroupUser(row)"
          >
            {{ row.users_count }}
          </div>
        </template>

        <template #created_at-data="{ row }">
          <div>
            <div>
              {{ formatDateTime(row.created_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              作成者: {{ row.created_username }} ({{
                formatDistanceStrictDateTime(row.created_at)
              }})
            </div>
          </div>
        </template>
        <template #updated_at-data="{ row }">
          <div
            v-if="row.updated_at === row.created_at"
            class="text-gray-500 dark:text-gray-500"
          >
            --
          </div>
          <div v-else>
            <div>
              {{ formatDateTime(row.updated_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              更新者: {{ row. updated_username }} ({{
                formatDistanceStrictDateTime(row.updated_at)
              }})
            </div>
          </div>
        </template>
        <template #enabled-data="{ row }">
          <div class="flex items-center gap-3 justify-between">
            <BaseStatusToggleNoConfirm
              v-model="row.enabled"
              class="capitalize flex-1 justify-center max-w-16"
              @toggle="toggleUserGroupStatus(row)"
            />

            <UDropdown
              class="group-hover:block"
              :class="{
                block: loadings.updateUserGroup,
                hidden: !loadings.updateUserGroup
              }"
              :items="rowMenus(row)"
              :popper="{ placement: 'bottom-start' }"
            >
              <UButton
                class="row-menu"
                color="white"
                icon="charm:menu-meatball"
                size="xs"
                square
                :loading="loadings.updateUserGroup"
              />
            </UDropdown>
          </div>
        </template>
      </UTable>
      <UDivider class="mt-0" />
      <UDashboardToolbar>
        <template #left>
          <div class="flex flex-wrap justify-between items-center">
            <div class="flex items-center gap-1.5">
              <span class="text-sm leading-5">表示件数:</span>

              <USelect
                v-model="userGroupsPagination.pageCount"
                :options="[5, 10, 20, 50, 100]"
                class="w-20"
                size="xs"
              />
            </div>
            <UDivider
              class="mx-3 h-full py-1"
              orientation="vertical"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-if="userGroupsPagination.pageCount < userGroupTotalCount"
            v-model="userGroupsPagination.page"
            :page-count="userGroupsPagination.pageCount"
            :total="userGroupTotalCount"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </template>
      </UDashboardToolbar>
    </UCard>

    <!-- フォーム -->
    <UDashboardModal
      v-model="isFormOpen"
      :title="selectedGroup?.id ? 'ユーザグループを編集' : '新規ユーザグループ登録'"
      :description="
        selectedGroup?.id
          ? 'ユーザグループの情報を編集します。'
          : '新しいユーザグループを作成します。'
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <UserGroupForm
        :loading="loadings.createUserGroup || loadings.updateUserGroup"
        v-bind="(selectedGroup as UserGroupBase)"
        :error="errors.createUserGroup || errors.updateUserGroup"
        @close="isFormOpen = false"
        @submit="onSubmit"
      />
    </UDashboardModal>
  </UDashboardPanelContent>
</template>

<script setup lang="ts">
// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_user_groups']
})

import { debounce } from 'lodash'
import type { UserGroup, UserGroupBase } from '~/types/user-group'

const confirm = useConfirm()
const router = useRouter()
const toast = useToast()
const userGroupsStore = useUserGroupsStore()
const { selectedTenantId, selectedEnvId } = useApp()
const {
  userGroupsFilter,
  userGroups,
  userGroupsPagination,
  userGroupTotalCount,
  selectedGroup,
  loadings,
  errors
} = storeToRefs(userGroupsStore)

const defaultColumns = [
  {
    key: 'name',
    label: 'ユーザグループ名',
    sortable: false
  },
  {
    key: 'users_count',
    label: '所属人数',
    sortable: false
  },
  {
    key: 'created_at',
    label: '登録日時',
    sortable: false
  },
  {
    key: 'updated_at',
    label: '更新日時',
    sortable: false
  },
  {
    key: 'enabled',
    label: 'ステータス',
    sortable: false,
    rowClass: 'text-center min-w-36'
  }
]

const selectedColumns = ref(defaultColumns)

const sort = computed({
  get: () => ({
    column: 'created_at',
    direction: (userGroupsPagination.value.asc ? 'asc' : 'desc') as ('asc' | 'desc')
  }),
  set: (value) => {
    userGroupsPagination.value.asc = value.direction === 'asc'
  }
})
const input = ref<{ input: HTMLInputElement }>()
const isFormOpen = ref(false)
const columns = computed(() =>
  defaultColumns.filter(column => selectedColumns.value.includes(column))
)
const defaultStatuses = [
  { label: '有効', value: 'enabled' },
  { label: '無効', value: 'disabled' }
]

const onSubmit = async (data: UserGroupBase) => {
  if (selectedGroup.value?.id) {
    const result = await userGroupsStore.updateUserGroup(
      selectedTenantId.value,
      selectedGroup.value.id,
      data
    )
    if (result) {
      isFormOpen.value = false
      debounceRefresh()
    }
  } else {
    const result = await userGroupsStore.createUserGroup(
      selectedTenantId.value,
      data
    )
    if (result) {
      isFormOpen.value = false
      await userGroupsStore.fetchUserGroups(
        selectedTenantId.value
      )
    }
  }
}
const onCreate = () => {
  selectedGroup.value = {
    name: '',
    description: '',
    enabled: true
  } as UserGroup
  isFormOpen.value = true
}
const onUpdate = (group: UserGroup) => {
  selectedGroup.value = group
  isFormOpen.value = true
}

const onEditGroupUser = (group: UserGroup) => {
  selectedGroup.value = group
  router.push(
    `/${selectedTenantId.value}/${selectedEnvId.value}/settings/user-groups/${group.id}/users`
  )
}

defineShortcuts({
  '/': () => {
    input.value?.input?.focus()
  }
})

onMounted(() => {
  userGroupsStore.fetchUserGroups(
    selectedTenantId.value
  )
})
const isFilterEmpty = computed(() => {
  return Object.values(userGroupsFilter.value).every(value => !value)
})
const refresh = () => {
  userGroupsStore.fetchUserGroups(
    selectedTenantId.value
  )
}
const debounceRefresh = debounce(refresh, 1000)

watch(
  () => userGroupsPagination.value,
  () => {
    refresh()
  },
  { deep: true, immediate: true }
)

watch(
  () => userGroupsPagination.value.pageCount,
  () => {
    userGroupsPagination.value.page = 1
  }
)

watch(
  () => userGroupsFilter,
  () => {
    debounceRefresh()
  },
  { deep: true }
)

const onDeleteUserGroup = async (row: UserGroup) => {
  confirm.show({
    title: '削除確認',
    description: 'このユーザグループを削除しますか？',
    confirmText: '削除',
    onConfirm: async () => {
      const result = await userGroupsStore.deleteUserGroup(
        selectedTenantId.value,
        row.id
      )
      if (result) {
        await userGroupsStore.fetchUserGroups(
          selectedTenantId.value
        )
        toast.add({
          title: '削除しました',
          description: 'ユーザグループを削除しました',
          color: 'green'
        })
      } else if (errors.value.deleteUserGroup.error_code === 414) {
        toast.add({
          title: '削除に失敗しました。',
          description: 'グループにユーザが所属しているため、削除はできません。',
          color: 'red'
        })
      }
    }
  })
}

const toggleUserGroupStatus = async (row: UserGroup) => {
  confirm.show({
    title: 'ステータス変更',
    description: `このユーザグループのステータスを${
      row.enabled ? '無効' : '有効'
    }に変更しますか？`,
    confirmText: '変更',
    onConfirm: async () => {
      const result = await userGroupsStore.patchUserGroup(
        selectedTenantId.value,
        row.id,
        { enabled: !row.enabled }
      )
      if (result) {
        toast.add({
          title: '成功',
          description: 'ユーザグループのステータスを変更しました',
          color: 'green'
        })
      }
    }
  })
}

const rowMenus = (row: UserGroup) => {
  return [
    [
      {
        label: 'ユーザグループ編集',
        click: () => onUpdate(row)
      },
      {
        label: '無効化/有効化',
        click: () => toggleUserGroupStatus(row)
      }
    ],
    [
      {
        label: '所属ユーザ編集',
        click: () => onEditGroupUser(row)
      }
    ],
    [
      {
        label: 'ユーザグループ削除',
        icon: 'i-heroicons-trash-20-solid',
        iconClass: 'text-red-500 dark:text-red-400',
        click: () => onDeleteUserGroup(row)
      }
    ]
  ]
}
</script>

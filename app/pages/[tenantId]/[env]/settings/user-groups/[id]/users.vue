<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar>
        <template #title>
          <UBreadcrumb :links="breadcrumb" />
          <UBadge :label="String(userGroupUsers.length ?? 0)" />
        </template>
        <template #right>
          <UButton
            label="ユーザをグループに追加"
            trailing-icon="i-heroicons-plus"
            color="gray"
            :disabled="!selectedGroup?.enabled || loadings.fetchUsersNotInGroup"
            @click="onOpenAddUsers"
          />
          <UButton
            label="選択したユーザを削除する"
            trailing-icon="i-heroicons-trash-20-solid"
            color="red"
            :disabled="selectedRemoveUsers && selectedRemoveUsers.length == 0"
            @click="onRemoveUsers"
          />
        </template>
      </UDashboardNavbar>
      <UserGroupUserList
        v-if="selectedGroup"
        v-model:selected-remove-users="selectedRemoveUsers"
        :selected-group="selectedGroup"
        :group-users="userGroupUsers"
        :loading="loadings.fetchGroupUsers"
        @remove-user="onRemoveUser"
      />
    </UDashboardPanel>

    <!-- フォーム -->
    <UDashboardModal
      v-model="isFormOpen"
      title="ユーザをグループに追加する"
      :ui="{ width: 'sm:max-w-md' }"
    >
      <USelectMenu
        v-model="selectedUsers"
        :options="targetUsers"
        multiple
        searchable
        searchable-placeholder="ユーザを検索できます。"
      >
        <template #label>
          <template v-if="selectedUsers?.length">
            <span>{{ selectedUsers.map(u => u.key).join(", ") }}</span>
          </template>
          <template v-else>
            <span class="text-gray-500 dark:text-gray-400 truncate">
              ユーザを選択・入力してください。
            </span>
          </template>
        </template>
        <template #option="{ option }">
          <div
            :class="{
              'text-primary-600': selectedUsers.includes(option)
            }"
          >
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ option.key }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">
              {{ option.label }}
            </div>
          </div>
        </template>
      </USelectMenu>
      <BasicFormButtonGroup
        class="justify-end"
        :loading="loadings.fetchUsersNotInGroup"
        @close="isFormOpen = false"
        @submit="onSubmitAddUsers"
      />
    </UDashboardModal>
  </UDashboardPage>
</template>

<script setup lang="ts">
import type { UserAccount } from '~/types/user'

const { selectedTenantId, selectedEnvId } = useApp()
const confirm = useConfirm()
const route = useRoute()
const router = useRouter()
const toast = useToast()

const userGroupsStore = useUserGroupsStore()
const { selectedGroup, userGroupUsers, usersNotInGroup, loadings, selectedRemoveUsers }
  = storeToRefs(userGroupsStore)

const isFormOpen = ref(false)
const groupId = ref(route.params.id as string)
const selectedUsers = ref([] as any[])

onMounted(() => {
  userGroupUsers.value = []
  if (!selectedGroup.value || selectedGroup.value.id !== groupId.value) {
    // Reload User Group
    const result = userGroupsStore.fetchUserGroup(
      selectedTenantId.value,
      groupId.value
    )
    if (!result) {
      // Cannot get the target group, return
      router.push(
        `/${selectedTenantId.value}/${selectedEnvId.value}/settings/user-groups`
      )
    }
  }
  userGroupsStore.fetchGroupUsers(selectedTenantId.value, groupId.value)
  userGroupsStore.fetchUsersNotInGroup(selectedTenantId.value, groupId.value)
})

const breadcrumb = computed(() => {
  return [
    {
      label: 'ユーザグループ一覧',
      to: `/${selectedTenantId.value}/${selectedEnvId.value}/settings/user-groups`
    },
    {
      label: selectedGroup?.value?.name
    },
    {
      label: '所属ユーザ一覧'
    }
  ]
})
const targetUsers = computed(() => {
  return usersNotInGroup.value
    .map(x => ({
      key: x.username,
      label: x.email
    }))
})

const onRemoveUser = async (user: UserAccount) => {
  confirm.show({
    title: 'ユーザをグループ非所属にする',
    description: `ユーザ「${user.username}」をグループ非所属に変更しますか？`,
    confirmText: '変更',
    onConfirm: async () => {
      const result = await userGroupsStore.removeUserFromGroup(
        selectedTenantId.value,
        groupId.value,
        user.username
      )
      await userGroupsStore.fetchGroupUsers(
        selectedTenantId.value,
        groupId.value
      )
      if (result) {
        toast.add({
          title: '成功',
          description: `ユーザ「${user.username}」をグループ非所属に変更しました`,
          color: 'green'
        })
      }
    }
  })
}

const onRemoveUsers = async () => {
  if (selectedRemoveUsers.value.length < 1) return

  confirm.show({
    title: '選択したユーザをグループ非所属にする',
    description: `選択したユーザをグループ非所属に変更しますか？`,
    confirmText: '変更',
    onConfirm: async () => {
      const result = await userGroupsStore.removeUsersFromGroup(
        selectedTenantId.value,
        groupId.value,
        selectedRemoveUsers.value.map(user => user.username)
      )
      selectedRemoveUsers.value = []
      await userGroupsStore.fetchGroupUsers(
        selectedTenantId.value,
        groupId.value
      )
      userGroupsStore.fetchUsersNotInGroup(selectedTenantId.value, groupId.value)
      if (result) {
        toast.add({
          title: '成功',
          description: `選択したユーザをグループ非所属に変更しました`,
          color: 'green'
        })
      }
    }
  })
}

const onOpenAddUsers = async () => {
  selectedUsers.value = []
  isFormOpen.value = true
}

const onSubmitAddUsers = () => {
  if (selectedUsers.value.length > 0) {
    const selectedUsername = selectedUsers.value.map(x => x.key)
    confirm.show({
      title: 'ユーザをグループ所属にする',
      description: `選択したユーザをグループ所属に変更しますか？`,
      confirmText: '変更',
      onConfirm: async () => {
        const result = await userGroupsStore.addUsersToGroup(
          selectedTenantId.value,
          groupId.value,
          selectedUsername
        )
        await userGroupsStore.fetchGroupUsers(
          selectedTenantId.value,
          groupId.value
        )
        userGroupsStore.fetchUsersNotInGroup(selectedTenantId.value, groupId.value)
        if (result) {
          toast.add({
            title: '成功',
            description: `選択したユーザをグループ所属に変更しました`,
            color: 'green'
          })
        }
      }
    })
  }
  isFormOpen.value = false
}
</script>

<script setup lang="ts">
// Get route params for navigation
const route = useRoute()
const selectedTenantId = computed(() => route.params.tenantId as string)
const selectedEnvId = computed(() => route.params.env as string)

// FAQ data organized by categories
const faqData = [
  {
    label: '基本的な使い方',
    icon: 'material-symbols:help-outline',
    content: [
      {
        label: 'チャットボットの設定方法を教えてください',
        content:
          '設定 > 基本設定 > チャットボット設定から、チャットボットの名前、説明、アバター、色などを設定できます。変更後は「保存」ボタンをクリックしてください。'
      },
      {
        label: 'データソースを追加するにはどうすればよいですか？',
        content:
          'データソース > データソース登録から、ファイル、ウェブサイト、Q&A形式でデータを追加できます。ファイルの場合は、PDF、Word、テキストファイルなどをアップロードしてください。'
      },
      {
        label: 'ナレッジが反映されないのはなぜですか？',
        content:
          'データソースを追加した後、インデックス化処理が必要です。データソース一覧で「インデックス化」ボタンをクリックし、処理が完了するまでお待ちください。通常5-10分程度かかります。'
      },
      {
        label: 'チャットボットをウェブサイトに埋め込むにはどうすればよいですか？',
        content:
          'コネクト > 埋め込みから、提供されるスクリプトタグをコピーし、ウェブサイトの</body>タグの直前に貼り付けてください。本番環境と検証環境で異なるスクリプトが提供されます。'
      }
    ]
  },
  {
    label: '統計・分析',
    icon: 'material-symbols:bar-chart',
    content: [
      {
        label: '質問数や回答率はどこで確認できますか？',
        content:
          'ダッシュボードで基本的な統計情報を確認できます。詳細な分析は統計メニューの各項目（回答後のアンケート、未回答の質問など）で確認してください。'
      },
      {
        label: 'ユーザーの質問履歴を見ることはできますか？',
        content:
          'ログ情報メニューから、すべての会話履歴を確認できます。セッションID、カテゴリ、日時でフィルタリングして検索することも可能です。'
      },
      {
        label: '回答できなかった質問を改善するにはどうすればよいですか？',
        content:
          '統計 > 未回答の質問から、回答できなかった質問を確認し、関連するナレッジデータを追加してください。また、プロンプト設定で回答精度を調整することも可能です。'
      }
    ]
  },
  {
    label: 'ユーザー管理・権限',
    icon: 'material-symbols:admin-panel-settings',
    content: [
      {
        label: '新しいユーザーを追加するにはどうすればよいですか？',
        content:
          '設定 > 管理者設定 > ユーザ管理から「新規ユーザー追加」ボタンをクリックし、ユーザー情報を入力してください。初期パスワードは自動生成され、メールで通知されます。'
      },
      {
        label: 'ユーザーの権限を変更したいです',
        content:
          'ユーザ管理画面でユーザーを選択し、役割（管理者、編集者、閲覧者）を変更できます。また、ユーザーグループ機能を使用して、複数のユーザーに一括で権限を設定することも可能です。'
      },
      {
        label: '特定のページへのアクセスを制限したいです',
        content:
          '設定 > 管理者設定 > ページ権限管理から、各ページやメニューへのアクセス権限をユーザーまたはユーザーグループ単位で設定できます。'
      }
    ]
  }
]

// Search functionality
const searchQuery = ref('')
const selectedCategory = ref('')

const categoryOptions = computed(() => [
  { label: 'すべて', value: '' },
  ...faqData.map(category => ({
    label: category.label,
    value: category.label
  }))
])

const filteredFAQ = computed(() => {
  let filtered = faqData

  // Filter by category
  if (selectedCategory.value) {
    filtered = filtered.filter(category => category.label === selectedCategory.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered
      .map(category => ({
        ...category,
        content: category.content.filter(
          item =>
            item.label.toLowerCase().includes(query)
            || item.content.toLowerCase().includes(query)
        )
      }))
      .filter(category => category.content.length > 0)
  }

  return filtered
})

const totalQuestionsCount = computed(() => {
  return faqData.reduce((total, category) => total + category.content.length, 0)
})

const filteredQuestionsCount = computed(() => {
  return filteredFAQ.value.reduce(
    (total, category) => total + category.content.length,
    0
  )
})
</script>

<template>
  <UDashboardPanelContent class="p-6">
    <div class="w-full mx-auto space-y-8">
      <!-- Header -->
      <div class="text-center">
        <UIcon
          name="material-symbols:quiz-outline"
          class="w-16 h-16 text-primary-500 mx-auto mb-4"
        />
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          よくある質問
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          システムの使い方でお困りの際は、まずこちらをご確認ください
        </p>

        <!-- Stats -->
        <div class="flex justify-center space-x-8 mb-8">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-500">
              {{ totalQuestionsCount }}
            </div>
            <div class="text-sm text-gray-500">
              質問項目
            </div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-500">
              {{ faqData.length }}
            </div>
            <div class="text-sm text-gray-500">
              カテゴリ
            </div>
          </div>
        </div>
      </div>

      <!-- Search and Filter -->
      <UCard>
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1">
            <UInput
              v-model="searchQuery"
              icon="i-heroicons-magnifying-glass"
              placeholder="質問を検索..."
              size="lg"
            />
          </div>
          <div class="w-full md:w-48">
            <USelectMenu
              v-model="selectedCategory"
              :options="categoryOptions"
              placeholder="カテゴリ選択"
              size="lg"
              value-attribute="value"
              option-attribute="label"
            />
          </div>
        </div>

        <div
          v-if="searchQuery || selectedCategory"
          class="mt-4 text-sm text-gray-600 dark:text-gray-400"
        >
          {{ filteredQuestionsCount }}件の質問が見つかりました
          <UButton
            v-if="searchQuery || selectedCategory"
            size="xs"
            color="gray"
            variant="ghost"
            @click="
              searchQuery = '';
              selectedCategory = '';
            "
          >
            クリア
          </UButton>
        </div>
      </UCard>

      <!-- FAQ Content -->
      <div
        v-if="filteredFAQ.length > 0"
        class="space-y-8"
      >
        <div
          v-for="category in filteredFAQ"
          :key="category.label"
          class="space-y-4"
        >
          <!-- Category Header -->
          <div class="flex items-center space-x-3 mb-6">
            <UIcon
              :name="category.icon"
              class="w-6 h-6 text-primary-500"
            />
            <h2 class="text-md font-semibold text-gray-900 dark:text-white">
              {{ category.label }}
            </h2>
            <UBadge
              :label="category.content.length.toString()"
              color="primary"
              variant="soft"
            />
          </div>

          <!-- FAQ Items using ULandingFAQ -->
          <ULandingFAQ
            :items="category.content"
            multiple
            :ui="{
              item: {
                base: 'border border-gray-200 dark:border-gray-700',
                padding: 'p-4',
                rounded: 'rounded-lg'
              },
              button: {
                base: 'flex items-center justify-between w-full text-left',
                font: 'font-medium'
              },
              content: {
                base: 'text-gray-600 dark:text-gray-300 mt-3'
              }
            }"
          />
        </div>
      </div>

      <!-- No Results -->
      <div
        v-else
        class="text-center py-12"
      >
        <UIcon
          name="i-heroicons-magnifying-glass"
          class="w-12 h-12 text-gray-400 mx-auto mb-4"
        />
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">
          検索結果が見つかりませんでした
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          検索キーワードを変更するか、カテゴリ選択をリセットしてください
        </p>
        <UButton
          color="primary"
          variant="outline"
          @click="
            searchQuery = '';
            selectedCategory = '';
          "
        >
          検索条件をリセット
        </UButton>
      </div>

      <!-- Contact Support -->
      <UCard
        class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 border-primary-200 dark:border-primary-800"
      >
        <div class="text-center">
          <UIcon
            name="material-symbols:contact-support-outline"
            class="w-12 h-12 text-primary-500 mx-auto mb-4"
          />
          <h3 class="text-md font-semibold text-gray-900 dark:text-white mb-2">
            解決しない場合は
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            FAQで解決しない問題については、お気軽にお問い合わせください
          </p>
          <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <UButton
              :to="`/${selectedTenantId}/${selectedEnvId}/support/contact`"
              color="primary"
              icon="material-symbols:contact-support-outline"
            >
              お問い合わせ
            </UButton>
            <UButton
              :to="`/${selectedTenantId}/${selectedEnvId}/support/tours`"
              color="gray"
              icon="streamline:manual-book-remix"
            >
              ガイドツアー
            </UButton>
            <UButton
              href="https://playnext-lab.notion.site/1f384d393a9c805b8944fce3560616f9"
              target="_blank"
              color="gray"
              icon="material-symbols:menu-book-outline"
            >
              操作マニュアル
            </UButton>
          </div>
        </div>
      </UCard>
    </div>
  </UDashboardPanelContent>
</template>

<script setup lang="ts">
const form = ref({
  name: "",
  email: "",
  subject: "",
  message: "",
  priority: "medium",
});

const isSubmitting = ref(false);
const toast = useToast();

const priorityOptions = [
  { label: "低", value: "low", color: "green" },
  { label: "中", value: "medium", color: "yellow" },
  { label: "高", value: "high", color: "orange" },
  { label: "緊急", value: "urgent", color: "red" },
];

const contactMethods = [
  {
    icon: "fluent-emoji:e-mail",
    title: "メールサポート",
    description: "一般的なお問い合わせやご質問",
    contact: "<EMAIL>",
    response: "24時間以内に回答",
  },
  {
    icon: "streamline-ultimate-color:phone-retro-1",
    title: "電話サポート",
    description: "緊急時や詳細なサポートが必要な場合",
    contact: "03-xxxx-xxxx",
    response: "平日 9:00-18:00",
  },
  {
    icon: "devicon:slack",
    title: "Slackサポート",
    description: "リアルタイムでのコミュニケーション",
    contact: "#support-channel",
    response: "営業時間内は即座に対応",
  },
];

const onSubmit = async () => {
  isSubmitting.value = true;

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    toast.add({
      title: "お問い合わせを送信しました",
      description: "担当者より回答いたします。しばらくお待ちください。",
      icon: "i-heroicons-check-circle",
      color: "green",
    });

    // Reset form
    form.value = {
      name: "",
      email: "",
      subject: "",
      message: "",
      priority: "medium",
    };
  } catch (error) {
    toast.add({
      title: "送信に失敗しました",
      description: "しばらく後に再度お試しください。",
      icon: "i-heroicons-exclamation-circle",
      color: "red",
    });
  } finally {
    isSubmitting.value = false;
  }
};

const validate = (state: any) => {
  const errors = [];
  if (!state.name) errors.push({ path: "name", message: "お名前は必須です" });
  if (!state.email) errors.push({ path: "email", message: "メールアドレスは必須です" });
  if (!state.subject) errors.push({ path: "subject", message: "件名は必須です" });
  if (!state.message)
    errors.push({ path: "message", message: "お問い合わせ内容は必須です" });
  return errors;
};
</script>

<template>
  <UDashboardPanelContent class="p-6">
    <div class="max-w-4xl mx-auto space-y-8">
      <div class="text-center">
        <UIcon
          name="material-symbols:contact-support-outline"
          class="w-16 h-16 text-primary-500 mx-auto mb-4"
        />
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          お問い合わせ
        </h2>
        <p class="text-gray-600 dark:text-gray-400">
          ご質問やサポートが必要でしたら、お気軽にお問い合わせください。
        </p>
      </div>

      <!-- Contact Methods -->
      <div class="grid md:grid-cols-3 gap-6 mb-8">
        <UCard
          v-for="method in contactMethods"
          :key="method.title"
          class="text-center hover:shadow-lg transition-shadow"
        >
          <template #header>
            <div class="text-center">
              <UIcon :name="method.icon" class="w-8 h-8 text-primary-500 mx-auto mb-2" />
              <h3 class="font-semibold text-gray-900 dark:text-white">
                {{ method.title }}
              </h3>
            </div>
          </template>

          <div class="space-y-3">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ method.description }}
            </p>
            <div class="space-y-1">
              <p class="font-medium text-gray-900 dark:text-white">
                {{ method.contact }}
              </p>
              <p class="text-xs text-gray-500">
                {{ method.response }}
              </p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- FAQ Banner -->
      <UCard
        class="bg-gradient-to-r from-primary-50 to-primary-50 dark:from-primary-900/20 dark:to-primary-900/20 border-primary-200 dark:border-primary-800"
      >
        <div class="text-center">
          <UIcon
            name="material-symbols:quiz-outline"
            class="w-12 h-12 text-primary-500 mx-auto mb-4"
          />
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            お問い合わせ前によくある質問をご確認ください
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            多くの疑問は FAQ
            で解決できます。お時間の節約にもなりますので、まずはこちらをご確認ください。
          </p>
          <UButton
            :to="`/${$route.params.tenantId}/${$route.params.env}/support/faq`"
            color="primary"
            icon="material-symbols:quiz-outline"
            size="lg"
          >
            よくある質問を見る
          </UButton>
        </div>
      </UCard>

      <!-- Contact Form -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            お問い合わせフォーム
          </h3>
        </template>

        <UForm :state="form" :validate="validate" @submit="onSubmit" class="space-y-6">
          <div class="grid md:grid-cols-2 gap-4">
            <UFormGroup label="お名前" name="name" required>
              <UInput v-model="form.name" placeholder="山田太郎" />
            </UFormGroup>

            <UFormGroup label="メールアドレス" name="email" required>
              <UInput
                v-model="form.email"
                type="email"
                placeholder="<EMAIL>"
              />
            </UFormGroup>
          </div>

          <div class="grid md:grid-cols-2 gap-4">
            <UFormGroup label="件名" name="subject" required>
              <UInput v-model="form.subject" placeholder="お問い合わせの件名" />
            </UFormGroup>

            <UFormGroup label="優先度" name="priority">
              <USelectMenu
                v-model="form.priority"
                :options="priorityOptions"
                option-attribute="label"
                value-attribute="value"
              >
                <template #option="{ option }">
                  <div class="flex items-center space-x-2">
                    <UBadge :color="option.color" variant="subtle" size="xs" />
                    <span>{{ option.label }}</span>
                  </div>
                </template>

                <template #label>
                  <div class="flex items-center space-x-2">
                    <UBadge
                      :color="priorityOptions.find(p => p.value === form.priority)?.color"
                      variant="subtle"
                      size="xs"
                    />
                    <span>{{
                      priorityOptions.find(p => p.value === form.priority)?.label
                    }}</span>
                  </div>
                </template>
              </USelectMenu>
            </UFormGroup>
          </div>

          <UFormGroup label="お問い合わせ内容" name="message" required>
            <UTextarea
              v-model="form.message"
              :rows="6"
              placeholder="お問い合わせの詳細をご記入ください..."
            />
          </UFormGroup>

          <div class="flex justify-end">
            <UButton
              type="submit"
              icon="i-heroicons-paper-airplane"
              :loading="isSubmitting"
              :disabled="isSubmitting"
            >
              送信する
            </UButton>
          </div>
        </UForm>
      </UCard>
    </div>
  </UDashboardPanelContent>
</template>

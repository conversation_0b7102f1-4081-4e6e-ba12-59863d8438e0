<script setup lang="ts">
const route = useRoute()
const router = useRouter()

// Auto redirect to tours page since manual opens externally
onMounted(() => {
  // Navigate to tours page
  router.push(`/${route.params.tenantId}/${route.params.env}/support/tours`)
})
</script>

<template>
  <UDashboardPanelContent class="p-8">
    <div class="flex flex-col items-center justify-center min-h-96 space-y-4">
      <UIcon
        name="material-symbols:menu-book-outline"
        class="w-16 h-16 text-gray-400 dark:text-gray-500"
      />
      <div class="text-center">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          ヘルプ＆サポート
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ガイドツアーページに移動しています...
        </p>
        <div class="flex flex-wrap gap-3 justify-center">
          <UButton
            icon="streamline:manual-book-remix"
            label="ガイドツアー"
            color="primary"
            @click="router.push(`/${route.params.tenantId}/${route.params.env}/support/tours`)"
          />
          <UButton
            icon="material-symbols:quiz-outline"
            label="よくある質問"
            color="blue"
            @click="router.push(`/${route.params.tenantId}/${route.params.env}/support/faq`)"
          />
          <UButton
            icon="i-heroicons-arrow-top-right-on-square"
            label="操作マニュアル"
            color="gray"
            @click="window.open('https://playnext-lab.notion.site/1f384d393a9c805b8944fce3560616f9', '_blank')"
          />
        </div>
      </div>
    </div>
  </UDashboardPanelContent>
</template>

<script setup lang="ts">
import markdownit from 'markdown-it'

const md = markdownit()
const { selectedTenantId, selectedEnvId } = useApp()

const uiClass = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const ui = {
  formGroup: {
    container: 'col-span-7',
    inner: 'col-span-5'
  }
}

const trainingDatasStore = useTrainingDatasStore()

const { trainingDataNew, selectedKnowledgeDocument }
  = storeToRefs(trainingDatasStore)
const knowledgeSelected = computed(() => {
  return !!selectedKnowledgeDocument.value
})

onMounted(() => {
  // Call init API to wake up lambda tools when page loads
  trainingDatasStore.initKnowledgeApi()
})

onUnmounted(() => {
  if (trainingDataNew.value.websites) {
    trainingDatasStore.resetWebsites()
  }
})

const htmlTags = ref([
  'a',
  'abbr',
  'acronym',
  'address',
  'applet',
  'area',
  'article',
  'aside',
  'audio',
  'b',
  'base',
  'basefont',
  'bdi',
  'bdo',
  'bgsound',
  'big',
  'blink',
  'blockquote',
  'body',
  'br',
  'button',
  'canvas',
  'caption',
  'center',
  'cite',
  'code',
  'col',
  'colgroup',
  'command',
  'content',
  'data',
  'datalist',
  'dd',
  'del',
  'details',
  'dfn',
  'dialog',
  'dir',
  'div',
  'dl',
  'dt',
  'element',
  'em',
  'embed',
  'fieldset',
  'figcaption',
  'figure',
  'font',
  'footer',
  'form',
  'frame',
  'frameset',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
  'head',
  'header',
  'hgroup',
  'hr',
  'html',
  'i',
  'iframe',
  'image',
  'img',
  'input',
  'ins',
  'isindex',
  'kbd',
  'keygen',
  'label',
  'legend',
  'li',
  'link',
  'listing',
  'main',
  'map',
  'mark',
  'marquee',
  'menu',
  'menuitem',
  'meta',
  'meter',
  'nav',
  'nobr',
  'noembed',
  'noframes',
  'noscript',
  'object',
  'ol',
  'optgroup',
  'option',
  'output',
  'p',
  'param',
  'picture',
  'plaintext',
  'pre',
  'progress',
  'q',
  'rp',
  'rt',
  'ruby',
  's',
  'samp',
  'script',
  'section',
  'select',
  'shadow',
  'slot',
  'small',
  'source',
  'spacer',
  'span',
  'strike',
  'strong',
  'style',
  'sub',
  'summary',
  'sup',
  'table',
  'tbody',
  'td',
  'template',
  'textarea',
  'tfoot',
  'th',
  'thead',
  'time',
  'title',
  'tr',
  'track',
  'tt',
  'u',
  'ul',
  'var',
  'video',
  'wbr',
  'xmp'
])

const htmlClasses = ref([
  'body',
  'header',
  'footer',
  'main',
  'nav',
  'article',
  'section',
  'aside'
]) as Ref<string[]>

const updateIgnoreTags = (index: number, value: string[]) => {
  const tag = value[0] as string
  if (!htmlTags.value.find(htmlTag => htmlTag === tag)) {
    htmlTags.value.push(tag)
  }
  if (trainingDataNew.value.websites[index]?.ignore_tags.includes(tag)) {
    const tagIndex
      = trainingDataNew.value.websites[index]?.ignore_tags.indexOf(tag)
    trainingDataNew.value.websites[index]?.ignore_tags.splice(tagIndex, 1)
  } else {
    trainingDataNew.value.websites[index]?.ignore_tags.push(tag)
  }
}

const updateIgnoreClasses = (index: number, value: string[]) => {
  const tag = value[0] as string
  if (!htmlClasses.value.find(htmlClass => htmlClass === tag)) {
    htmlClasses.value.push(tag)
  }
  if (trainingDataNew.value.websites[index]?.ignore_classes.includes(tag)) {
    const tagIndex
      = trainingDataNew.value.websites[index]?.ignore_classes.indexOf(tag)
    trainingDataNew.value.websites[index]?.ignore_classes.splice(tagIndex, 1)
  } else {
    trainingDataNew.value.websites[index]?.ignore_classes.push(tag)
  }
}
</script>

<template>
  <UDashboardSection title="ウェブサイトのURLの登録">
    <template #description>
      <div class="text-xs text-gray-500 dark:text-gray-400">
        単独URLの情報のみ取得可能です。リンク先の取得は実施しません。
      </div>
    </template>
    <div
      v-for="(website, index) in trainingDataNew.websites"
      :key="index"
      class="flex flex-col gap-4"
    >
      <div
        v-if="website.loadings['training'] || website.loadings['preview']"
        class="flex text-sm items-center gap-2 justify-center w-full h-full absolute top-0 left-0 bg-white dark:bg-gray-900/10 backdrop-blur-sm z-10"
      >
        <UIcon
          name="eos-icons:loading"
          class="text-gray-500 text-lg"
        />
        {{
          website.loadings["training"]
            ? "データを登録中..."
            : "プレビューを取得中..."
        }}
      </div>
      <UFormGroup
        name="name"
        label="データソース"
        description="識別しやすい名前を付けてください。"
        required
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UInput
          v-model="website.name"
          autocomplete="off"
          size="md"
          placeholder="例：税金に関するデータ"
          :disabled="knowledgeSelected"
          data-tour="website-name-input"
        />
      </UFormGroup>

      <UFormGroup
        name="url"
        label="URL"
        required
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <template #description>
          <div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              データソースが取得されるURLを入力してください。
            </div>
          </div>
        </template>
        <UInput
          v-model="website.url"
          autocomplete="off"
          placeholder="例: https://example.com"
          size="md"
          data-tour="website-url-input"
        />
      </UFormGroup>
      <UFormGroup
        v-if="website.errors['preview']"
        name="label"
        label=" "
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UAlert
          :color="'red'"
          variant="subtle"
          :icon="'material-symbols:error'"
          :description="website.errors['preview']"
          :title="'プレビュー失敗'"
        />
      </UFormGroup>
      <div v-if="website.preview && website.url">
        <div
          class="flex items-center justify-center gap-1 text-gray-500 dark:text-gray-400"
        >
          <UIcon
            name="line-md:downloading-loop"
            class="text-xl"
          />
        </div>
        <div
          class="flex items-center justify-center mt-2 mb-1 gap-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 cursor-pointer"
          @click="website.expand = !website.expand"
        >
          <div class="text-xs font-semibold">
            {{
              website.loadings["preview"]
                ? "プレビュー取得中..."
                : "プレビューを確認"
            }}
          </div>
          <UIcon
            v-if="!website.loadings['preview']"
            name="mdi:menu-down"
            :class="{
              'transform rotate-180': website.expand
            }"
            class="transition-transform duration-200"
          />
        </div>
        <div
          v-if="!website.loadings['preview']"
          class="text-sm text-gray-500 dark:text-gray-400 border border-gray-300 dark:border-gray-700 px-2 py-3 rounded-md border-dashed transition-transform duration-200"
          :class="{
            'max-h-32 overflow-hidden relative': !website.expand,
            'h-auto': website.expand
          }"
        >
          <div>
            <div
              v-for="(row, index) in website.preview"
              :key="index"
              class="whitespace-break-spaces w-full"
            >
              {{ JSON.parse(row).content }}
            </div>
          </div>
          <div
            v-if="!website.expand"
            class="backdrop-blur-sm text-xs dark:hover:text-gray-300 hover:backdrop-blur-0 hover:text-gray-900 cursor-pointer flex items-center justify-center dark:bg-gray-800/10 bg-gray-400/10 h-10 absolute bottom-0 left-0 w-full"
            @click="website.expand = !website.expand"
          >
            もっと見る
          </div>
        </div>
      </div>

      <UFormGroup
        name="dynamic"
        label="動的ページ取り込み"
        description="オンにすると動的ページを取り込みます。"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UToggle
          v-model="website.dynamic"
          size="md"
          data-tour="website-dynamic-toggle"
        />
      </UFormGroup>

      <UFormGroup
        name="ignore_tags"
        label="無視するタグ"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <template #description>
          <div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              指定されたタグに囲まれたテキストは
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              データソースとして取りこまれません。
            </div>
          </div>
        </template>
        <USelectMenu
          :value="website.ignore_tags"
          :options="htmlTags"
          multiple
          searchable
          size="md"
          creatable
          :searchable-placeholder="'タグを検索できます。新しいタグを追加することもできます。'"
          data-tour="website-ignore-tags"
          @update:model-value="updateIgnoreTags(index, $event)"
        >
          <template #label>
            <template v-if="website.ignore_tags?.length">
              <span>{{ website.ignore_tags.join(", ") }}</span>
            </template>
            <template v-else>
              <span class="text-gray-500 dark:text-gray-400 truncate">
                タグを選択・入力してください。
              </span>
            </template>
          </template>
          <template #option="{ option }">
            <span
              class="truncate"
              :class="{
                'text-primary-600': website.ignore_tags.includes(option)
              }"
            >{{ option }}</span>
          </template>
          <template #option-create="{ option }">
            <span class="flex-shrink-0">タグ追加:</span>

            <span class="block truncate">{{ option }}</span>
          </template>
        </USelectMenu>
      </UFormGroup>
      <UFormGroup
        name="ignore_classes"
        label="無視するクラス"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <template #description>
          <div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              指定されたクラスが設定された要素は
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              データソースとして取りこまれません。
            </div>
          </div>
        </template>
        <USelectMenu
          :value="website.ignore_classes"
          :options="htmlClasses"
          multiple
          searchable
          size="md"
          creatable
          :searchable-placeholder="'クラスを検索・追加'"
          @update:model-value="updateIgnoreClasses(index, $event)"
        >
          <template #label>
            <template v-if="website.ignore_classes?.length">
              <span>{{ website.ignore_classes.join(", ") }}</span>
            </template>
            <template v-else>
              <span class="text-gray-500 dark:text-gray-400 truncate">
                クラスを選択・入力してください。
              </span>
            </template>
          </template>
          <template #option="{ option }">
            <span
              class="truncate"
              :class="{
                'text-primary-600': website.ignore_classes.includes(option)
              }"
            >{{ option }}</span>
          </template>
          <template #option-create="{ option }">
            <span class="flex-shrink-0">クラス追加:</span>

            <span class="block truncate">{{ option }}</span>
          </template>
        </USelectMenu>
      </UFormGroup>
      <UFormGroup
        name="priority"
        label="優先度"
        description="1~9999の範囲の数字を設定ください。"
        help="数字が小さいほど回答生成の際に優先して使用されます。"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <BasePriorityInput
          v-model="website.priority"
          autocomplete="off"
          size="md"
          data-tour="website-priority-input"
        />
      </UFormGroup>

      <UFormGroup
        name="label"
        label="ラベル"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <template #description>
          <div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              ラベルを設定することによりデータソースの
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              分類が可能になります。
            </div>
          </div>
        </template>
        <LabelsSelect v-model="website.labels" />
      </UFormGroup>

      <UFormGroup
        v-if="index || trainingDataNew.websites.length > 1"
        label="#"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UButton
          icon="i-heroicons-trash"
          size="sm"
          variant="solid"
          color="white"
          label="データソースを削除"
          :trailing="false"
          block
          :ui="{
            color: {
              white: {
                solid: 'text-red-500 dark:text-red-300'
              }
            }
          }"
          @click="trainingDatasStore.removeWebsite(index)"
        />
      </UFormGroup>

      <UFormGroup
        v-if="website.success !== null"
        name="label"
        label="登録状況"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UAlert
          :color="website.success ? 'green' : 'red'"
          variant="subtle"
          :icon="
            website.success ? 'ep:success-filled' : 'material-symbols:error'
          "
          :description="
            website.success
              ? '登録が完了しました。'
              : website.errors['training']?.error_message
          "
          :title="website.success ? '登録成功' : '登録失敗'"
        />
      </UFormGroup>
    </div>
    <div>
      <UFormGroup
        label=" "
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UButton
          icon="i-heroicons-plus"
          size="sm"
          color="gray"
          variant="solid"
          label="他のURLを追加"
          :trailing="false"
          block
          data-tour="add-website-button"
          @click="trainingDatasStore.addWebsite"
        />
      </UFormGroup>
    </div>
  </UDashboardSection>
</template>

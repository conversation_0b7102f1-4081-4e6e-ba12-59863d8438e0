<script setup lang="ts">
const { selectedTenantId, selectedEnvId } = useApp()

const uiClass = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const ui = {
  formGroup: {
    container: 'col-span-7',
    inner: 'col-span-5'
  }
}
const trainingDatasStore = useTrainingDatasStore()
const { trainingDataNew, trainingAcceptsFiles, selectedKnowledgeDocument }
  = storeToRefs(trainingDatasStore)
const knowledgeSelected = computed(() => {
  return !!selectedKnowledgeDocument.value
})

onMounted(() => {
  // Call init API to wake up lambda tools when page loads
  trainingDatasStore.initKnowledgeApi()
})

onUnmounted(() => {
  if (trainingDataNew.value.documents) {
    trainingDatasStore.resetDocuments()
  }
})
const onMultipleFilesUpload = (files: File[], index: number) => {
  const { name, priority, labels } = trainingDataNew.value.documents[index]
  let labelCount = 1
  for (const file of files) {
    const newName = `${name} ${labelCount}`
    labelCount++
    trainingDatasStore.addDocument(file, newName, priority, labels)
  }
}

// Function to check if file is Excel or CSV
const isExcelOrCsvFile = (file: File | null) => {
  if (!file) return false
  const excelMimeType
    = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  const csvMimeType = 'text/csv'
  return (
    file.type === excelMimeType
    || file.type === csvMimeType
    || file.name.toLowerCase().endsWith('.xlsx')
    || file.name.toLowerCase().endsWith('.csv')
  )
}

const { canUseFeature, previewBadge, isPreviewFeature } = useFeatures()
</script>

<template>
  <UDashboardSection title="ファイルのアップロード">
    <template #description>
      <div class="text-xs text-gray-500 dark:text-gray-400">
        データのフォーマットによっては取り込めない場合があるため、登録時にプレビューを確認して、問題がある場合は適宜削除、修正等手動で行ってください。
      </div>
    </template>
    <div
      v-for="(document, index) in trainingDataNew.documents"
      :key="index"
      class="flex flex-col gap-4 relative"
    >
      <div
        v-if="document.loadings['training']"
        class="flex text-sm items-center gap-2 justify-center w-full h-full absolute top-0 left-0 bg-white dark:bg-gray-900/10 backdrop-blur-sm z-10"
      >
        <UIcon
          name="eos-icons:loading"
          class="text-gray-500 text-lg"
        />
        データを登録中...
      </div>
      <UFormGroup
        name="name"
        label="データソース"
        description="識別しやすい名前を付けてください。"
        required
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UInput
          v-model="document.name"
          autocomplete="off"
          size="md"
          placeholder="例：税金に関するデータ"
          :disabled="knowledgeSelected"
        />
      </UFormGroup>

      <UFormGroup
        name="file"
        label="ファイル"
        required
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
        class="!items-start"
      >
        <template #description>
          <div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              データソースのファイルを選択してください。
            </div>
            <ul
              v-if="!document.file"
              class="text-xs text-gray-500 dark:text-gray-400 list-disc list-inside pl-1"
            >
              <li>CSV, XLSX, DOCX, PPTX, TXT, PDF, MD, JSON のみ対応</li>
              <li>古いOffice（拡張子にxがないもの）は不可</li>
              <li>画像PDF（OCR対応）は不可</li>
              <li>アップロードサイズは一度で最大5M</li>
            </ul>
          </div>
        </template>
        <div>
          <BaseFilesSelect
            v-model="document.file"
            :accept="trainingAcceptsFiles.join(',')"
            :class="{
              '!h-12': false
            }"
            @handle-multiple-files="(files: File[]) => onMultipleFilesUpload(files, index)"
          />
        </div>
      </UFormGroup>

      <!-- FAQ Mode Toggle - Only show for Excel/CSV files -->
      <UFormGroup
        v-if="isExcelOrCsvFile(document.file) && canUseFeature('faqImportMode')"
        name="as_faq"
        label="FAQモード"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <template #description>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            ExcelまたはCSVファイルをFAQ形式でインポートします
          </div>
        </template>
        <div class="flex items-center gap-2 justify-between w-full">
          <UToggle
            v-model="document.as_faq"
            size="md"
          />
        </div>
        <template #hint>
          <UBadge
            v-if="isPreviewFeature('faqImportMode')"
            v-bind="previewBadge('faqImportMode')"
          />
        </template>
      </UFormGroup>

      <!-- FAQ Settings - Only show when FAQ mode is enabled -->
      <template v-if="document.as_faq && isExcelOrCsvFile(document.file)">
        <UFormGroup
          name="faq_header_row"
          label="FAQヘッダー行"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <template #description>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              ヘッダーの行番号を指定してください。行が結合されている場合は、すべての行番号を指定してください。
            </div>
          </template>
          <FaqHeaderRowSelect v-model="document.faq_header_row" />
        </UFormGroup>

        <UFormGroup
          name="faq_id_header"
          label="FAQ IDヘッダー"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <template #description>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              QAレコードのIDのヘッダーテキスト（オプション）。未指定の場合はシステムが番号を自動生成します。
            </div>
          </template>
          <UInput
            v-model="document.faq_id_header"
            placeholder="例: ID"
            size="md"
          />
        </UFormGroup>

        <UFormGroup
          name="faq_subtitle_header"
          label="FAQサブタイトルヘッダー"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <template #description>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              QAレコードのサブタイトルのヘッダーテキスト（オプション）
            </div>
          </template>
          <UInput
            v-model="document.faq_subtitle_header"
            placeholder="例: サブタイトル"
            size="md"
          />
        </UFormGroup>

        <UFormGroup
          name="faq_question_header"
          label="FAQ質問ヘッダー"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <template #description>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              QAレコードの質問のヘッダーテキスト（未指定の場合は「問い合わせ文」が使用されます）
            </div>
          </template>
          <UInput
            v-model="document.faq_question_header"
            placeholder="例: 質問"
            size="md"
          />
        </UFormGroup>

        <UFormGroup
          name="faq_answer_header"
          label="FAQ回答ヘッダー"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <template #description>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              QAレコードの回答のヘッダーテキスト（未指定の場合は「応答文」が使用されます）
            </div>
          </template>
          <UInput
            v-model="document.faq_answer_header"
            placeholder="例: 回答"
            size="md"
          />
        </UFormGroup>

        <UFormGroup
          name="faq_category_header"
          label="FAQカテゴリヘッダー"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <template #description>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              QAレコードのカテゴリのヘッダーテキスト（オプション）。カテゴリの区切りにはカンマ、コロン、または空白を使用してください。
            </div>
          </template>
          <UInput
            v-model="document.faq_category_header"
            placeholder="例: カテゴリ"
            size="md"
          />
        </UFormGroup>
      </template>

      <div v-if="document.preview && document.file">
        <div
          class="flex items-center justify-center gap-1 text-gray-500 dark:text-gray-400"
        >
          <UIcon
            name="line-md:downloading-loop"
            class="text-xl"
          />
        </div>
        <div
          class="flex items-center justify-center mt-2 mb-1 gap-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 cursor-pointer"
          @click="document.expand = !document.expand"
        >
          <div class="text-xs font-semibold">
            {{
              document.loadings["preview"]
                ? "プレビュー取得中..."
                : "プレビューを確認"
            }}
          </div>
          <UIcon
            v-if="!document.loadings['preview']"
            name="mdi:menu-down"
            :class="{
              'transform rotate-180': document.expand
            }"
            class="transition-transform duration-200"
          />
        </div>
        <div
          v-if="!document.loadings['preview']"
          class="text-sm text-gray-500 dark:text-gray-400 border border-gray-300 dark:border-gray-700 px-2 py-3 rounded-md border-dashed transition-transform duration-200"
          :class="{
            'max-h-32 overflow-hidden relative': !document.expand,
            'h-auto': document.expand
          }"
        >
          <ul class="list-disc list-inside pl-1">
            <li
              v-for="(row, rowIndex) in document.preview"
              :key="rowIndex"
              class="whitespace-break-spaces"
            >
              {{ row }}
            </li>
          </ul>
          <div
            v-if="!document.expand"
            class="backdrop-blur-sm text-xs dark:hover:text-gray-300 hover:backdrop-blur-0 hover:text-gray-900 cursor-pointer flex items-center justify-center dark:bg-gray-800/10 bg-gray-400/10 h-10 absolute bottom-0 left-0 w-full"
            @click="document.expand = !document.expand"
          >
            もっと見る
          </div>
        </div>
      </div>
      <UFormGroup
        name="priority"
        label="優先度"
        description="1~9999の範囲の数字を設定ください。"
        help="数字が小さいほど回答生成の際に優先して使用されます。"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <BasePriorityInput
          v-model="document.priority"
          autocomplete="off"
          size="md"
        />
      </UFormGroup>

      <UFormGroup
        name="label"
        label="ラベル"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <template #description>
          <div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              ラベルを設定することによりデータソースの分類が
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              可能になります。
            </div>
          </div>
        </template>
        <LabelsSelect v-model="document.labels" />
      </UFormGroup>

      <UFormGroup
        v-if="index || trainingDataNew.documents.length > 1"
        label="#"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UButton
          icon="i-heroicons-trash"
          size="sm"
          variant="solid"
          color="white"
          label="データソースを削除"
          :trailing="false"
          block
          :ui="{
            color: {
              white: {
                solid: 'text-red-500 dark:text-red-300'
              }
            }
          }"
          @click="trainingDatasStore.removeDocument(index)"
        />
      </UFormGroup>

      <UFormGroup
        v-if="document.success !== null"
        name="label"
        label="登録状況"
        :class="uiClass.formGroup"
        :ui="ui.formGroup"
      >
        <UAlert
          :color="document.success ? 'green' : 'red'"
          variant="subtle"
          :icon="
            document.success ? 'ep:success-filled' : 'material-symbols:error'
          "
          :description="
            document.success
              ? '登録が完了しました。'
              : $t(
                document.errors['training']?.error_message
                  || document.errors['training']?.message
                  || '登録に失敗しました。ファイルを確認してください。'
              )
          "
          :title="document.success ? '登録完了' : '登録失敗'"
        />
      </UFormGroup>
    </div>
    <div>
      <UFormGroup
        :class="uiClass.formGroup"
        :ui="{
          ...ui.formGroup,
          container: '!col-span-7',
          inner: 'col-span-5'
        }"
        label=" "
      >
        <UButton
          icon="i-heroicons-plus"
          size="sm"
          color="gray"
          variant="solid"
          label="データソースを追加"
          :trailing="false"
          block
          @click="trainingDatasStore.addDocument(null)"
        />
      </UFormGroup>
    </div>
  </UDashboardSection>
</template>

<script setup lang="ts">
import { debounce } from 'lodash'
import { BaseStatusToggleNoConfirm } from '#components'
import type { RagKnowledgeDocument } from '~/types/knowledge'

const confirm = useConfirm()
const toast = useToast()
const searchIndexersStore = useSearchIndexersStore()
const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()
const router = useRouter()
const trainingDatasStore = useTrainingDatasStore()
const { contextTypeList } = useContextType()
const {
  trainingDataFilter,
  trainingDatas,
  trainingDatasCount,
  loadings,
  hasAnyUnreflected,
  trainingDatasPagination,
  trainingDatasTotal,
  selectedKnowledgeDocument
} = storeToRefs(trainingDatasStore)
const defaultColumns = [
  {
    key: 'name',
    label: 'データソース',
    sortable: false
  },
  {
    key: 'original_filename',
    label: 'ファイル名/URL',
    sortable: false
  },
  {
    key: 'created_at',
    label: '登録日時',
    sortable: false
  },
  {
    key: 'updated_at',
    label: '更新日時',
    sortable: false
  },
  {
    key: 'enabled',
    label: 'ステータス',
    sortable: false,
    rowClass: 'text-center min-w-36'
  }
]

const { loadings: searchIndexerLoadings, indexerStatus } = storeToRefs(searchIndexersStore)

const q = ref('')
const selectedColumns = ref(defaultColumns)
const selectedStatuses = ref([])
const selectedLocations = ref([])
const sort = computed({
  get: () => ({
    column: 'id',
    direction: trainingDatasPagination.value.asc ? 'asc' : 'desc'
  }),
  set: (value) => {
    trainingDatasPagination.value.asc = value.direction === 'asc'
  }
})
const input = ref<{ input: HTMLInputElement }>()
const isNewUserModalOpen = ref(false)

const columns = computed(() =>
  defaultColumns.filter(column => selectedColumns.value.includes(column))
)
const defaultStatuses = [
  { label: '有効', value: 'enabled' },
  { label: '無効', value: 'disabled' }
]

function onSelect(row: any) {
  router.push(
    `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/${row.id}`
  )
}

defineShortcuts({
  '/': () => {
    input.value?.input?.focus()
  }
})

onMounted(() => {
  // Only fetch data if we have valid tenant and env IDs
  if (
    selectedTenantId.value
    && selectedTenantId.value !== 'undefined'
    && selectedEnvId.value
    && selectedEnvId.value !== 'undefined'
  ) {
    trainingDatasStore.checkIfHasAnyUnreflected(
      selectedTenantId.value,
      selectedEnvId.value
    )
    trainingDatasStore.fetchTrainingDatas(
      selectedTenantId.value,
      selectedEnvId.value
    )
  } else {
    console.warn(
      'Cannot fetch training data: No valid tenant or environment ID selected'
    )
  }
})

const refresh = () => {
  // Only fetch data if we have valid tenant and env IDs
  if (
    selectedTenantId.value
    && selectedTenantId.value !== 'undefined'
    && selectedEnvId.value
    && selectedEnvId.value !== 'undefined'
  ) {
    trainingDatasStore.fetchTrainingDatas(
      selectedTenantId.value,
      selectedEnvId.value,
      q.value // Pass the search query
    )
  } else {
    console.warn(
      'Cannot refresh training data: No valid tenant or environment ID selected'
    )
  }
}

const debounceRefresh = debounce(refresh, 1000)

watch(
  () => trainingDatasPagination.value,
  () => {
    refresh()
  },
  { deep: true, immediate: true }
)

// watch indexerUpdateHistoriesPagination.value.pageCount, if it is changed, reset page to 1
watch(
  () => trainingDatasPagination.value.pageCount,
  () => {
    trainingDatasPagination.value.page = 1
  }
)

// watch trainingDataFilter, debounceRefresh
watch(
  () => trainingDataFilter,
  () => {
    debounceRefresh()
  },
  { deep: true }
)

// watch search query, debounceRefresh
watch(
  () => q.value,
  () => {
    debounceRefresh()
  }
)

const onDeleteTrainingData = async (row: any) => {
  confirm.show({
    title: '削除確認',
    description: 'このデータソースを削除しますか？',
    confirmText: '削除',
    onConfirm: async () => {
      // Only delete data if we have valid tenant and env IDs
      if (
        !selectedTenantId.value
        || selectedTenantId.value === 'undefined'
        || !selectedEnvId.value
        || selectedEnvId.value === 'undefined'
      ) {
        toast.add({
          title: 'エラー',
          description: '有効なテナントまたは環境IDが選択されていません',
          color: 'red'
        })
        return
      }

      const result = await trainingDatasStore.deleteTrainingData(
        row.id,
        selectedTenantId.value,
        selectedEnvId.value
      )
      if (result) {
        toast.add({
          title: '削除しました',
          description: 'データソースを削除しました',
          color: 'green'
        })
      }
    }
  })
}

const toggleTrainingDataStatus = async (row: any) => {
  if (isSelectedEnvIsProd.value) return
  confirm.show({
    title: 'ステータス変更',
    description: `このデータソースのステータスを${
      row.enabled ? '無効' : '有効'
    }に変更しますか？`,
    confirmText: '変更',
    onConfirm: async () => {
      const result = await trainingDatasStore.updateTrainingData(
        row.id,
        selectedTenantId.value,
        selectedEnvId.value,
        { enabled: !row.enabled }
      )
      if (result) {
        toast.add({
          title: '成功',
          description: 'データソースのステータスを変更しました',
          color: 'green'
        })
      }
    }
  })
}

const onReuploadOrReImport = (row: RagKnowledgeDocument) => {
  selectedKnowledgeDocument.value = row
  if (row.original_filename) {
    // 再アップロード
    router.push(
      `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/new`
    )
  } else {
    // 再インポート
    router.push(
      `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/new/web`
    )
  }
}
const onCreateNew = () => {
  selectedKnowledgeDocument.value = null
  router.push(
    `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/new`
  )
}

const rowMenus = (row: any) => {
  return [
    [
      {
        label: 'ナレッジ編集',
        icon: 'carbon:ibm-knowledge-catalog',
        click: () => onSelect(row),
        disabled: isSelectedEnvIsProd.value
      },
      {
        label: '無効化/有効化',
        icon: 'ri:toggle-line',
        click: () => toggleTrainingDataStatus(row),
        disabled: isSelectedEnvIsProd.value
      },
      {
        label: '再アップロード',
        icon: 'material-symbols:upload',
        click: () => onReuploadOrReImport(row),
        disabled: isSelectedEnvIsProd.value
      }
    ],
    [
      {
        label: '削除',
        icon: 'i-heroicons-trash-20-solid',
        // class: 'text-red-500 dark:text-red-400',
        iconClass: 'text-red-500 dark:text-red-400',
        click: () => onDeleteTrainingData(row),
        disabled: isSelectedEnvIsProd.value
      }
    ]
  ]
}

const onRunIndexer = async () => {
  await searchIndexersStore.runIndexer(
    selectedTenantId.value,
    selectedEnvId.value
  )
}
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar
        title="データソース"
        :badge="trainingDatasCount"
      >
        <template #right>
          <UInput
            ref="input"
            v-model="q"
            icon="i-heroicons-funnel"
            autocomplete="off"
            placeholder="データソースの検索"
            class="hidden lg:block"
            @keydown.esc="$event.target.blur()"
          >
            <template #trailing>
              <UKbd value="/" />
            </template>
          </UInput>

          <UButton
            v-if="!isSelectedEnvIsProd"
            label="データソース登録"
            trailing-icon="i-heroicons-plus"
            color="gray"
            @click="onCreateNew"
          />
        </template>
      </UDashboardNavbar>
      <UDashboardToolbar
        v-if="hasAnyUnreflected && indexerStatus !== 2"
        class="bg-orange-50 dark:bg-orange-100 text-gray-800 dark:text-gray-900"
      >
        <div class="flex items-center gap-1.5 text-sm">
          <UIcon name="si:warning-fill" />
          更新されたナレッジを適用するにはインデックスの更新が必要です。
        </div>
        <UButton
          icon="material-symbols:model-training"
          label="インデックスを更新"
          color="orange"
          variant="solid"
          size="sm"
          :loading="searchIndexerLoadings.runIndexer"
          @click="onRunIndexer"
        />
        <template #right />
      </UDashboardToolbar>
      <BaseRunIndexerProgress />
      <UDashboardToolbar>
        <template #left>
          <USelectMenu
            key="value"
            v-model="trainingDataFilter.status"
            icon="i-heroicons-check-circle"
            placeholder="ステータス"
            class="w-48"
            multiple
            :options="defaultStatuses"
            :ui-menu="{ option: { base: 'capitalize' } }"
          >
            <template #label>
              <div v-if="trainingDataFilter.status?.length">
                {{
                  trainingDataFilter.status
                    ?.map((obj: any) => obj.label)
                    .join(", ")
                }}
              </div>
              <div v-else>
                ステータス
              </div>
            </template>
          </USelectMenu>
          <USelectMenu
            v-model="trainingDataFilter.contextTypes"
            icon="hugeicons:file-unknown"
            placeholder="ファイルの種類"
            :options="contextTypeList"
            multiple
            class="w-48"
          >
            <template #label>
              <div v-if="trainingDataFilter.contextTypes?.length">
                <label
                  v-if="contextTypeList"
                  class="flex items-center border border-white rounded-md px-2"
                  @click.stop="
                    () => {
                      trainingDataFilter.contextTypes = null;
                    }
                  "
                >
                  選択クリア
                  <Icon name="lucide:x" />
                </label>
              </div>
              <div v-else>
                ファイルの種類
              </div>
            </template>
          </USelectMenu>
          <UInput
            v-model="trainingDataFilter.source_url"
            icon="i-heroicons-magnifying-glass-20-solid"
            autocomplete="off"
            placeholder="Web検索"
            size="sm"
            @keydown.esc="$event.target.blur()"
          >
            <template #trailing>
              <UKbd value="/" />
            </template>
          </UInput>
        </template>

        <template #right>
          <UButton
            icon="prime:sync"
            color="gray"
            size="sm"
            @click="refresh"
          />
        </template>
      </UDashboardToolbar>

      <UDashboardModal
        v-model="isNewUserModalOpen"
        title="New user"
        description="Add a new user to your database"
        :ui="{ width: 'sm:max-w-md' }"
      >
        <!-- ~/components/users/UsersForm.vue -->
        <UsersForm @close="isNewUserModalOpen = false" />
      </UDashboardModal>

      <UTable
        v-model:sort="sort"
        sort-mode="manual"
        :rows="trainingDatas"
        :columns="columns"
        :loading="loadings.fetchTrainingDatas"
        class="w-full"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #name-data="{ row }">
          <div
            class="max-w-52 text-xs truncate cursor-pointer hover:underline hover:text-primary"
            @click="onSelect(row)"
          >
            {{ row.name }}
          </div>
        </template>
        <template #original_filename-data="{ row }">
          <div class="flex items-center gap-1">
            <UTooltip :text="row.original_context_type">
              <UAvatar
                :icon="
                  contextTypeIcon(row.original_context_type || row.source_url)
                "
                :alt="row.original_context_type"
                size="xs"
                :ui="{
                  rounded: 'rounded-lg',
                  icon: {
                    size: {
                      xs: 'w-4 h-4'
                    }
                  }
                }"
              />
            </UTooltip>

            <div class="flex flex-col text-xs">
              <span
                v-if="row.original_filename"
                class="font-medium"
              >{{
                shorterFileName(row.original_filename)
              }}</span>
              <a
                v-else
                :href="row.source_url"
                target="_blank"
                class="text-primary hover:underline"
              >
                {{ shorterFileName(row.source_url) }}
              </a>
            </div>
          </div>
        </template>

        <template #enabled-data="{ row }">
          <div class="flex items-center gap-3 justify-between">
            <BaseStatusToggleNoConfirm
              v-model="row.enabled"
              class="capitalize flex-1 justify-center max-w-16"
              @toggle="toggleTrainingDataStatus(row)"
            />
            <UDropdown
              class="group-hover:block"
              :class="{
                block: loadings['updateTrainingData']?.[row.id],
                hidden: !loadings['updateTrainingData']?.[row.id]
              }"
              :items="rowMenus(row)"
              :popper="{ placement: 'bottom-start' }"
            >
              <UButton
                class="row-menu"
                color="white"
                icon="charm:menu-meatball"
                size="xs"
                square
                :loading="loadings['updateTrainingData']?.[row.id]"
              />
            </UDropdown>
          </div>
        </template>

        <template #created_at-data="{ row }">
          <div>
            <div>
              {{ formatDateTime(row.created_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              作成者: {{ row.created_username }} ({{
                formatDistanceStrictDateTime(row.created_at)
              }})
            </div>
          </div>
        </template>

        <template #updated_at-data="{ row }">
          <div>
            <div>
              {{ formatDateTime(row.updated_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              更新者: {{ row. updated_username }} ({{
                formatDistanceStrictDateTime(row.updated_at)
              }})
            </div>
          </div>
        </template>
      </UTable>
      <UDivider class="mt-0" />
      <UDashboardToolbar>
        <template #left>
          <div class="flex flex-wrap justify-between items-center">
            <div class="flex items-center gap-1.5">
              <span class="text-sm leading-5">表示件数:</span>

              <USelect
                v-model="trainingDatasPagination.pageCount"
                :options="[10, 20, 50, 100, 500, 1000]"
                class="w-20"
                size="xs"
              />
            </div>
            <UDivider
              class="mx-3 h-full py-1"
              orientation="vertical"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-if="trainingDatasPagination.pageCount < trainingDatasTotal"
            v-model="trainingDatasPagination.page"
            :page-count="trainingDatasPagination.pageCount"
            :total="trainingDatasTotal"
            size="sm"
          />
        </template>
      </UDashboardToolbar>
    </UDashboardPanel>
  </UDashboardPage>
</template>

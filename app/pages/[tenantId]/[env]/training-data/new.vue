<script setup lang="ts">
import { onUnmounted } from 'vue'
import type { FormError, FormSubmitEvent } from '#ui/types'

const { selectedTenantId, selectedEnvId } = useApp()

const route = useRoute()
const router = useRouter()
const trainingDatasStore = useTrainingDatasStore()
const { trainingDataNavigators } = useNavigators()
const { trainingDataNewSummary, loadings, selectedKnowledgeDocument } = storeToRefs(trainingDatasStore)

const { trainingDataTypeNavigators } = useNavigators()

const toast = useToast()

async function onSubmit(event: FormSubmitEvent<any>) {
  const newDocumentInfoIsFilled = trainingDatasStore.trainingDataNew.documents.some(doc => 
    doc.name && doc.file
  )

  const newWebInfoIsFilled = trainingDatasStore.trainingDataNew.websites?.some?.(website => 
    website.name && website.url
  ) || false

  if (!newDocumentInfoIsFilled && !newWebInfoIsFilled) {
    toast.add({
      title: '入力エラー',
      description: 'データソース名とファイル（またはURL）を入力してください。',
      icon: 'i-heroicons-exclamation-circle',
      color: 'red'
    })
    return
  }
  const result = await trainingDatasStore.trainingData(selectedTenantId.value, selectedEnvId.value)
  console.log('🚀 ~ onSubmit ~ result:', result)
  if (result) {
    toast.add({ title: 'データソースが登録されました', icon: 'i-heroicons-check-circle' })
    router.push(`/${selectedTenantId.value}/${selectedEnvId.value}/training-data/`)
  }
}

const onPreview = () => {
  trainingDatasStore.previewTrainingData(selectedTenantId.value, selectedEnvId.value)
}

const breadcrumb = computed(() => {
  const value: any[] = [
    {
      label: trainingDataNavigators.value[0]?.label,
      icon: trainingDataNavigators.value[0]?.icon,
      to: `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/`
    },
    {
      label: selectedKnowledgeDocument.value ? 'データソース再登録' : 'データソース登録',
      icon: 'icons8:plus'
    }
  ]
  if (selectedKnowledgeDocument.value) {
    value.push({
      label: selectedKnowledgeDocument.value.name
    })
  }
  return value
})

onMounted(() => {
  trainingDatasStore.setLoadedDocumentToForm()
})

onUnmounted(() => {
  selectedKnowledgeDocument.value = null
})
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar>
        <template #title>
          <UBreadcrumb :links="breadcrumb" />
        </template>
      </UDashboardNavbar>

      <UDashboardToolbar
        v-if="!selectedKnowledgeDocument"
        class="py-0 px-1.5 overflow-x-auto"
      >
        <UHorizontalNavigation :links="trainingDataTypeNavigators" />
      </UDashboardToolbar>

      <UDashboardPanelContent
        id="training-data-new"
        class="pb-24"
      >
        <div class="flex flex-row gap-8 relative w-full">
          <NuxtPage class="flex-1" />
          <div class="sticky top-0 right-0 min-w-64 h-96">
            <UCard class="w-full dark:bg-gray-900">
              <template #header>
                データソース
              </template>

              <UAsideLinks :links="trainingDataNewSummary" />

              <template #footer>
                <div class="flex flex-col gap-3">
                  <UButton
                    icon="mdi:eye"
                    size="sm"
                    color="gray"
                    variant="solid"
                    label="プレビュー"
                    :trailing="true"
                    block
                    :ui="{
                      block: 'justify-between'
                    }"
                    :loading="loadings.previewTrainingData"
                    @click="onPreview"
                  />
                  <UButton
                    icon="ep:right"
                    size="sm"
                    color="primary"
                    variant="solid"
                    label="登録"
                    :trailing="true"
                    block
                    :ui="{
                      block: 'justify-between'
                    }"
                    :loading="loadings.trainingData"
                    @click="onSubmit"
                  />
                </div>
              </template>
            </UCard>
          </div>
        </div>
      </UDashboardPanelContent>
    </UDashboardPanel>
  </UDashboardPage>
</template>

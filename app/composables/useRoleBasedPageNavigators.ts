import { PERMISSIONS } from '~/stores/permissions'

/**
 * Composable for role-based page navigation
 */
export const useRoleBasedPageNavigators = () => {
  const navigators = useNavigators()
  const { hasPermission } = useAppPermissions()
  const { isPageAccessible } = usePagePermissions()
  const pagePermissionsStore = usePagePermissionsStore()
  
  /**
   * Filter navigation items based on page permissions
   * @param items Navigation items to filter
   * @returns Filtered navigation items
   */
  const filterNavigationItems = (items: any[]) => {
    return items.filter(item => {
      // Get the page path from the item
      const path = item.to
      
      // Find the corresponding page permission
      const page = pagePermissionsStore.pagePermissions.find(p => p.path === path)
      
      // If no matching page is found, allow access by default
      if (!page) {
        return true
      }
      
      // Check if the page is accessible
      return isPageAccessible(page)
    })
  }
  
  // Filter settings navigators based on page permissions
  const roleBasedSettingsNavigators = computed(() => {
    const settingsNavs = navigators.settingsNavigators.value(true)
    return filterNavigationItems(settingsNavs)
  })
  
  // Filter admin settings navigators based on page permissions
  const roleBasedAdminSettingsNavigators = computed(() => {
    const adminNavigators = navigators.adminSettingsNavigators.value(true)
    return filterNavigationItems(adminNavigators)
  })
  
  // Filter training data navigators based on page permissions
  const roleBasedTrainingDataNavigators = computed(() => {
    const trainingDataNavs = navigators.trainingDataNavigators.value
    return filterNavigationItems(trainingDataNavs)
  })
  
  // Filter statistics navigators based on page permissions
  const roleBasedStatisticsNavigators = computed(() => {
    const statisticsNavs = navigators.statisticsNavigators.value
    return filterNavigationItems(statisticsNavs)
  })
  
  return {
    ...navigators,
    roleBasedSettingsNavigators,
    roleBasedAdminSettingsNavigators,
    roleBasedTrainingDataNavigators,
    roleBasedStatisticsNavigators
  }
}

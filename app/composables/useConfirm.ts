import { createSharedComposable } from '@vueuse/core'

interface Confirm {
  title: string
  description?: string
  icon?: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void
}

const _useConfirm = () => {
  const open = ref(false)
  const loading = ref(false)
  const confirm = ref({
    title: '',
    description: '',
    icon: 'i-heroicons-exclamation-circle',
    confirmText: ' OK',
    cancelText: 'キャンセル',
    onConfirm: () => {}
  }) as Ref<Confirm>

  function show(_confirm: Confirm) {
    confirm.value.title = _confirm.title
    if (_confirm.description) {
      confirm.value.description = _confirm.description
    }
    if (_confirm.icon) {
      confirm.value.icon = _confirm.icon
    }

    if (_confirm.confirmText) {
      confirm.value.confirmText = _confirm.confirmText
    }

    if (_confirm.cancelText) {
      confirm.value.cancelText = _confirm.cancelText
    }

    confirm.value.onConfirm = _confirm.onConfirm

    open.value = true
  }
  return {
    open,
    loading,
    confirm,
    show
  }
}

export const useConfirm = createSharedComposable(_useConfirm)

export const useFeatures = () => {
  const runtimeConfig = useRuntimeConfig()
  const authStore = useAuthStore()

  const { isOperator } = storeToRefs(authStore)

  const canUseFeature = (key: string) => {
    return (
      runtimeConfig.public.features[
        key as keyof typeof runtimeConfig.public.features
      ] || isOperator.value
    )
  }

  const isPreviewFeature = (key: string) => {
    return !runtimeConfig.public.features[
      key as keyof typeof runtimeConfig.public.features
    ]
  }

  const previewBadge = (key: string) => {
    return {
      label: isPreviewFeature(key) ? 'プレビュー' : '',
      color: 'red',
      variant: 'outline',
      size: 'xs',
      ui: {
        size: {
          xs: 'text-[8px] px-1 py-0'
        }
      }
    }
  }

  return {
    canUseFeature,
    isPreviewFeature,
    previewBadge
  }
}

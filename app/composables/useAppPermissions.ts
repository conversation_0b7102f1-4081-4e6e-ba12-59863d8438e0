import { PERMISSIONS } from '~/stores/permissions'
import { UserType } from '~/types/index.d'

export const useAppPermissions = () => {
  const permissionsStore = usePermissionsStore()
  const authStore = useAuthStore()
  const { userRole } = storeToRefs(authStore)

  // No longer need selectedEnvId

  // Get environments store
  const environmentsStore = useEnvironmentsStore()
  const { selectedEnv } = storeToRefs(environmentsStore)

  // Check if the current environment is a staging environment (not production)
  const isStagingEnvironment = computed(() => {
    return selectedEnv.value?.environment !== 1
  })

  // Initialize permissions based on user role and environment
  onMounted(() => {
    if (userRole.value) {
      permissionsStore.setUserPermissions(userRole.value)

      // Set environment type based on selectedEnv.environment
      permissionsStore.setEnvironmentType()
    }
  })

  // Watch for changes in user role
  watch(() => userRole.value, (newRole) => {
    if (newRole) {
      permissionsStore.setUserPermissions(newRole)
    } else {
      permissionsStore.clearPermissions()
    }
  })

  // Watch for changes in environment
  watch(() => selectedEnv.value?.environment, () => {
    permissionsStore.setEnvironmentType()
  })

  /**
   * Get permissions that should only be available in staging environments for the current user role
   * @returns Array of permissions that are only for staging environments
   */
  const getStagingOnlyPermissions = (): string[] => {
    // Staff staging-only permissions
    const staffStagingPermissions = [
      PERMISSIONS.CREATE_TRAINING_DATA,
      PERMISSIONS.EDIT_TRAINING_DATA,
      PERMISSIONS.DELETE_TRAINING_DATA,
      PERMISSIONS.EDIT_KNOWLEDGE,
      PERMISSIONS.DELETE_KNOWLEDGE,
      PERMISSIONS.DOWNLOAD_KNOWLEDGE_CSV,
      PERMISSIONS.VIEW_BASIC_SETTINGS,
      PERMISSIONS.EDIT_BASIC_SETTINGS,
      PERMISSIONS.VIEW_CHATBOT_SETTINGS,
      PERMISSIONS.EDIT_CHATBOT_SETTINGS,
      PERMISSIONS.VIEW_ERROR_MESSAGES,
      PERMISSIONS.EDIT_ERROR_MESSAGES,
      PERMISSIONS.VIEW_SURVEY_SETTINGS,
      PERMISSIONS.EDIT_SURVEY_SETTINGS
    ]

    // Admin staging-only permissions
    const adminStagingPermissions = [
      PERMISSIONS.CREATE_TRAINING_DATA,
      PERMISSIONS.EDIT_TRAINING_DATA,
      PERMISSIONS.DELETE_TRAINING_DATA,
      PERMISSIONS.EDIT_KNOWLEDGE,
      PERMISSIONS.DELETE_KNOWLEDGE,
      PERMISSIONS.EDIT_BASIC_SETTINGS,
      PERMISSIONS.EDIT_CHATBOT_SETTINGS,
      PERMISSIONS.EDIT_ERROR_MESSAGES,
      PERMISSIONS.EDIT_SURVEY_SETTINGS,
      PERMISSIONS.EDIT_KNOWLEDGE_SETTINGS,
      PERMISSIONS.CREATE_LABEL,
      PERMISSIONS.EDIT_LABEL,
      PERMISSIONS.DELETE_LABEL,
      PERMISSIONS.CREATE_CATEGORY,
      PERMISSIONS.EDIT_CATEGORY,
      PERMISSIONS.DELETE_CATEGORY
    ]

    // Return the appropriate staging-only permissions based on user role
    if (userRole.value === UserType.STAFF) {
      return staffStagingPermissions
    } else if (userRole.value === UserType.ADMIN) {
      return adminStagingPermissions
    }

    // PNL_ADMIN has all permissions regardless of environment
    return []
  }

  // Check if user has a specific permission, considering the environment
  const hasPermission = (permission: string) => {
    // If we're in a staging environment, all permissions are available as normal
    if (isStagingEnvironment.value) {
      return permissionsStore.hasPermission(permission)
    }

    // In production environment, check if the permission is staging-only
    const stagingOnlyPermissions = getStagingOnlyPermissions()
    if (stagingOnlyPermissions.includes(permission)) {
      return false
    }

    // Otherwise, check normal permissions
    return permissionsStore.hasPermission(permission)
  }

  // Check if user has any of the specified permissions, considering the environment
  const hasAnyPermission = (permissions: string[]) => {
    return permissions.some(permission => hasPermission(permission))
  }

  // Check if user has all of the specified permissions, considering the environment
  const hasAllPermissions = (permissions: string[]) => {
    return permissions.every(permission => hasPermission(permission))
  }

  return {
    PERMISSIONS,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isStagingEnvironment
  }
}

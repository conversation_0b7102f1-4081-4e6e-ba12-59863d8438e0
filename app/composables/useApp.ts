import { createSharedComposable } from '@vueuse/core'
import type { Environment } from '~/types/environment'
import type { Tenant } from '~/types/tenant'

const _useApp = () => {
  const ragsStore = useRagsStore()
  const appConfig = useAppConfig()
  const settingsStore = useSettingsStore()
  const tenantsStore = useTenantsStore()
  const environmentsStore = useEnvironmentsStore()
  const trainingDatasStore = useTrainingDatasStore()

  const { selectedEnvId, selectedEnv, isSelectedEnvIsProd } = storeToRefs(environmentsStore)
  const { selectedTenantId, firstTenant, tenants } = storeToRefs(tenantsStore)
  const router = useRouter()
  const route = useRoute()
  const loadings = ref({}) as Ref<Record<string, any>>
  const isInited = ref(false)
  const initialApp = async () => {
    // if (isInited.value) return

    try {
      loadings.value.fullLoading = true
      await tenantsStore.fetchTenants()
      let redirect = false

      // Check if the cached Tenant ID is valid or not
      const validTenants = tenants.value.map((x: Tenant) => x.id) || []

      if (!validTenants.includes(selectedTenantId.value)) {
        // If not, select the first one
        console.error(`No Tenant = ${selectedTenantId.value}!!`)
        selectedTenantId.value = firstTenant.value?.id
        redirect = true
      }

      // Only fetch environments if we have a valid tenant ID
      if (selectedTenantId.value && selectedTenantId.value !== 'undefined') {
        await environmentsStore.fetchAllEnvs(selectedTenantId.value)
        if (!selectedEnvId.value) {
          selectedEnvId.value
            = environmentsStore.environments[selectedTenantId.value]?.[0]?.id
              || ''
        } else {
          // Check if the cached Env ID is valid or not
          const validEnvIds: string[]
            = environmentsStore.environments[selectedTenantId.value]?.map(
              (x: Environment) => x.id
            ) || []
          if (!validEnvIds.includes(selectedEnvId.value)) {
            // If not, redirect if possible
            console.error(
              `No Environment ${selectedEnvId.value} for Tenant = ${selectedTenantId.value}!!`
            )
            if (validEnvIds.length > 0) selectedEnvId.value = validEnvIds[0]!
          }
        }
      } else {
        console.warn('Cannot fetch environments: No valid tenant ID selected')
      }
      if (!route.params.tenantId || redirect) {
        router.push(`/${selectedTenantId.value}/${selectedEnvId.value}`)
      }
      isInited.value = true
    } catch (error: any) {
      console.error(error)
    } finally {
      loadings.value.fullLoading = false
      // Only fetch training data if we have valid tenant and env IDs
      if (
        selectedTenantId.value
        && selectedTenantId.value !== 'undefined'
        && selectedEnvId.value
        && selectedEnvId.value !== 'undefined'
      ) {
        await trainingDatasStore.fetchTrainingDatas(
          selectedTenantId.value,
          selectedEnvId.value
        )
      }

      // Only check for unreflected documents if we have valid tenant and env IDs
      if (
        selectedTenantId.value
        && selectedTenantId.value !== 'undefined'
        && selectedEnvId.value
        && selectedEnvId.value !== 'undefined'
      ) {
        await trainingDatasStore.checkIfHasAnyUnreflected(
          selectedTenantId.value,
          selectedEnvId.value
        )
      }

      appConfig.ui.primary = settingsStore.currentColorPrimary || 'sky'
    }
  }

  watch([selectedTenantId, selectedEnvId], async () => {
    ragsStore.llmRagChatbot?.reload({
      tenantId: selectedTenantId.value,
      envId: selectedEnvId.value
    })
  })

  return {
    initialApp,
    loadings,
    selectedEnvId,
    selectedTenantId,
    selectedEnv,
    isSelectedEnvIsProd
  }
}

export const useApp = createSharedComposable(_useApp)

export function useI18nDebug() {
  const { t } = useI18n()
  
  function getMessages() {
    // @ts-ignore - Access the internal messages object
    return t.messages.value
  }
  
  function getTranslation(key: string) {
    return t(key)
  }
  
  function hasTranslation(key: string) {
    const translation = t(key)
    return translation !== key
  }
  
  return {
    getMessages,
    getTranslation,
    hasTranslation
  }
}

import { PERMISSIONS } from '~/stores/permissions'
import { UserType } from '~/types/index.d'

export const useRoleBasedNavigators = () => {
  const { hasPermission } = useAppPermissions()
  const navigators = useNavigators()
  const authStore = useAuthStore()
  const { userRole } = storeToRefs(authStore)

  // Filter admin settings navigators based on permissions
  const roleBasedAdminSettingsNavigators = computed(() => {
    const adminNavigators = navigators.adminSettingsNavigators.value(true)

    return adminNavigators.filter((nav) => {
      // Special case: operator users menu is only for PNL_ADMIN (operators)
      if (nav.id === 'tenantId-env-settings-operator-users') {
        return userRole.value === UserType.PNL_ADMIN
      }

      // Map navigation items to required permissions
      const permissionMap: Record<string, string | string[]> = {
        'tenantId-env-settings-tenants': PERMISSIONS.VIEW_TENANTS,
        'tenantId-env-settings-users': PERMISSIONS.VIEW_USERS,
        'tenantId-env-settings-user-groups': PERMISSIONS.VIEW_USER_GROUPS,
        'tenantId-env-settings-page-permissions': [PERMISSIONS.VIEW_ADMIN_SETTINGS, PERMISSIONS.VIEW_USERS], // Allow both operators and admins
        'tenantId-env-settings-ip-address-control': PERMISSIONS.VIEW_IP_ADDRESS_CONTROL, // Allow both operators and admins
        'tenantId-env-settings-system': PERMISSIONS.VIEW_SYSTEM_SETTINGS
      }

      const requiredPermission = permissionMap[nav.id]
      if (!requiredPermission) return true // If no mapping, show by default

      // Handle both string and string array permissions
      if (Array.isArray(requiredPermission)) {
        // If any of the permissions is granted, show the menu item
        return requiredPermission.some(permission => hasPermission(permission))
      } else {
        return hasPermission(requiredPermission)
      }
    })
  })

  // Filter settings navigators based on permissions
  const roleBasedSettingsNavigators = computed(() => {
    const settingsNavs = navigators.settingsNavigators.value(true)

    return settingsNavs.filter((nav) => {
      // Map navigation items to required permissions
      const permissionMap: Record<string, string> = {
        'tenantId-env-settings-basic': PERMISSIONS.VIEW_BASIC_SETTINGS,
        'tenantId-env-settings-users': PERMISSIONS.VIEW_USERS,
        'tenantId-env-settings-labels': PERMISSIONS.VIEW_LABELS,
        'tenantId-env-settings-categories': PERMISSIONS.VIEW_CATEGORIES,
        'tenantId-env-settings-prompts': PERMISSIONS.VIEW_CHATBOT_SETTINGS,
        'tenantId-env-settings-system': PERMISSIONS.VIEW_SYSTEM_SETTINGS
      }

      const requiredPermission = permissionMap[nav.id]
      if (!requiredPermission) return true // If no mapping, show by default

      return hasPermission(requiredPermission)
    })
  })

  // Filter training data navigators based on permissions
  const roleBasedTrainingDataNavigators = computed(() => {
    const trainingDataNavs = navigators.trainingDataNavigators.value

    // Only show if user has permission to view training data
    if (!hasPermission(PERMISSIONS.VIEW_TRAINING_DATA)) {
      return []
    }

    return trainingDataNavs
  })

  // Role-based footer navigators
  const roleBasedFooterNavigators = computed(() => {
    // Get the base navigators
    const baseFooterNavigators = navigators.footerNavigators.value

    // If user is staff, remove the settings menu
    // Make sure to check userRole.value exists before comparing
    if (userRole.value && userRole.value === UserType.STAFF) {
      return baseFooterNavigators.filter(nav => nav.id !== 'settings')
    }

    return baseFooterNavigators
  })

  // Role-based connect navigators
  const roleBasedConnectNavigators = computed(() => {
    // Get the base navigators
    const baseConnectNavigators = navigators.connectNavigators.value

    // If user is staff, remove the connect menu
    // Make sure to check userRole.value exists before comparing
    if (userRole.value && userRole.value === UserType.STAFF) {
      return []
    }

    return baseConnectNavigators
  })

  // Role-based support navigators - available to all users
  const roleBasedSupportNavigators = computed(() => {
    return navigators.supportNavigators.value
  })



  return {
    ...navigators,
    roleBasedAdminSettingsNavigators,
    roleBasedSettingsNavigators,
    roleBasedTrainingDataNavigators,
    roleBasedFooterNavigators,
    roleBasedConnectNavigators,
    roleBasedSupportNavigators
  }
}

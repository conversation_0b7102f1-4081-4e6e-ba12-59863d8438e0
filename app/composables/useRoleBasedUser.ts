import { UserType } from '~/types/index.d'
import type { 
  RoleBasedUser, 
  CreateRoleBasedUserPayload, 
  UpdateRoleBasedUserPayload 
} from '~/types/role-based-user'
import { PERMISSIONS } from '~/stores/permissions'

/**
 * Composable for working with role-based users
 */
export const useRoleBasedUser = () => {
  const authStore = useAuthStore()
  const { user } = storeToRefs(authStore)
  
  /**
   * Check if the current user has a specific role
   * @param role The role to check
   * @returns True if the user has the specified role
   */
  const hasRole = (role: UserType): boolean => {
    return user.value?.role === role
  }
  
  /**
   * Check if the current user has a specific permission
   * @param permission The permission to check
   * @returns True if the user has the specified permission
   */
  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false
    return user.value.permissions?.includes(permission) || false
  }
  
  /**
   * Check if the current user has any of the specified permissions
   * @param permissions Array of permissions to check
   * @returns True if the user has any of the specified permissions
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user.value) return false
    return permissions.some(permission => hasPermission(permission))
  }
  
  /**
   * Check if the current user has all of the specified permissions
   * @param permissions Array of permissions to check
   * @returns True if the user has all of the specified permissions
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!user.value) return false
    return permissions.every(permission => hasPermission(permission))
  }
  
  /**
   * Check if the current user belongs to a specific group
   * @param groupId The group ID to check
   * @returns True if the user belongs to the specified group
   */
  const belongsToGroup = (groupId: string): boolean => {
    if (!user.value) return false
    return user.value.groups?.includes(groupId) || false
  }
  
  /**
   * Get the current user's role
   * @returns The user's role or null if not authenticated
   */
  const getUserRole = (): UserType | null => {
    return user.value?.role || null
  }
  
  /**
   * Get the current user's permissions
   * @returns Array of the user's permissions or empty array if not authenticated
   */
  const getUserPermissions = (): string[] => {
    return user.value?.permissions || []
  }
  
  /**
   * Get the current user's groups
   * @returns Array of the user's groups or empty array if not authenticated
   */
  const getUserGroups = (): string[] => {
    return user.value?.groups || []
  }
  
  /**
   * Check if the current user is an administrator
   * @returns True if the user is an administrator
   */
  const isAdmin = (): boolean => {
    return hasRole(UserType.ADMIN) || hasRole(UserType.PNL_ADMIN)
  }
  
  /**
   * Check if the current user is a staff member
   * @returns True if the user is a staff member
   */
  const isStaff = (): boolean => {
    return hasRole(UserType.STAFF)
  }
  
  /**
   * Check if the current user is a guest
   * @returns True if the user is a guest
   */
  const isGuest = (): boolean => {
    return hasRole(UserType.GUEST)
  }
  
  /**
   * Check if the current user is an operator
   * @returns True if the user is an operator
   */
  const isOperator = (): boolean => {
    return hasRole(UserType.PNL_ADMIN)
  }
  
  /**
   * Get the current user
   * @returns The current user or null if not authenticated
   */
  const getCurrentUser = (): RoleBasedUser | null => {
    return user.value
  }
  
  return {
    // Role checks
    hasRole,
    isAdmin,
    isStaff,
    isGuest,
    isOperator,
    
    // Permission checks
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    
    // Group checks
    belongsToGroup,
    
    // Getters
    getUserRole,
    getUserPermissions,
    getUserGroups,
    getCurrentUser,
    
    // Constants
    PERMISSIONS,
    UserType
  }
}

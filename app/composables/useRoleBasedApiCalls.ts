import { UserType } from '~/types/index.d'
import type { RoleBasedEndpoints } from '~/utils/role-based-api'
import { useRoleBasedApi } from '~/utils/role-based-api'

/**
 * Composable for common role-based API calls
 * This provides a reusable way to make role-based API calls for common entities
 */
export function useRoleBasedApiCalls() {
  const roleBasedApi = useRoleBasedApi()
  const { selectedTenantId } = useApp()
  const authStore = useAuthStore()
  const { userRole } = storeToRefs(authStore)

  /**
   * Check if the current user is an operator (PNL_ADMIN)
   */
  const isOperator = computed(() => userRole.value === UserType.PNL_ADMIN)

  /**
   * Get the current tenant ID or null if not available
   * For operators, this can be null on first login
   */
  const getCurrentTenantId = (): string | null => {
    // If user is an operator and tenant ID is not set, return null
    // This will trigger the fallback to the operator endpoint
    if (isOperator.value && (!selectedTenantId.value || selectedTenantId.value === 'undefined')) {
      console.info('Operator user with no tenant selected, using operator-specific endpoints')
      return null
    }

    // For non-operators, ensure we have a valid tenant ID
    if (!isOperator.value && (!selectedTenantId.value || selectedTenantId.value === 'undefined')) {
      console.warn('Non-operator user with no tenant selected, this may cause issues')
    }

    return selectedTenantId.value
  }

  // Note: We may need to add a getCurrentEnvId function in the future
  // when we need to include env_id in API calls

  /**
   * Make a role-based API call for tenants
   */
  const tenants = {
    /**
     * Fetch tenants based on user role
     * - Operators can see all tenants using /v2/tenants/all
     * - Admins and staff can only see their assigned tenant using /v2/tenants/{tenant_id}
     */
    async fetch<T = any>() {
      // For operators, use the /v2/tenants/all endpoint
      // For non-operators, use the /v2/tenants/{tenantId} endpoint
      return roleBasedApi.call<T>({
        operator: '/v2/tenants/all',
        admin: '/v2/tenants/{tenantId}',
        staff: '/v2/tenants/{tenantId}',
        default: '/v2/tenants/{tenantId}',
        params: {
          tenantId: getCurrentTenantId()
        },
        transform: (data) => {
          // If the response is for a single tenant, wrap it in an array
          if (!data.tenants && data.id) {
            return {
              tenants: [data],
              total: 1,
              page: 1,
              page_size: 1
            }
          }
          return data
        }
      })
    },

    /**
     * Create a tenant based on user role
     * - Operators can create tenants directly
     * - Other roles need to request tenant creation
     */
    async create<T = any>(payload: any) {
      return roleBasedApi.call<T>({
        operator: '/v2/tenants',
        admin: '/v2/tenants/request',
        staff: '/v2/tenants/request',
        default: '/v2/tenants/request',
        // Add params for potential future use
        params: {
          tenantId: getCurrentTenantId()
        },
        transform: data => data
      }, 'post', payload)
    },

    /**
     * Update a tenant based on user role
     * - Operators can update any tenant
     * - Admins can only update their assigned tenant
     * - Staff cannot update tenants
     */
    async update<T = any>(id: string, payload: any) {
      return roleBasedApi.call<T>({
        operator: '/v2/tenants/{tenantId}',
        admin: '/v2/tenants/{tenantId}',
        staff: '/v2/tenants/{tenantId}/request-update',
        default: '/v2/tenants/{tenantId}/request-update',
        params: {
          tenantId: id || getCurrentTenantId()
        },
        transform: data => data
      }, 'put', payload)
    },

    /**
     * Delete a tenant based on user role
     * - Only operators can delete tenants
     * - Other roles need to request tenant deletion
     */
    async delete<T = any>(id: string) {
      return roleBasedApi.call<T>({
        operator: '/v2/tenants/{tenantId}',
        admin: '/v2/tenants/{tenantId}/request-deletion',
        staff: '/v2/tenants/{tenantId}/request-deletion',
        default: '/v2/tenants/{tenantId}/request-deletion',
        params: {
          tenantId: id || getCurrentTenantId()
        }
      }, 'delete')
    }
  }

  /**
   * Make a role-based API call for users
   */
  const users = {
    /**
     * Fetch users based on user role
     * - Operators can see all users
     * - Admins can see users in their tenant
     * - Staff can only see their own user
     */
    async fetch<T = any>() {
      return roleBasedApi.call<T>({
        operator: '/v2/users/all',
        admin: '/v2/users/tenant/{tenantId}',
        staff: '/v2/users/me',
        default: '/v2/users/me',
        params: {
          tenantId: getCurrentTenantId()
        },
        transform: (data) => {
          // If the response is for a single user, wrap it in an array
          if (!data.users && data.id) {
            return {
              users: [data],
              total: 1,
              page: 1,
              page_size: 1
            }
          }
          return data
        }
      })
    },

    /**
     * Create a user based on user role
     * - Operators can create users in any tenant
     * - Admins can create users in their tenant
     * - Staff cannot create users
     */
    async create<T = any>(payload: any) {
      return roleBasedApi.call<T>({
        operator: '/v2/users',
        admin: '/v2/users/tenant/{tenantId}',
        staff: '/v2/users/request',
        default: '/v2/users/request',
        params: {
          tenantId: getCurrentTenantId()
        },
        transform: data => data
      }, 'post', payload)
    }
  }

  /**
   * Make a role-based API call for environments
   */
  const environments = {
    /**
     * Fetch environments based on user role
     * - Operators can see all environments
     * - Admins and staff can only see environments in their tenant
     */
    async fetch<T = any>(tenantId?: string) {
      return roleBasedApi.call<T>({
        operator: tenantId ? '/v2/tenants/{tenantId}/all' : '/v2/tenants/all',
        admin: '/v2/tenants/{tenantId}/all',
        staff: '/v2/tenants/{tenantId}/all',
        default: '/v2/tenants/{tenantId}/all',
        params: {
          tenantId: tenantId || getCurrentTenantId()
        }
      })
    },

    /**
     * Create an environment based on user role
     * - Operators can create environments in any tenant
     * - Admins can create environments in their tenant
     * - Staff cannot create environments
     */
    async create<T = any>(tenantId: string, payload: any): Promise<T> {
      return roleBasedApi.call<T>({
        operator: '/v2/tenants/{tenantId}/env',
        admin: '/v2/tenants/{tenantId}/env',
        staff: '/v2/tenants/{tenantId}/env/request',
        default: '/v2/tenants/{tenantId}/env/request',
        params: {
          tenantId: tenantId || getCurrentTenantId()
        }
      }, 'post', payload)
    },

    /**
     * Update an environment based on user role
     * - Operators can update environments in any tenant
     * - Admins can update environments in their tenant
     * - Staff cannot update environments
     */
    async update<T = any>(tenantId: string, envId: string, payload: any): Promise<T> {
      return roleBasedApi.call<T>({
        operator: '/v2/tenants/{tenantId}/env/{envId}',
        admin: '/v2/tenants/{tenantId}/env/{envId}',
        staff: '/v2/tenants/{tenantId}/env/{envId}/request-update',
        default: '/v2/tenants/{tenantId}/env/{envId}/request-update',
        params: {
          tenantId: tenantId || getCurrentTenantId(),
          envId
        }
      }, 'put', payload)
    },

    /**
     * Delete an environment based on user role
     * - Operators can delete environments in any tenant
     * - Admins can delete environments in their tenant
     * - Staff cannot delete environments
     */
    async delete<T = any>(tenantId: string, envId: string): Promise<T> {
      return roleBasedApi.call<T>({
        operator: '/v2/tenants/{tenantId}/env/{envId}',
        admin: '/v2/tenants/{tenantId}/env/{envId}',
        staff: '/v2/tenants/{tenantId}/env/{envId}/request-deletion',
        default: '/v2/tenants/{tenantId}/env/{envId}/request-deletion',
        params: {
          tenantId: tenantId || getCurrentTenantId(),
          envId
        }
      }, 'delete')
    }
  }

  /**
   * Make a custom role-based API call
   * @param endpoints Object containing endpoints for different roles
   * @param method HTTP method to use
   * @param data Optional data to send with the request
   * @param service API service to use
   */
  async function custom<T = any>(
    endpoints: RoleBasedEndpoints<T>,
    method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
    data?: any,
    service: 'adminService' | 'authService' | 'ragService' | 'reportService' = 'adminService'
  ): Promise<T> {
    return roleBasedApi.call<T>(endpoints, method, data, service)
  }

  return {
    tenants,
    users,
    environments,
    custom
  }
}

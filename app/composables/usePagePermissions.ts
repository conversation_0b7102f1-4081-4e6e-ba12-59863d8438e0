import type { PagePermission } from '~/types/page-permission'

/**
 * Composable for working with page permissions
 */
export const usePagePermissions = () => {
  const pagePermissionsStore = usePagePermissionsStore()
  const { hasPermission, hasAnyPermission } = useAppPermissions()
  const route = useRoute()
  
  /**
   * Check if a page is accessible based on permissions and enabled state
   * @param page The page to check
   * @returns True if the page is accessible
   */
  const isPageAccessible = (page: PagePermission): boolean => {
    // Check if the page is enabled
    if (!page.enabled) {
      return false
    }
    
    // Check if the user has the required permissions
    return hasAnyPermission(page.requiredPermissions)
  }
  
  /**
   * Check if the current route is accessible
   * @returns True if the current route is accessible
   */
  const isCurrentRouteAccessible = (): boolean => {
    const currentPath = route.path
    
    // Find the page that matches the current path
    const page = pagePermissionsStore.pagePermissions.find(p => {
      // Check if the route matches the page path
      // We need to handle dynamic routes, so we'll use a simple check
      // that the route starts with the page path
      const routeParts = currentPath.split('/')
      const pageParts = p.path.split('/')
      
      // If the page path has fewer parts than the route, it might be a parent route
      if (pageParts.length <= routeParts.length) {
        // Check if all parts of the page path match the corresponding parts of the route
        for (let i = 0; i < pageParts.length; i++) {
          // Skip dynamic parts (those starting with :)
          if (pageParts[i].startsWith(':')) {
            continue
          }
          
          // If any part doesn't match, this is not the right page
          if (pageParts[i] !== routeParts[i]) {
            return false
          }
        }
        
        // All parts matched, this is the right page
        return true
      }
      
      return false
    })
    
    // If no matching page is found, allow access by default
    if (!page) {
      return true
    }
    
    // Check if the page is accessible
    return isPageAccessible(page)
  }
  
  /**
   * Get all accessible pages
   * @returns Array of accessible pages
   */
  const getAccessiblePages = (): PagePermission[] => {
    return pagePermissionsStore.pagePermissions.filter(isPageAccessible)
  }
  
  /**
   * Get accessible pages by category
   * @param categoryId The category ID
   * @returns Array of accessible pages in the category
   */
  const getAccessiblePagesByCategory = (categoryId: string): PagePermission[] => {
    return pagePermissionsStore.pagePermissions.filter(
      page => page.category === categoryId && isPageAccessible(page)
    )
  }
  
  return {
    isPageAccessible,
    isCurrentRouteAccessible,
    getAccessiblePages,
    getAccessiblePagesByCategory
  }
}

/**
 * API エラーハンドリング用のコンポーザブル
 * i18n対応のエラーメッセージ表示機能を提供
 */
export function useApiErrorHandler() {
  // i18nを安全に取得する関数
  const getTranslation = (key: string, params?: any): string => {
    try {
      const { t: $t } = useNuxtApp().$i18n
      return ($t as any)(key, params) as string
    } catch {
      // フォールバック: キーをそのまま返す
      return key
    }
  }

  const toast = useToast()

  /**
   * HTTPステータスコードに基づいてエラーメッセージを取得
   * @param status HTTPステータスコード
   * @returns 翻訳されたエラーメッセージ
   */
  const getErrorMessageByStatus = (status: number): string => {
    const statusKey = status.toString()

    // 特定のステータスコードに対応するメッセージがあるかチェック
    const specificMessage = getTranslation(`errors.api.${statusKey}`)
    if (specificMessage !== `errors.api.${statusKey}`) {
      return specificMessage
    }

    // ステータスコード範囲による分類
    if (status >= 400 && status < 500) {
      // クライアントエラー
      switch (status) {
        case 400:
          return getTranslation('errors.api.400')
        case 401:
          return getTranslation('errors.api.401')
        case 403:
          return getTranslation('errors.api.403')
        case 404:
          return getTranslation('errors.api.404')
        case 409:
          return getTranslation('errors.api.409')
        case 422:
          return getTranslation('errors.api.422')
        case 429:
          return getTranslation('errors.api.429')
        default:
          return getTranslation('errors.api.default')
      }
    } else if (status >= 500) {
      // サーバーエラー
      switch (status) {
        case 500:
          return getTranslation('errors.api.500')
        case 502:
          return getTranslation('errors.api.502')
        case 503:
          return getTranslation('errors.api.503')
        case 504:
          return getTranslation('errors.api.504')
        default:
          return getTranslation('errors.api.500')
      }
    }

    return getTranslation('errors.api.default')
  }

  /**
   * エラーレスポンスからメッセージを抽出
   * @param errorResponse エラーレスポンス
   * @returns エラーメッセージ
   */
  const extractErrorMessage = (errorResponse: any): string | null => {
    if (errorResponse?.data?.error_base?.template) {
      return getTranslation(
        errorResponse.data.error_base.template,
        errorResponse.data.error_base.params
      )
    }

    // サーバーから返されたエラーメッセージを優先
    if (errorResponse?.data?.error_message) {
      return getTranslation(errorResponse.data.error_message)
    }

    if (errorResponse?.data?.message) {
      return errorResponse.data.message
    }

    // エラーコードがある場合は翻訳を試行
    if (errorResponse?.data?.error_code) {
      const errorCode = errorResponse.data.error_code
      const translatedMessage = getTranslation(`errors.login.${errorCode}`)
      if (translatedMessage !== `errors.login.${errorCode}`) {
        return translatedMessage
      }
    }

    // レスポンスデータがstring型の場合
    if (typeof errorResponse?.data === 'string') {
      // 特定の英語エラーメッセージを翻訳
      const englishMessage = errorResponse.data
      const translatedMessage = getTranslation(englishMessage)
      if (translatedMessage !== englishMessage) {
        return translatedMessage
      }
      return englishMessage
    }

    return null
  }

  /**
   * ネットワークエラーかどうかを判定
   * @param error エラーオブジェクト
   * @returns ネットワークエラーかどうか
   */
  const isNetworkError = (error: any): boolean => {
    return !error.response && error.request
  }

  /**
   * タイムアウトエラーかどうかを判定
   * @param error エラーオブジェクト
   * @returns タイムアウトエラーかどうか
   */
  const isTimeoutError = (error: any): boolean => {
    return error.code === 'ECONNABORTED' || error.message?.includes('timeout')
  }

  /**
   * エラーに基づいてトーストメッセージを表示
   * @param error エラーオブジェクト
   * @param options 表示オプション
   */
  const showErrorToast = (
    error: any,
    options: {
      title?: string
      timeout?: number
      showDetails?: boolean
    } = {}
  ) => {
    const { title = 'エラー', timeout = 30000 } = options

    let description: string
    let status: number | null = null

    if (isNetworkError(error)) {
      description = getTranslation('errors.api.network')
    } else if (isTimeoutError(error)) {
      description = getTranslation('errors.api.timeout')
    } else if (error.response) {
      status = error.response.status
      const extractedMessage = extractErrorMessage(error.response)

      if (extractedMessage) {
        description = extractedMessage
      } else {
        description = getErrorMessageByStatus(status!)
      }
    } else {
      description = error.message || getTranslation('errors.api.unknown')
    }

    // ステータスコードを含むタイトル
    const finalTitle = status ? `${title}: ${status}` : title

    toast.add({
      id: 'api-error',
      title: finalTitle,
      description,
      color: 'red',
      timeout,
      icon: 'i-heroicons-exclamation-circle'
    })
  }

  /**
   * 特定のステータスコードを無視するかどうかを判定
   * @param status HTTPステータスコード
   * @param ignoredStatuses 無視するステータスコードの配列
   * @returns 無視するかどうか
   */
  const shouldIgnoreError = (
    status: number,
    ignoredStatuses: number[] = []
  ): boolean => {
    return ignoredStatuses.includes(status)
  }

  /**
   * エラーハンドリングの設定オプション
   */
  interface ErrorHandlerOptions {
    showToast?: boolean
    ignoredStatuses?: number[]
    customTitle?: string
    timeout?: number
  }

  /**
   * 統合エラーハンドラー
   * @param error エラーオブジェクト
   * @param options ハンドリングオプション
   */
  const handleApiError = (error: any, options: ErrorHandlerOptions = {}) => {
    const {
      showToast = true,
      ignoredStatuses = [],
      customTitle,
      timeout = 30000
    } = options

    const status = error.response?.status

    // 無視するステータスコードの場合は何もしない
    if (status && shouldIgnoreError(status, ignoredStatuses)) {
      return
    }

    // トーストメッセージを表示
    if (showToast) {
      showErrorToast(error, {
        title: customTitle,
        timeout
      })
    }

    // コンソールにエラーログを出力
    console.error('API Error:', {
      status,
      url: error.config?.url,
      method: error.config?.method,
      message: error.message,
      response: error.response?.data
    })
  }

  return {
    getErrorMessageByStatus,
    extractErrorMessage,
    isNetworkError,
    isTimeoutError,
    showErrorToast,
    shouldIgnoreError,
    handleApiError
  }
}

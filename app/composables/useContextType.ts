import { createSharedComposable } from '@vueuse/core'

const _useContextType = () => {
  const contextTypeList = [
    {
      label: 'PDF',
      value: 'application/pdf',
      icon: contextTypeIcon('application/pdf')
    },
    {
      label: 'TXT',
      value: 'text/plain',
      icon: contextTypeIcon('text/plain')
    },
    {
      label: 'JSON',
      value: 'application/json',
      icon: contextTypeIcon('application/json')
    },
    {
      label: 'CSV',
      value: 'text/csv',
      icon: contextTypeIcon('text/csv')
    },
    {
      label: 'Excel',
      value: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      icon: contextTypeIcon('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    },
    {
      label: 'Markdown',
      value: 'text/markdown',
      icon: contextTypeIcon('text/markdown')
    },
    {
      label: 'DOCX',
      value: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      icon: contextTypeIcon('application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    },
    {
      label: 'PPTX',
      value: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      icon: contextTypeIcon('application/vnd.openxmlformats-officedocument.presentationml.presentation')
    },
    {
      label: 'WEB',
      value: 'WEBPAGE',
      icon: contextTypeIcon('http')
    }
  ]

  return {
    contextTypeList
  }
}

export const useContextType = createSharedComposable(_useContextType)

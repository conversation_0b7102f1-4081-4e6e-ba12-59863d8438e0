<script setup lang="ts">
const { selectedTenantId, selectedEnvId } = useApp()

const categoriesStore = useCategoriesStore()
const { categories: rowsOptions, loadings } = storeToRefs(categoriesStore)

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const categoryModel = computed({
  get: () => {
    return rowsOptions.value?.filter(option =>
      props.modelValue.includes(option.category)
    )
  },
  set: async (categories) => {
    const promises = categories.map(async (row: any) => {
      if (rowsOptions.value?.some(option => option.category === row.category)) {
        return row
      }

      // In a real app, you would make an API call to create the label
      const newLabel = await categoriesStore.createCategory(
        selectedTenantId.value,
        selectedEnvId.value,
        {
          category: row.category
        }
      )

      return newLabel
    })

    const newcategories = await Promise.all(promises)
    emit(
      'update:modelValue',
      newcategories.map((row: any) => row.category || row)
    )
  }
})

onMounted(() => {
  categoriesStore.fetchCategories(selectedTenantId.value, selectedEnvId.value)
})
</script>

<template>
  <USelectMenu
    v-model="categoryModel"
    by="category"
    name="categories"
    :options="rowsOptions"
    multiple
    searchable
    creatable
    searchable-placeholder="カテゴリを検索"
    :loading="loadings.fetchcategories"
    :ui-menu="{
      width: 'min-w-72'
    }"
  >
    <template #label>
      <template v-if="categoryModel?.length">
        <span class="flex items-center -space-x-1">
          <span
            v-for="row of categoryModel"
            :key="row.key"
            class="flex-shrink-0 w-2 h-2 mt-px rounded-full"
            :style="{ background: `#${generateColorFromString(row.category)}` }"
          />
        </span>
        <span class="truncate max-w-40">{{ categoryModel.map((row) => row.category).join(", ") }}
        </span>
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate">
          カテゴリを選択
        </span>
      </template>
    </template>

    <template #option="{ option }">
      <span
        class="flex-shrink-0 w-2 h-2 mt-px rounded-full"
        :style="{ background: `#${generateColorFromString(option.category)}` }"
      />
      <span class="truncate">{{ option.category }}</span>
    </template>

    <template #option-create="{ option }">
      <span class="flex-shrink-0">新規カテゴリ:</span>
      <span class="block truncate">{{ option.category }}</span>
    </template>
  </USelectMenu>
</template>

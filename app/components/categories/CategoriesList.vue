<script setup lang="ts">
const { selectedTenantId, selectedEnvId } = useApp()

const categoriesStore = useCategoriesStore()
const { categories: options, loadings } = storeToRefs(categoriesStore)

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const models = computed(() => {
  return options.value?.filter(option =>
    props.modelValue.includes(option.label)
  )
})

onMounted(() => {
  categoriesStore.fetchCategories(selectedTenantId.value, selectedEnvId.value)
})
</script>

<template>
  <div
    v-if="model?.length"
    class="flex flex-wrap gap-1"
  >
    <span
      v-for="label of models"
      :key="label.key"
      class="flex items-center gap-1"
    >
      <span
        class="flex-shrink-0 w-2 h-2 mt-px rounded-full"
        :style="{ background: `#${generateColorFromString(label.key)}` }"
      />
      <span class="text-xs mr-2">
        {{ label.label }}
      </span>
    </span>
  </div>
  <div v-else>
    <span class="text-xs text-gray-500 dark:text-gray-400 truncate">
      ラベルなし
    </span>
  </div>
</template>

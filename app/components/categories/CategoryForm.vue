<script setup lang="ts">
import type { FormError, FormSubmitEvent } from '#ui/types'

const emit = defineEmits(['close', 'submit'])
const props = defineProps<{
  category?: string
  categoryId?: number
  loading?: boolean
  error: any
}>()

const state = reactive({
  category: '',
  category_id: null as number | null
})

const validate = (state: any): FormError[] => {
  const errors = []
  if (!state.category)
    errors.push({ path: 'category', message: 'カテゴリが必要です。' })
  return errors
}

async function onSubmit(event: FormSubmitEvent<any>) {
  // Do something with data
  emit('submit', event.data)
  // emit('close')
}

onMounted(() => {
  if (props.categoryId) {
    state.category_id = props.categoryId
  }
  if (props.category) {
    state.category = props.category
  }
})

const formRef = ref()
// watch props.error
watch(
  () => props.error,
  (error) => {
    if (error) {
      formRef.value?.setErrors([
        { path: 'category', message: error?.error_message }
      ])
    }
  }
)
</script>

<template>
  <UForm
    ref="formRef"
    :validate="validate"
    :validate-on="['submit']"
    :state="state"
    class="space-y-4"
    @submit="onSubmit"
  >
    <UFormGroup
      label="カテゴリ"
      name="category"
      required
    >
      <UInput
        v-model="state.category"
        type="text"
        placeholder="例: 税金"
        autofocus
      />
    </UFormGroup>

    <BasicFormButtonGroup
      class="justify-end"
      :loading="props.loading"
      @close="emit('close')"
    />
  </UForm>
</template>

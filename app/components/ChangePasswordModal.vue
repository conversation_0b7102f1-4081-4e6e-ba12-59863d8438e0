<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const model = defineModel({
  type: Boolean
})

const authStore = useAuthStore()
const { loadings } = storeToRefs(authStore)
const toast = useToast()

// Zod schema for password validation
const schema = z
  .object({
    currentPassword: z.string().min(1, '現在のパスワードは必須です'),
    newPassword: z
      .string({
        required_error: '新しいパスワードは必須です'
      })
      .nonempty('新しいパスワードは必須です')
      .min(8, 'パスワードは8文字以上である必要があります')
      .max(128, 'パスワードは128文字以下である必要があります')
      .regex(/[A-Z]/, 'パスワードには少なくとも1つの大文字を含めてください')
      .regex(/[a-z]/, 'パスワードには少なくとも1つの小文字を含めてください')
      .regex(/[0-9]/, 'パスワードには少なくとも1つの数字を含めてください')
      .regex(
        /[!@#$%^&*(),.?":{}|<>]/,
        'パスワードには少なくとも1つの特殊文字を含めてください'
      )
      .regex(
        /^(?! ).*(?<! )$/,
        'パスワードには先頭・末尾のスペースを含めないでください'
      ),
    confirmPassword: z.string().min(1, 'パスワードの確認は必須です')
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'パスワードが一致しません',
    path: ['confirmPassword']
  })

type Schema = z.output<typeof schema>

const state = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const form = ref<any>(null)

const passwordRequirements = computed(() => {
  const { newPassword } = state

  return [
    {
      label: '長さ：8文字以上',
      valid: newPassword?.length >= 8
    },
    {
      label: '英大文字（最低1文字）',
      valid: /[A-Z]/.test(newPassword)
    },
    {
      label: '英小文字（最低1文字）',
      valid:
        /[a-z]/.test(newPassword) && newPassword !== newPassword?.toUpperCase()
    },
    {
      label: '数字（最低1文字）',
      valid: /[0-9]/.test(newPassword)
    },
    {
      label: '特殊文字（最低1文字）',
      valid: /[!@#$%^&*(),.?":{}|<>]/.test(newPassword)
    },
    {
      label: '先頭・末尾にスペースなし',
      valid: /^(?! ).*(?<! )$/.test(newPassword)
    }
  ]
})

const authError = ref('')
async function onSubmit(event: FormSubmitEvent<Schema>) {
  try {
    authError.value = ''
    const result = await authStore.changePassword(
      event.data.currentPassword,
      event.data.newPassword
    )
    console.log('🚀 ~ onSubmit ~ result:', result)

    if (result) {
      toast.add({
        id: 'success',
        title: '成功',
        description: 'パスワードが正常に変更されました',
        color: 'green'
      })
      model.value = false
      // Reset form
      state.currentPassword = ''
      state.newPassword = ''
      state.confirmPassword = ''
    } else {
      authError.value
        = '現在のパスワードが正しいか確認してください。'
    }
  } catch {
    toast.add({
      id: 'error',
      title: 'エラー',
      description: 'パスワードの変更中にエラーが発生しました',
      color: 'red'
    })
  }
}

function onCancel() {
  model.value = false
  // Reset form
  state.currentPassword = ''
  state.newPassword = ''
  state.confirmPassword = ''
}
</script>

<template>
  <UDashboardModal
    v-model="model"
    data-tour="change-password-modal"
    title="パスワード変更"
    description="現在のパスワードと新しいパスワードを入力してください"
    icon="i-heroicons-key"
    :ui="{
      width: 'sm:max-w-md',
      icon: {
        base: 'text-primary-500 dark:text-primary-400'
      }
    }"
  >
    <UForm
      ref="form"
      :schema="schema"
      :state="state"
      class="space-y-4"
      @submit="onSubmit"
    >
      <UFormGroup
        name="currentPassword"
        label="現在のパスワード"
        required
        :error="authError"
      >
        <BasePasswordInput
          v-model="state.currentPassword"
          placeholder="現在のパスワードを入力してください"
          autocomplete="current-password"
          aria-autocomplete="none"
          tabindex="1"
        />
      </UFormGroup>
      <div>
        <UFormGroup
          name="newPassword"
          label="新しいパスワード"
          required
        >
          <BasePasswordInput
            v-model="state.newPassword"
            placeholder="新しいパスワードを入力してください"
            autocomplete="new-password"
            aria-autocomplete="none"
            tabindex="2"
          />
        </UFormGroup>
        <div
          class="text-xs text-left pt-1"
          data-tour="password-requirements"
        >
          パスワードは下記の条件を満たす必要があります。
          <ul class="list-none pl-0 mt-2 space-y-1">
            <li
              v-for="requirement in passwordRequirements"
              :key="requirement.label"
            >
              <span
                :class="[
                  requirement.valid
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-500 dark:text-gray-400'
                ]"
                class="flex items-center gap-2"
              >
                <UIcon
                  :name="
                    requirement.valid
                      ? 'i-heroicons-check-circle-solid'
                      : 'i-heroicons-x-circle-solid'
                  "
                  :class="[
                    requirement.valid
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-400 dark:text-red-400'
                  ]"
                  class="w-4 h-4"
                />
                {{ requirement.label }}
              </span>
            </li>
          </ul>
        </div>
      </div>
      <UFormGroup
        name="confirmPassword"
        label="パスワードの確認"
        required
      >
        <BasePasswordInput
          v-model="state.confirmPassword"
          placeholder="新しいパスワードを再度入力してください"
          autocomplete="confirm-password"
          aria-autocomplete="none"
        />
      </UFormGroup>

      <div class="flex justify-end gap-3 w-full mt-6">
        <UButton
          color="gray"
          variant="ghost"
          @click="onCancel"
        >
          キャンセル
        </UButton>
        <UButton
          type="submit"
          :loading="loadings.changePassword"
          icon="i-heroicons-key"
        >
          パスワードを変更
        </UButton>
      </div>
    </UForm>
  </UDashboardModal>
</template>

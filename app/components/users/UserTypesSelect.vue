<script setup lang="ts">
import { USER_TYPES } from '~/constants/user-types'
import { UserType } from '~/types/index.d'

const props = defineProps({
  modelValue: {
    type: String,
    default: UserType.STAFF
  }
})

const userTypesOptions = computed(() => {
  return USER_TYPES.filter(type => ![UserType.GUEST, UserType.PNL_ADMIN].includes(type.id))
})

const emit = defineEmits(['update:modelValue'])

const selected = computed({
  get: () => props.modelValue,
  set: (userType: any) => {
    emit('update:modelValue', userType?.id)
  }
})
</script>

<template>
  <USelectMenu
    v-model="selected"
    :options="userTypesOptions"
    searchable
    creatable
    searchable-placeholder="ユーザ権限を検索"
    autocomplete="off"
    v-bind="$attrs"
  >
    <template #label>
      <template v-if="selected">
        <div class="flex items-center flex-row gap-1 text-xs">
          <UIcon
            :name="userTypeObject(selected as UserType).icon"
            class="w-5 h-5 text-gray-500 dark:text-gray-400"
          />
          <span class="truncate">{{
            userTypeObject(selected as UserType).label
          }}</span>
        </div>
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate">
          ユーザ権限を選択
        </span>
      </template>
    </template>

    <template #option="{ option }">
      <div class="flex items-center flex-row gap-1 text-xs">
        <UIcon
          :name="userTypeObject(option.id as UserType).icon"
          class="w-5 h-5 text-gray-500 dark:text-gray-400"
        />
        <span class="truncate">{{
          userTypeObject(option.id as UserType).label
        }}</span>
      </div>
    </template>
  </USelectMenu>
</template>

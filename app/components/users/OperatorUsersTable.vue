<script setup lang="ts">
const { hasPermission } = useAppPermissions()

const props = defineProps({
  users: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  lockedUsers: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  usersPagination: {
    type: Object,
    required: true
  },
  usersTotal: {
    type: Number,
    required: true
  },
  usersFilter: {
    type: Object,
    required: true
  },
  selectedUsers: {
    type: Array as PropType<any[]>,
    default: () => []
  }
})

const emit = defineEmits([
  'edit',
  'delete',
  'create',
  'refresh',
  'deleteMany',
  'update:selectedUsers',
  'releaseUserLock'
])

const { PERMISSIONS } = useAppPermissions()

function getItems(user: any) {
  const items = [[]] as any

  // Only add edit option if user has permission
  if (hasPermission(PERMISSIONS.EDIT_USER)) {
    items[0].push({
      label: 'PNL管理者ユーザ編集',
      click: () => emit('edit', user)
    })
  }

  // Add release user lock option if user is locked and has permission
  if (
    hasPermission(PERMISSIONS.EDIT_ADMIN_SETTINGS)
    && props.lockedUsers.includes(user.username)
  ) {
    items[0].push({
      label: 'ユーザーロック解除',
      click: () => emit('releaseUserLock', user.username)
    })
  }

  // Only add delete option if user has permission
  if (hasPermission(PERMISSIONS.DELETE_USER)) {
    items[0].push({
      label: 'PNL管理者ユーザ削除',
      labelClass: 'text-red-500 dark:text-red-400',
      click: () => emit('delete', user)
    })
  }
  return items
}

const defaultColumns = [
  {
    key: 'display_name',
    label: '名前',
    sortable: true
  },
  {
    key: 'username',
    label: 'アカウント情報',
    sortable: true
  },
  {
    key: 'user_type',
    label: '権限',
    sortable: true
  },
  {
    key: 'created_at',
    label: '作成日時',
    sortable: true
  },
  {
    key: 'actions',
    label: '操作',
    sortable: false,
    class: 'w-fit text-right'
  }
]

const items = [
  [
    {
      label: '一括削除',
      icon: 'carbon:batch-job',
      click: () => {
        emit('deleteMany')
      }
    }
  ]
]
</script>

<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-3 !py-3' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700'
      },
      footer: { padding: 'p-4' }
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
        >
          PNL管理者ユーザ一覧
          <UBadge
            v-if="usersTotal"
            :label="usersTotal"
          />
        </h2>
        <!-- Use slot for actions to allow parent component to control permissions -->
        <slot name="actions">
          <UButton
            label="PNL管理者ユーザ追加"
            icon="icomoon-free:user-plus"
            color="gray"
            size="sm"
            @click="emit('create')"
          />
        </slot>
      </div>
    </template>

    <div class="flex items-center justify-between gap-3 px-4 py-3">
      <div class="flex items-center gap-1.5">
        <UInput
          v-model="usersFilter.username"
          icon="i-heroicons-magnifying-glass-20-solid"
          placeholder="ユーザ名で検索..."
          size="sm"
          class="w-48"
        />
      </div>
      <div class="flex items-center gap-1.5">
        <div v-if="selectedUsers.length">
          <UDropdown
            :items="items"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              color="white"
              :label="`一括操作（${selectedUsers.length}件）`"
              icon="fluent:form-multiple-20-regular"
              trailing-icon="i-heroicons-chevron-down-20-solid"
              size="sm"
            />
          </UDropdown>
        </div>
        <UButton
          icon="prime:sync"
          color="gray"
          size="sm"
          @click="emit('refresh')"
        />
      </div>
    </div>

    <UTable
      v-if="users"
      :rows="users"
      :columns="defaultColumns"
      class="w-full"
      :ui="{
        divide: 'divide-gray-200 dark:divide-gray-800',
        tr: { base: 'group' }
      }"
      :loading="loading"
      @update:model-value="$emit('update:selectedUsers', $event)"
    >
      <template #created_at-data="{ row }">
        <span class="text-gray-500 dark:text-gray-400">
          {{ formatDateTime(row.created_at) }}
        </span>
      </template>
      <template #updated_at-data="{ row }">
        <span class="text-gray-500 dark:text-gray-400">
          {{ new Date(row.updated_at).toLocaleDateString() }}
        </span>
      </template>
      <template #username-data="{ row }">
        <div class="flex items-center gap-2">
          <UTooltip
            v-if="lockedUsers.includes(row.username)"
            text="このユーザはロックされています。"
            @click="emit('releaseUserLock', row.username)"
          >
            <UAvatar
              icon="heroicons:lock-closed"
              size="md"
              :ui="{
                icon: {
                  base: 'text-red-500 dark:text-red-400',
                  size: {
                    md: 'w-6 h-6'
                  }
                },
                rounded: 'rounded-lg'
              }"
              class="cursor-pointer"
            />
          </UTooltip>

          <div class="flex flex-col">
            <div class="font-medium text-gray-900 dark:text-white">
              {{ row.username }}
            </div>
            <div class="text-gray-500 dark:text-gray-400 text-sm">
              {{ row.email }}
            </div>
          </div>
        </div>
      </template>
      <template #user_type-data="{ row }">
        <UBadge
          :label="userTypeObject(row.user_type).label"
          :color="userTypeObject(row.user_type).color"
          :icon="userTypeObject(row.user_type).icon"
          variant="subtle"
          size="sm"
          :ui="{
            rounded: 'rounded-full'
          }"
        />
      </template>
      <template #lock_status-data="{ row }">
        <UBadge
          :label="
            !lockedUsers.includes(row.username) ? 'ロックなし' : 'ロック中'
          "
          :color="!lockedUsers.includes(row.username) ? 'green' : 'red'"
          :icon="
            !lockedUsers.includes(row.username)
              ? 'heroicons:lock-open'
              : 'heroicons:lock-closed'
          "
          variant="subtle"
          size="sm"
          :ui="{
            rounded: 'rounded-full'
          }"
        />
      </template>
      <template #actions-data="{ row }">
        <div class="flex flex-row items-end justify-end">
          <PermissionGuard
            :permissions="[PERMISSIONS.EDIT_USER, PERMISSIONS.DELETE_USER]"
          >
            <UDropdown
              class="hidden group-hover:block"
              :items="getItems(row)"
              :popper="{ placement: 'bottom-start' }"
            >
              <UButton
                class="row-menu"
                color="white"
                icon="charm:menu-meatball"
                size="xs"
                square
              />
            </UDropdown>
          </PermissionGuard>
        </div>
      </template>
    </UTable>

    <template #footer>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-1.5">
          <span class="text-sm leading-5">表示件数:</span>
          <USelect
            v-model="usersPagination.pageCount"
            :options="[10, 20, 50, 100]"
            class="w-20"
          />
        </div>

        <UPagination
          v-model="usersPagination.page"
          :page-count="usersPagination.pageCount"
          :total="usersTotal"
          size="sm"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline'
              }
            }
          }"
        />
      </div>
    </template>
  </UCard>
</template>

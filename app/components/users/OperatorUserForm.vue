<script setup lang="ts">
import { z } from 'zod'
import { UserType } from '~/types/index.d'
import type { FormSubmitEvent } from '#ui/types'

const formRef = ref()

const schema = z.object({
  email: z.string().email('有効なメールアドレスを入力してください'),
  username: z.string().min(1, 'ユーザ名は必須です'),
  password: z.string().min(6, 'パスワードは6文字以上で入力してください').optional(),
  display_name: z.string().optional(),
  user_type: z.enum([UserType.PNL_ADMIN])
})
type Schema = z.output<typeof schema>

const emit = defineEmits(['close', 'submit'])
const props = defineProps<{
  user?: Schema
  loading?: boolean
  error?: any
  email?: string
  display_name?: string
  label?: string
  username?: string
  user_type?: UserType
  isUpdateMode?: boolean
}>()

const state = reactive({
  email: '',
  username: '',
  display_name: '',
  password: '',
  user_type: UserType.PNL_ADMIN // Default to PNL_ADMIN for operator users
})

async function onSubmit(event: FormSubmitEvent<Schema>) {
  // Do something with data
  if (event.data.display_name === '') {
    event.data.display_name = null
  }
  emit('submit', event.data)
}

const generateTempPassword = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let randomPart = ''
  for (let i = 0; i < 8; i++) {
    randomPart += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  state.password = 'Pnl@1' + randomPart
}

// Watch for user prop changes to populate form
watch(
  () => props.user,
  (newUser) => {
    if (newUser) {
      state.email = newUser.email || ''
      state.username = newUser.username || ''
      state.display_name = newUser.display_name || ''
      state.user_type = newUser.user_type || UserType.PNL_ADMIN
      state.password = '' // Don't populate password for updates
    } else {
      // Reset form for new user
      state.email = ''
      state.username = ''
      state.display_name = ''
      state.password = ''
      state.user_type = UserType.PNL_ADMIN
    }
  },
  { immediate: true }
)

// Watch for errors and set form errors
watch(
  () => props.error,
  (newError) => {
    if (newError && formRef.value) {
      // Handle validation errors from API
      if (newError.errors) {
        Object.keys(newError.errors).forEach((field) => {
          formRef.value.setErrors({
            [field]: newError.errors[field]
          })
        })
      }
    }
  }
)
</script>

<template>
  <UForm
    ref="formRef"
    :schema="schema"
    :state="state"
    class="space-y-4"
    autocomplete="off"
    @submit="onSubmit"
  >
    <UFormGroup
      label="メールアドレス"
      name="email"
      required
    >
      <UInput
        v-model="state.email"
        type="email"
        placeholder="例: <EMAIL>"
        autofocus
        aria-autocomplete="none"
        autocomplete="new-email"
      />
    </UFormGroup>

    <UFormGroup
      label="ユーザ名"
      name="username"
      required
    >
      <UInput
        v-model="state.username"
        type="text"
        placeholder="ログインのユーザ名"
        aria-autocomplete="none"
        autocomplete="new-username"
        :disabled="isUpdateMode"
      />
    </UFormGroup>

    <UFormGroup
      v-if="!isUpdateMode"
      label="仮パスワード"
      name="password"
      required
    >
      <template #hint>
        <div
          class="flex items-center gap-1 text-sm text-primary-500 hover:underline cursor-pointer dark:text-primary-400"
          @click="generateTempPassword"
        >
          仮パスワードを生成する
        </div>
      </template>
      <BasePasswordInput
        v-model="state.password"
        show-password
        placeholder="ユーザの仮パスワード"
        aria-autocomplete="none"
        autocomplete="new-password"
      />
    </UFormGroup>

    <UFormGroup
      label="名前"
      name="display_name"
    >
      <UInput
        v-model="state.display_name"
        type="text"
        placeholder="ユーザの名前"
        aria-autocomplete="none"
        autocomplete="new-name"
      />
    </UFormGroup>

    <UFormGroup
      label="ユーザ権限"
      name="user_type"
    >
      <UInput
        :model-value="userTypeObject(UserType.PNL_ADMIN).label"
        type="text"
        disabled
        readonly
        :ui="{
          icon: {
            trailing: {
              pointer: ''
            }
          }
        }"
      >
        <template #trailing>
          <UIcon
            :name="userTypeObject(UserType.PNL_ADMIN).icon"
            class="w-5 h-5 text-gray-400 dark:text-gray-500"
          />
        </template>
      </UInput>
    </UFormGroup>

    <BasicFormButtonGroup
      class="justify-end"
      :loading="props.loading"
      @close="emit('close')"
    />
  </UForm>
</template>

<script setup lang="ts">
const selected = ref([] as string[])
const cityWardTowns = ref([] as any[])
const last = ref([] as any[])
const searchKeywords = ref([] as string[])
const searchKeyword = ref('')
const props = defineProps({
  show: Boolean,
  excludes: {
    type: Array as PropType<string[]>,
    default: () => []
  }
})

const emit = defineEmits(['close', 'select'])

const isOpen = computed({
  get: () => props.show,
  set: (value) => {
    if (value === false) emit('close')
  }
})

const onSelectCityWardTown = async (cityWardTown: string) => {
  // replace second element with the selected city, ward, town
  selected.value = [selected.value[0] as string]
  selected.value[1] = cityWardTown
  last.value = []
  const response = await fetch(
    `https://japanese-addresses-v2.geoloniamaps.com/api/ja/${selected.value[0]}/${cityWardTown}.json`
  )
  const data = await response.json()
  last.value = [
    {
      label: cityWardTown,
      children: data?.data?.map((item: any) => {
        return {
          label: item?.oaza_cho
        }
      })
    }
  ]
}

const onSelectPrefecture = async (prefecture: any) => {
  // replace first element with the selected prefecture
  selected.value = []
  selected.value[0] = prefecture?.label
  cityWardTowns.value = []
  last.value = []
  // call api to get the city, ward, town
  // https:// japanese-addresses-v2.geoloniamaps.com/api/ja/<都道府県名>/<市区町村名>.json
  const response = await fetch(
    `https://japanese-addresses-v2.geoloniamaps.com/api/ja/${prefecture?.label}.json`
  )
  const data = await response.json()
  cityWardTowns.value = [
    {
      label: prefecture?.label,
      children: data?.data?.map((item: any) => {
        const label
          = item?.city
            + (item?.ward ? `${item?.ward}` : '')
            + (item?.county ? `${item?.county}` : '')
        return {
          label: label,
          click: () => {
            onSelectCityWardTown(label)
            searchKeywords.value = [prefecture?.label, label]
            searchKeyword.value = prefecture?.label + label
          }
        }
      })
    }
  ]
}

const onSelectLast = (town: string) => {
  // replace third element with the selected town
  selected.value[2] = town
}

const cityWardTownsLinks = computed(() => {
  return cityWardTowns.value.map((cityWardTown) => {
    const cityWardTownChildrenUnique = cityWardTown.children.filter(
      (item: any, index: number, self: any) =>
        index === self.findIndex((t: any) => t.label === item.label)
    )
    cityWardTown.children = cityWardTownChildrenUnique
    return {
      ...cityWardTown,
      children: cityWardTown.children.map((town: any) => {
        return {
          ...town,
          active: selected.value.includes(town.label)
        }
      })
    }
  })
})

const lastLinks = computed(() => {
  return last.value.map((item) => {
    const itemChildrenUnique = item.children.filter(
      (item: any, index: number, self: any) =>
        index === self.findIndex((t: any) => t.label === item.label)
    )
    item.children = itemChildrenUnique
    return {
      ...item,
      children: item.children.map((town: any) => {
        return {
          ...town,
          active: selected.value.includes(town.label),
          click: () => {
            onSelectLast(town.label)
            searchKeywords.value = [selected.value[0], selected.value[1], town.label]
            searchKeyword.value = selected.value[0] + selected.value[1] + town.label
          }
        }
      })
    }
  })
})

const prefectures = computed(() => {
  return [
    {
      label: '北海道',
      children: [
        {
          label: '北海道'
        }
      ],
      open: false
    },
    {
      label: '東北',
      // 青森県  岩手県  宮城県  秋田県  山形県  福島県
      children: [
        {
          label: '青森県'
        },
        {
          label: '岩手県'
        },
        {
          label: '宮城県'
        },
        {
          label: '秋田県'
        },
        {
          label: '山形県'
        },
        {
          label: '福島県'
        }
      ]
    },
    {
      label: '関東',
      // 茨城県  栃木県  群馬県  埼玉県  千葉県  東京都  神奈川県  山梨県  長野県
      children: [
        {
          label: '茨城県'
        },
        {
          label: '栃木県'
        },
        {
          label: '群馬県'
        },
        {
          label: '埼玉県'
        },
        {
          label: '千葉県'
        },
        {
          label: '東京都'
        },
        {
          label: '神奈川県'
        },
        {
          label: '山梨県'
        },
        {
          label: '長野県'
        }
      ]
    },
    {
      label: '北陸',
      // 新潟県  富山県  石川県  福井県
      children: [
        {
          label: '新潟県'
        },
        {
          label: '富山県'
        },
        {
          label: '石川県'
        },
        {
          label: '福井県'
        }
      ]
    },
    {
      label: '中部',
      // 岐阜県  静岡県  愛知県  三重県
      children: [
        {
          label: '岐阜県'
        },
        {
          label: '静岡県'
        },
        {
          label: '愛知県'
        },
        {
          label: '三重県'
        }
      ]
    },
    {
      label: '近畿',
      // 滋賀県  京都府  大阪府  兵庫県  奈良県  和歌山県
      children: [
        {
          label: '滋賀県'
        },
        {
          label: '京都府'
        },
        {
          label: '大阪府'
        },
        {
          label: '兵庫県'
        },
        {
          label: '奈良県'
        },
        {
          label: '和歌山県'
        }
      ]
    },
    {
      label: '中国',
      // 鳥取県  島根県  岡山県  広島県  山口県
      children: [
        {
          label: '鳥取県'
        },
        {
          label: '島根県'
        },
        {
          label: '岡山県'
        },
        {
          label: '広島県'
        },
        {
          label: '山口県'
        }
      ]
    },
    {
      label: '四国',
      // 徳島県  香川県  愛媛県  高知県
      children: [
        {
          label: '徳島県'
        },
        {
          label: '香川県'
        },
        {
          label: '愛媛県'
        },
        {
          label: '高知県'
        }
      ]
    },
    {
      label: '九州',
      // 福岡県  佐賀県  長崎県  熊本県  大分県  宮崎県  鹿児島県
      children: [
        {
          label: '福岡県'
        },
        {
          label: '佐賀県'
        },
        {
          label: '長崎県'
        },
        {
          label: '熊本県'
        },
        {
          label: '大分県'
        },
        {
          label: '宮崎県'
        },
        {
          label: '鹿児島県'
        }
      ]
    },
    {
      label: '沖縄',
      // 沖縄県
      children: [
        {
          label: '沖縄県'
        }
      ]
    }
  ].map((prefecture) => {
    return {
      ...prefecture,
      children: prefecture.children.map((city) => {
        return {
          ...city,
          active: selected.value.includes(city.label),
          click: () => {
            onSelectPrefecture(city)
            searchKeywords.value = [city.label]
            searchKeyword.value = city.label
          }
        }
      })
    }
  })
})

watch(
  () => searchKeyword.value,
  (value) => {
    if (!value) {
      searchKeywords.value = []
      selected.value = []
      cityWardTowns.value = []
      last.value = []
      return
    }
    // check if prefectures has the search keyword, select it
    prefectures.value.forEach((prefecture) => {
      prefecture.children.forEach((city) => {
        if (city.label.includes(value)) {
          onSelectPrefecture(city)
          searchKeywords.value.push(value)
        }
      })
    })

    // check if cityWardTowns has the search keyword, select it
    const cityWwardTownsKeyword = value.replace(
      searchKeywords.value?.[0] as string,
      ''
    )
    cityWardTowns.value.forEach((cityWardTown) => {
      cityWardTown.children.forEach((town: any) => {
        if (town.label.includes(cityWwardTownsKeyword)) {
          onSelectCityWardTown(town.label)
          searchKeywords.value.push(cityWwardTownsKeyword)
        }
      })
    })

    // check if last has the search keyword, select it
    const lastKeyword = value
      .replace(searchKeywords.value?.[0] as string, '')
      .replace(searchKeywords.value?.[1] as string, '')
    last.value.forEach((item) => {
      item.children.forEach((town: any) => {
        if (town?.label?.includes(lastKeyword)) {
          onSelectLast(town.label)
        }
      })
    })
  }
)
</script>

<template>
  <UModal
    v-model="isOpen"
    :ui="{
      height: 'max-h-[80vh]',
      base: 'scrollbar-thin',
      width: 'w-full sm:max-w-screen-md'
    }"
  >
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!p-0'
        }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div>場所を選択</div>
          <UInput
            ref="input"
            v-model="searchKeyword"
            icon="la:search-location"
            autocomplete="off"
            placeholder="場所を検索..."
            size="sm"
            class="w-1/2"
            @keydown.esc="$event.target.blur()"
          />
        </div>
      </template>

      <div class="grid grid-cols-3 px-4 pb-4 pt-2">
        <div>
          <UNavigationTree
            class="max-h-[60vh] overflow-y-auto"
            :links="prefectures"
          />
        </div>
        <div>
          <UNavigationTree
            class="max-h-[60vh] overflow-y-auto"
            :links="cityWardTownsLinks"
          />
        </div>
        <div>
          <UNavigationTree
            class="max-h-[60vh] overflow-y-auto"
            :links="lastLinks"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex flex-row justify-between items-center">
          <div>
            <div
              v-if="selected.length"
              class="text-gray-500 dark:text-gray-400 text-sm"
            >
              選択中: {{ selected.join(" > ") }}
            </div>
          </div>
          <div class="flex justify-end gap-3">
            <UButton
              label="閉じる"
              color="gray"
              @click="emit('close')"
            />
            <UButton
              :disabled="!selected.length"
              label="選択"
              color="primary"
              @click="emit('select', selected.join(''))"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

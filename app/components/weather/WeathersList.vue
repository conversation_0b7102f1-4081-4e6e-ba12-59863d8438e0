<script setup lang="ts">
const props = defineProps({
  locations: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['delete', 'add'])

function getItems(location: any) {
  return [
    [
      {
        label: '場所削除',
        click: () => emit('delete', location),
        icon: 'material-symbols:delete'
      }
    ]
  ]
}
</script>

<template>
  <UCard :ui="{ body: { padding: '!p-0' } }">
    <ul
      role="list"
      class="divide-y divide-gray-200 dark:divide-gray-800"
    >
      <li
        v-if="loading"
        class="flex items-center justify-between gap-3 py-2.5 pl-3 pr-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <div class="flex items-center gap-3 min-w-0">
          <UIcon
            name="eos-icons:loading"
            class="text-xl text-gray-500 dark:text-gray-500"
          />

          <div class="text-sm min-w-0">
            <p class="text-gray-500 dark:text-gray-500 font-medium truncate text-sm">
              読み込み中
            </p>
          </div>
        </div>
      </li>
      <li
        v-for="(row, index) in locations"
        :key="index"
        class="flex items-center justify-between gap-3 py-0.5 pl-3 pr-2"
      >
        <div class="flex items-center gap-3 min-w-0">
          <div class="text-sm min-w-0">
            <p class="text-gray-900 dark:text-white font-medium truncate">
              {{ row.location }}
            </p>
            <p class="text-gray-500 dark:text-gray-400 truncate">
              {{ row.description }}
            </p>
          </div>
        </div>

        <div class="flex items-center gap-3">
          <UDropdown
            :items="getItems(row)"
            position="bottom-end"
          >
            <UButton
              icon="i-heroicons-ellipsis-vertical"
              color="gray"
              variant="ghost"
            />
          </UDropdown>
        </div>
      </li>
      <li
        class="flex items-center justify-between gap-3 py-2.5 pl-3 pr-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
        @click="emit('add')"
      >
        <div class="flex items-center gap-3 min-w-0">
          <UIcon
            name="formkit:add"
            class="text-xl text-gray-500 dark:text-gray-500"
          />

          <div class="text-sm min-w-0">
            <p class="text-gray-500 dark:text-gray-500 font-medium truncate">
              場所追加
            </p>
          </div>
        </div>
      </li>
    </ul>
  </UCard>
</template>

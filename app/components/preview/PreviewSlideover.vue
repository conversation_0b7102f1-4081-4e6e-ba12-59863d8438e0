<script setup lang="ts">
const ragsStore = useRagsStore()
const { loadings, chats } = storeToRefs(ragsStore)

const { isPreviewSlideoverOpen } = useDashboard()

const chatTextarea = ref()
const chatQuery = ref('')
watch(
  () => isPreviewSlideoverOpen.value,
  async (isOpen) => {
    if (isOpen) {
      nextTick(() => {
        chatTextarea.value?.textarea?.focus()
      })
      await ragsStore.ragTenantLogin()
      ragsStore.chatInit()
    }
  }
)

const onChat = () => {
  if (!chatQuery.value) return
  ragsStore.chat(chatQuery.value)
  chatQuery.value = ''
  nextTick(() => {
    chatTextarea.value?.textarea?.focus()
  })
}

defineShortcuts({
  meta_enter: {
    usingInput: true,
    handler: () => {
      onChat()
    }
  }
})

watch(
  () => chats.value,
  () => {
    nextTick(() => {
      const chatList = document.querySelector('.chat-list')
      chatList?.scrollTo({
        top: chatList.scrollHeight,
        behavior: 'smooth'
      })
    })
  },
  {
    deep: true
  }
)
</script>

<template>
  <UDashboardSlideover
    v-model="isPreviewSlideoverOpen"
    title="チャットボットのプレビュー"
    :ui="{
      body: {
        base: 'scrollbar-thin chat-list pb-24'
      }
    }"
  >
    <div
      v-if="loadings.ragTenantLogin || loadings.chatInit"
      class="flex items-center justify-center h-full flex-col gap-3 text-gray-500"
    >
      <UIcon
        name="eos-icons:loading"
        class="text-4xl"
      />
      <div>チャットボットを起動中...</div>
    </div>
    <div
      v-else
      class="space-y-4"
    >
      <PreviewChatsList
        :chats="chats"
        :loading="loadings.chat"
      />
    </div>
    <template #footer>
      <div
        v-if="!loadings.ragTenantLogin"
        class="w-full"
      >
        <div>
          <UTextarea
            ref="chatTextarea"
            v-model="chatQuery"
            name="chatTextarea"
            color="gray"
            size="xl"
            :rows="1"
            autoresize
            :maxrows="5"
            placeholder="メッセージを入力"
          >
            <UButton
              type="submit"
              size="xs"
              color="primary"
              label="送信"
              variant="solid"
              icon="i-heroicons-paper-airplane"
              class="absolute bottom-2 right-2"
              :disabled="loadings.chat"
              @click.prevent="onChat()"
            />
          </UTextarea>
        </div>
      </div>
    </template>
  </UDashboardSlideover>
</template>

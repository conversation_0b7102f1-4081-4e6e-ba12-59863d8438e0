<script setup lang="ts">
import type { Chat } from '~/types'

const ragsStore = useRagsStore()
const { chatSettings } = storeToRefs(ragsStore)
const props = defineProps<{
  chats: Chat[]
  loading: boolean
}>()

const chatListRef = ref()

// watch chats and scroll smoothly to the bottom
watch(
  () => props.chats,
  () => {
    nextTick(() => {
      chatListRef.value?.scrollTo({
        top: chatListRef.value.scrollHeight,
        behavior: 'smooth'
      })
    })
  }
)
</script>

<template>
  <div class="space-y-4">
    <!-- :translated-message=" chat.type === 'ai' ? chat.message : ''"
    :knowledge=" chat.type === 'ai' ? chat.message : ''" -->
    <ChatBubble
      v-for="(chat, index) in chats"
      :key="index"
      :sender-avatar="
        chat.type === 'ai' && chatSettings?.custom?.settings?.avatar_url
      "
      :sender-name="chat.type === 'ai' && chatSettings?.basic?.name"
      :message="chat.message"
      :right="chat.type === 'human'"
    />
    <ChatBubble
      v-if="loading"
      :sender-avatar="chatSettings?.custom?.settings?.avatar_url"
      :sender-name="chatSettings?.basic?.name"
      :loading="loading"
    />
  </div>
</template>

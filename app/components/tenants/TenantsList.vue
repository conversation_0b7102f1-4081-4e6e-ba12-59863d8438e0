<script setup lang="ts">
import type { PropType } from 'vue'
import type { TenantWithEnvironments } from '~/types/tenant'

defineProps({
  tenants: {
    type: Array as PropType<TenantWithEnvironments[]>,
    default: () => []
  }
})

const emit = defineEmits(['edit', 'delete'])

function getItems(tenant: TenantWithEnvironments) {
  return [
    [
      {
        label: 'テナント編集',
        click: () => emit('edit', tenant)
      },
      {
        label: 'テナント削除',
        labelClass: 'text-red-500 dark:text-red-400',
        click: () => emit('delete', tenant)
      }
    ]
  ]
}
</script>

<template>
  <ul
    role="list"
    class="divide-y divide-gray-200 dark:divide-gray-800"
  >
    <li
      v-for="(tenant, index) in tenants"
      :key="index"
      class="flex items-center justify-between gap-3 py-3 px-4 sm:px-6"
    >
      <div class="flex items-center gap-3 min-w-0">
        <UAvatar
          v-bind="tenant.avatar"
          size="md"
          :alt="tenant.id?.toUpperCase()"
        />

        <div class="text-sm min-w-0">
          <p class="text-gray-900 dark:text-white font-medium truncate">
            {{ tenant.id }}
          </p>
          <p class="text-gray-500 dark:text-gray-400 truncate">
            {{ tenant.description }}
          </p>
        </div>
      </div>

      <div class="flex items-center gap-3">
        <div v-if="!tenant.environments?.length" />
        <div
          v-else
          class="flex items-center gap-2"
        >
          <UBadge
            v-for="env in tenant.environments"
            :key="env.id"
            :icon="environmentObject(env.environment)?.icon"
            size="sm"
            :color="environmentObject(env.environment)?.color"
            variant="subtle"
            :label="environmentObject(env.environment)?.label"
            :trailing="false"
          >
            <div class="flex flex-wrap flex-row gap-1 items-center">
              <div>
                {{ environmentObject(env.environment)?.label }}
              </div>
              <div>
                (ver. {{ env.version }})
              </div>
            </div>
          </UBadge>
        </div>
        <UDropdown
          :items="getItems(tenant)"
          position="bottom-end"
        >
          <UButton
            icon="i-heroicons-ellipsis-vertical"
            color="gray"
            variant="ghost"
          />
        </UDropdown>
      </div>
    </li>
  </ul>
</template>

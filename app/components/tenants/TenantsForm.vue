<script setup lang="ts">
import { z } from 'zod'
import type { FormError, FormSubmitEvent } from '#ui/types'

const schema = z.object({
  id: z
    .string({ required_error: 'IDは 1 文字以上必要です。' })
    .min(1, 'IDは 1 文字以上必要です。')
    .max(25, 'IDは 25 文字以下必要です。'),
  description: z
    .string()
    .min(1, 'テナント名は 1 文字以上必要です。')
    .max(255, 'テナント名は 255 文字以下必要です。'),
  color: z
    .string(),
  enabled: z
    .boolean()
})
type Schema = z.output<typeof schema>

const emit = defineEmits(['close', 'submit'])
const props = defineProps<{
  description?: string
  id?: string
  color?: string
  loading?: boolean
  enabled?: boolean
}>()

const state = reactive({
  description: '',
  id: undefined as string | undefined,
  color: 'sky',
  enabled: true
})

async function onSubmit(event: FormSubmitEvent<Schema>) {
  // Do something with data
  console.log(event.data)
  emit('submit', event.data)
  emit('close')
}

onMounted(() => {
  if (props.id) {
    state.id = props.id
  }
  if (props.description) {
    state.description = props.description
  }
  if (props.color) {
    state.color = props.color
  }
  if (props.enabled !== undefined) {
    state.enabled = props.enabled
  }
})
</script>

<template>
  <UForm
    ref="formRef"
    :schema="schema"
    :state="state"
    class="space-y-4"
    @submit="onSubmit"
  >
    <UFormGroup
      label="テナントID"
      name="id"
      required
    >
      <UInput
        v-model="state.id"
        type="text"
        placeholder="例: playnextlab"
        autofocus
        :disabled="props.id !== undefined"
        maxlength="25"
      />
    </UFormGroup>

    <UFormGroup
      label="テナント名"
      name="description"
      required
    >
      <UInput
        v-model="state.description"
        type="text"
        placeholder="例: プレネクストラボ"
        maxlength="255"
      />
    </UFormGroup>

    <UFormGroup
      label="カラーテーマ"
      name="color"
    >
      <USelectMenu
        v-model="state.color"
        :options="['sky', 'blue', 'red', 'green', 'yellow', 'purple', 'pink', 'indigo', 'gray', 'customone']"
        placeholder="カラーテーマを選択"
      />
    </UFormGroup>

    <UFormGroup
      label="ステータス"
      name="enabled"
    >
      <UToggle
        v-model="state.enabled"
        :on-icon="'i-heroicons-check'"
        :off-icon="'i-heroicons-x-mark'"
      />
    </UFormGroup>

    <div class="flex justify-end gap-3">
      <UButton
        label="キャンセル"
        color="gray"
        variant="ghost"
        @click="emit('close')"
      />
      <UButton
        type="submit"
        label="確定"
        color="black"
      />
    </div>
  </UForm>
</template>

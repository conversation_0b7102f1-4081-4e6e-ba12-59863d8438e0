<script setup lang="ts">
import type { CreateTenantPayload } from '~/types/tenant'

const tenantsStore = useTenantsStore()
const environmentsStore = useEnvironmentsStore()
const settingsStore = useSettingsStore()
const toast = useToast()
const { errors: tenantsErrors } = storeToRefs(tenantsStore)
const { errors: environmentsErrors } = storeToRefs(environmentsStore)
const { errors: settingsErrors } = storeToRefs(settingsStore)
const emit = defineEmits(['close', 'submit'])
const props = defineProps<{
  description?: string
  id?: string
  color?: string
  enabled?: boolean
}>()
const tenantCreationSteps = computed(() => {
  // Ensure we have a valid tenant ID
  if (!props.id) return []

  const tenantId = props.id
  const prodEnvId = props.id + '001'
  const devEnvId = props.id + '002'
  return [
    {
      title: 'テナント作成中...',
      action: () =>
        tenantsStore.createTenant({
          description: props.description,
          id: tenantId,
          color: props.color || 'sky',
          enabled: props.enabled !== undefined ? props.enabled : true
        } as CreateTenantPayload),
      error: tenantsErrors.value['createTenant']
    },
    {
      title: '本番環境作成中...',
      action: () => {
        if (!tenantId) return false
        return environmentsStore.createEnvironment(tenantId, {
          environment: 1,
          id: prodEnvId
        })
      },
      error: environmentsErrors.value['createEnvironment']
    },
    {
      title: '検証環境作成中...',
      action: () => {
        if (!tenantId) return false
        return environmentsStore.createEnvironment(tenantId, {
          environment: 2,
          id: devEnvId
        })
      },
      error: environmentsErrors.value['createEnvironment']
    },
    {
      title: '一般設定作成中...',
      action: () => {
        if (!tenantId || !devEnvId) return false
        return settingsStore.createBasicSetting(tenantId, devEnvId, {
          name: (props.description || 'New Tenant') + 'のAIチャットボット',
          welcome_message:
            'こんにちは！ 私はAIチャットボットです。何かお手伝いできますか？',
          survey_option_count: 2
        })
      },
      error: settingsErrors.value['createBasicSetting']
    },
    {
      title: 'カスタム設定作成中...',
      action: () => {
        if (!tenantId || !devEnvId) return false
        return settingsStore.createCustomSetting(tenantId, devEnvId, {
          settings: {
            color_primary: props.color || 'sky'
          }
        })
      },
      error: settingsErrors.value['createCustomSetting']
    },
    {
      title: '完了',
      action: () => {
        emit('close')
        toast.add({
          title: 'テナント作成完了',
          description: 'テナントの作成が完了しました。',
          color: 'green'
        })
      },
      error: null
    }
  ]
})

const currentStep = ref(0)
const steps = computed(() => {
  return tenantCreationSteps.value.map(row => row.title)
})

const creationResults = ref<any[]>([])
const error = computed(() => {
  return tenantCreationSteps.value[currentStep.value]?.error
})
const startCreationProcess = async () => {
  for await (const step of tenantCreationSteps.value) {
    const result = await step.action()
    if (result) {
      creationResults.value.push(result)
      currentStep.value++
    } else {
      // stop the process if any step fails
      break
    }
  }
}

onMounted(() => {
  // Start the process if props are passed
  if (props.id) {
    startCreationProcess()
  }
})
</script>

<template>
  <div>
    <UAlert
      v-if="error"
      icon="material-symbols:error"
      color="red"
      :description="error?.error_message"
      :title="`エラー: ${error?.error_code}`"
    />
    <UProgress
      v-else
      :value="currentStep"
      :max="steps"
    />
  </div>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const tenantsStore = useTenantsStore()
const environmentsStore = useEnvironmentsStore()
const {
  topFiveTenantsDropdownItems,
  loadings,
  selectedTenant,
  selectedTenantId,
  tenantsDropdownItems
} = storeToRefs(tenantsStore)
const { isOperator } = storeToRefs(authStore)

onMounted(() => {
  if (selectedTenantId.value) {
    environmentsStore.fetchAllEnvs(selectedTenantId.value)
  }
})
</script>

<template>
  <UDropdown
    v-if="isOperator"
    mode="click"
    :items="[tenantsDropdownItems]"
    class="w-full group"
    :ui="{ width: 'w-full min-w-60' }"
    :popper="{ strategy: 'absolute' }"
  >
    <UButton
      color="gray"
      variant="ghost"
      class="w-full justify-start"
      block
      :loading="loadings.fetchTenants"
    >
      <UAvatar
        :src="selectedTenant?.avatar?.src"
        :alt="selectedTenant?.label?.toUpperCase()"
        size="2xs"
        :ui="{
          rounded: 'rounded-sm'
        }"
      />

      <div
        class="flex flex-row gap-1 items-center justify-between w-full truncate"
      >
        <span class="text-gray-900 dark:text-white font-semibold truncate">{{
          selectedTenant?.label
        }}</span>
      </div>
      <UIcon
        name="octicon:arrow-switch-24"
        class="ml-auto text-lg"
      />
    </UButton>
    <template
      v-for="tenant in tenantsDropdownItems"
      #[`tenant${tenant.id}`]="{ item }"
      :key="tenant.uuid"
    >
      <div class="flex items-center gap-2 w-full">
        <UAvatar
          :alt="item?.label?.toUpperCase()"
          :src="item?.avatar?.src"
          size="2xs"
          :ui="{
            rounded: 'rounded-sm'
          }"
        />

        <div
          class="flex flex-row gap-1 items-center justify-between w-full truncate"
        >
          <span class="text-gray-700 dark:text-gray-200 truncate text-sm">{{
            item?.label
          }}</span>

          <UBadge
            v-if="item?.deploy_slot"
            size="xs"
            :color="item?.color"
            variant="soft"
            :ui="{
              rounded: 'rounded-full',
              size: { xs: 'text-[11px] px-2.5 py-0' }
            }"
          >
            {{ `${item?.deploy_slot}` }}
          </UBadge>
        </div>
      </div>
    </template>
    <template #more="{ item }">
      <div class="flex items-center gap-2">
        <UAvatar
          :alt="'+ ' + item.remainAvatars?.length"
          size="2xs"
          :ui="{
            background: 'bg-gray-200 dark:bg-gray-700',
            text: 'text-gray-500 dark:text-gray-800'
          }"
        />

        <div>
          <span class="text-sm text-gray-500 dark:text-gray-400">他のテナント</span>
        </div>
      </div>
    </template>
  </UDropdown>
  <div
    v-else
    class="flex flex-row gap-1 items-center justify-between w-full truncate px-4"
  >
    <UAvatar
      :src="selectedTenant?.avatar?.src"
      :alt="selectedTenant?.label?.toUpperCase()"
      size="2xs"
      :ui="{
        rounded: 'rounded-sm'
      }"
    />

    <div
      class="flex flex-row gap-1 items-center justify-between w-full truncate"
    >
      <span class="text-gray-900 dark:text-white font-semibold truncate">{{
        selectedTenant?.label
      }}</span>
    </div>
  </div>
</template>

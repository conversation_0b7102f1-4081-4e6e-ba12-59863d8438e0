<script setup lang="ts">
const options = ref([
  { id: 1, name: '子育て', color: 'd73a4a' },
  { id: 2, name: '税金', color: '0075ca' },
  { id: 3, name: '保育園', color: 'cfd3d7' },
  { id: 4, name: '確定申告', color: 'a2eeef' }
])

const selected = ref([])

const labels = computed({
  get: () => selected.value,
  set: async (labels) => {
    const promises = labels.map(async (label) => {
      if (label.id) {
        return label
      }

      // In a real app, you would make an API call to create the label
      const response = {
        id: options.value.length + 1,
        name: label.name,
        color: generateColorFromString(label.name)
      }

      options.value.push(response)

      return response
    })

    selected.value = await Promise.all(promises)
  }
})

function hashCode(str) {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }
  return hash
}

function intToRGB(i) {
  const c = (i & 0x00ffffff).toString(16).toUpperCase()

  return '00000'.substring(0, 6 - c.length) + c
}

function generateColorFromString(str) {
  return intToRGB(hashCode(str))
}
</script>

<template>
  <USelectMenu
    v-model="labels"
    by="id"
    name="labels"
    :options="options"
    option-attribute="name"
    multiple
    searchable
    creatable
    searchable-placeholder="ラベルを検索"
  >
    <template #label>
      <template v-if="labels.length">
        <span class="flex items-center -space-x-1">
          <span
            v-for="label of labels"
            :key="label.id"
            class="flex-shrink-0 w-2 h-2 mt-px rounded-full"
            :style="{ background: `#${label.color}` }"
          />
        </span>
        <span class="truncate">{{ labels.map((label) => label.name).join(", ") }}
        </span>
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate">
          ラベルを選択
        </span>
      </template>
    </template>

    <template #option="{ option }">
      <span
        class="flex-shrink-0 w-2 h-2 mt-px rounded-full"
        :style="{ background: `#${option.color}` }"
      />
      <span class="truncate">{{ option.name }}</span>
    </template>

    <template #option-create="{ option }">
      <span class="flex-shrink-0">新規ラベル:</span>
      <span
        class="flex-shrink-0 w-2 h-2 mt-px rounded-full -mx-1"
        :style="{ background: `#${generateColorFromString(option.name)}` }"
      />
      <span class="block truncate">{{ option.name }}</span>
    </template>
  </USelectMenu>
</template>

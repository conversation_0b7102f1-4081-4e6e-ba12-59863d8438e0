<!-- eslint-disable vue/no-v-html -->
<template>
  <div>
    <div
      class="flex items-start gap-2.5 group"
      :class="{
        'flex-row-reverse': right
      }"
    >
      <img
        v-if="senderAvatar"
        class="w-8 rounded-sm mt-auto"
        :src="senderAvatar"
      >
      <UAvatar
        v-else-if="senderIcon"
        :icon="senderIcon"
        :alt="senderName"
        size="md"
        :ui="{
          rounded: 'rounded-full'
        }"
      />
      <div class="flex flex-col gap-1 w-fit max-w-[80%] relative">
        <div
          class="flex items-baseline gap-2"
          :class="{
            'flex-row-reverse': right
          }"
        >
          <span
            v-if="right"
            class="text-xs font-semibold text-gray-500 dark:text-gray-400"
            :class="{
              'text-right': right
            }"
          >
            ({{ $t(userType || "") }}) {{ username }}
          </span>
          <span
            v-if="showDatetime"
            class="text-xs font-normal text-gray-500 dark:text-gray-400"
          >
            {{ datetimeFormatted }}
          </span>
        </div>
        <div
          class="flex flex-col leading-1.5 py-1 px-3 w-fit"
          :class="{
            'rounded-s-xl rounded-br-xl self-end border-primary-200 bg-primary-100 dark:bg-primary-900 ':
              right,
            'rounded-e-xl rounded-tl-xl border-gray-200 bg-gray-100  dark:bg-gray-700  cursor-pointer':
              !right
          }"
          @click="showKnowledges = !showKnowledges"
        >
          <div v-if="loading">
            <div class="flex items-center gap-1">
              <UIcon
                name="eos-icons:three-dots-loading"
                class="text-4xl"
              />
            </div>
          </div>
          <div
            class="text-xs relative top-2 font-normal text-gray-900 dark:text-white break-words whitespace-pre-wrap"
            :class="{
              'pb-4': isHtmlMessage(message)
            }"
            v-html="isHtmlMessage(message) ? message : md.render(message || '')"
          />
        </div>
        <div
          v-if="!right"
          class="ml-2"
        >
          <div class="space-y-0">
            <div v-if="promptTokens || completionTokens || tokenCount">
              <BaseTokenInfoTag
                :prompt-tokens="promptTokens"
                :completion-tokens="completionTokens"
                :token-count="tokenCount"
                :session-id="sessionId"
                :is-session-mode="false"
              />
            </div>
            <div
              v-if="
                analyzedAction !== undefined
                  || contextType !== undefined
                  || userType
                  || username
              "
            >
              <BaseAnalysisInfoTag
                :analyzed-action="analyzedAction"
                :context-type="contextType"
                :user-type="userType"
                :username="username"
                :session-id="sessionId"
                :is-session-mode="isSessionMode"
              />
            </div>
          </div>
        </div>
        <div v-if="translatedMessage">
          <div
            class="flex items-center px-3 gap-1 mt-2 mb-2 text-gray-900 dark:text-gray-400"
          >
            <UIcon
              name="line-md:arrow-down"
              class="text-sm"
            />
            <span class="text-[10px] font-semibold"> 日本語に翻訳 </span>
          </div>
          <div
            class="flex flex-col leading-1.5 p-2 px-3"
            :class="{
              'border-l-2 rounded-xl border-primary-200 bg-primary-100 dark:bg-primary-950':
                right,
              'border-r-2 rounded-xl border-gray-400 bg-gray-200 dark:bg-gray-800':
                !right
            }"
          >
            <div
              class="text-xs font-normal text-gray-900 dark:text-white break-words relative top-2 whitespace-pre-wrap"
              :class="{
                'pb-4': isHtmlMessage(translatedMessage)
              }"
              v-html="
                isHtmlMessage(translatedMessage)
                  ? translatedMessage
                  : md.render(translatedMessage || '')
              "
            />
          </div>
        </div>
        <div
          v-if="!showKnowledges && knowledge"
          class="items-center gap-1 text-primary text-xs px-2 hidden absolute -bottom-5 group-hover:flex cursor-pointer"
        >
          <UIcon
            name="icon-park-solid:click"
            class="text-sm"
          />
          クリックしてナレッジを表示
        </div>
      </div>
    </div>
    <div v-if="showKnowledges && knowledge">
      <div
        class="flex flex-col gap-1 justify-center items-center my-3 text-primary"
      >
        <UIcon
          name="line-md:uploading-loop"
          class="text-3xl"
        />
        <div class="text-xs text-primary-500 dark:text-primary-400">
          下記のナレッジに基づいて回答されました。
        </div>
      </div>
      <UCard
        :ui="{
          body: {
            padding: '!px-4 !pb-3 !pt-3',
            base: 'text-sm'
          },
          rounded: 'rounded-md'
        }"
      >
        <div class="flex items-center gap-2.5">
          <div
            class="flex items-center gap-1 text-primary font-semibold text-xs"
          >
            <UIcon
              name="garden:knowledge-base-26"
              class=""
            />
            ナレッジID: XXXXXXXXXX (クリックでナレッジページに移動されます)
          </div>
        </div>
        <div class="text-gray-900 dark:text-gray-300 break-words text-xs mt-3">
          {{ knowledge }}
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format, isToday } from 'date-fns'
import markdownit from 'markdown-it'
import mila from 'markdown-it-link-attributes'

const md = markdownit()
md.use(mila, {
  attrs: {
    target: '_blank',
    rel: 'noopener',
    class: 'text-blue-500 underline'
  }
})
const props = defineProps<{
  message?: string
  translatedMessage?: string
  senderName: string
  senderAvatar?: string
  senderIcon?: string
  datetime?: string
  right?: boolean
  knowledge?: string
  loading?: boolean
  showDatetime?: boolean
  promptTokens?: number
  completionTokens?: number
  tokenCount?: number
  sessionId?: string
  isSessionMode?: boolean
  analyzedAction?: number
  contextType?: number
  userType?: string
  username?: string
}>()

const showKnowledges = ref(false)

const datetimeFormatted = computed(() => {
  if (props.datetime) {
    // check if isToday
    return isToday(new Date(props.datetime))
      ? format(new Date(props.datetime), 'HH:mm')
      : format(new Date(props.datetime), 'MM月dd日 HH:mm')
  }

  return ''
})

// const isHtmlMessage = computed(() => {
//   return /<[^>]+>/.test(props.message || '')
// })

const isHtmlMessage = (message: string | undefined) => {
  return /<[^>]+>/.test(message || '')
}
</script>

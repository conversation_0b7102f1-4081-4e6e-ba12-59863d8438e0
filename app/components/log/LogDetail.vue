<script setup lang="ts">
const settingsStore = useSettingsStore()

const { currentChatbotAvatarURL, currentChatbotName }
  = storeToRefs(settingsStore)
defineProps({
  selected: {
    type: Boolean,
    default: false
  },
  logs: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  allMessages: {
    type: Array as PropType<any[]>,
    default: () => []
  }
})
</script>

<template>
  <UDashboardPanelContent class="scrollbar-thin">
    <div class="flex flex-col gap-6">
      <template
        v-for="log in logs"
        :key="log.request_id"
      >
        <ChatBubble
          :sender-name="'ユーザ'"
          sender-icon="solar:user-bold-duotone"
          :message="log.query"
          :translated-message="log.query_jp"
          right
          :datetime="log.query_created_at"
          :user-type="log.user_type"
          :username="log.username"
        />
        <ChatBubble
          show-datetime
          :sender-name="currentChatbotName"
          :sender-avatar="currentChatbotAvatarURL"
          :message="log.answer"
          :translated-message="log.answer_jp"
          :datetime="log.answer_created_at"
          :prompt-tokens="log.prompt_tokens"
          :completion-tokens="log.completion_tokens"
          :token-count="log.token_count"
          :session-id="log.session_id"
          :is-session-mode="true"
          :analyzed-action="log.analyzed_action"
          :context-type="log.context_type"
        />
      </template>
    </div>
  </UDashboardPanelContent>
</template>

<script setup lang="ts">
defineProps({
  loading: {
    type: Boolean,
    required: true
  }
})
</script>

<template>
  <div
    v-for="i in 10"
    :key="i"
    class="p-4 text-sm cursor-pointer border-l-2 text-gray-600 dark:text-gray-300"
    :class="'border-white dark:border-gray-900 hover:border-primary-500/25 dark:hover:border-primary-400/25 hover:bg-primary-100/50 dark:hover:bg-primary-900/10'"
  >
    <p class="text-gray-500 dark:text-gray-500 text-xs">
      <USkeleton class="h-3 w-1/3" />
    </p>
    <div
      class="flex items-center justify-between"
      :class="['font-semibold']"
    >
      <div class="w-full mt-1">
        <USkeleton class="h-4 w-[250px]" />
      </div>

      <div class="text-gray-500 dark:text-gray-500">
        <USkeleton class="h-3 w-10" />
      </div>
    </div>

    <p class=" text-gray-500 dark:text-gray-500 line-clamp-2 text-xs mt-3 flex flex-col gap-1">
      <USkeleton class="h-4 w-full" />
      <USkeleton class="h-4 w-full" />
    </p>
  </div>
</template>

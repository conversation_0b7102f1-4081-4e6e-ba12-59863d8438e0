<template>
  <USelectMenu
    v-model="headerRowModel"
    by="value"
    name="faq_header_row"
    :options="headerRowOptions"
    multiple
    searchable
    creatable
    searchable-placeholder="行番号を検索"
    :ui-menu="{
      width: 'min-w-72'
    }"
  >
    <template #label>
      <template v-if="headerRowModel?.length">
        <span class="truncate">{{
          headerRowModel.map((row) => `行${row.value + 1}`).join(", ")
        }}</span>
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate">
          ヘッダー行を選択
        </span>
      </template>
    </template>

    <template #option="{ option }">
      <span class="truncate">{{ option.label }}</span>
    </template>

    <template #option-create="{ option }">
      <span class="flex-shrink-0">行{{ parseInt(option.label) }}を追加</span>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
interface HeaderRowOption {
  value: number
  label: string
}

const props = defineProps<{
  modelValue: number[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: number[]]
}>()

// Generate options for header rows (0-19, representing rows 1-20)
// Plus any custom values that have been selected
const headerRowOptions = computed<HeaderRowOption[]>(() => {
  const baseOptions: HeaderRowOption[] = []
  for (let i = 0; i < 20; i++) {
    baseOptions.push({
      value: i,
      label: `行${i + 1}`
    })
  }

  // Add any custom values that are in modelValue but not in base options
  const customOptions: HeaderRowOption[] = []
  props.modelValue.forEach((value) => {
    if (value >= 20 && !baseOptions.find(opt => opt.value === value)) {
      customOptions.push({
        value,
        label: `行${value + 1}`
      })
    }
  })

  // Combine and sort all options
  return [...baseOptions, ...customOptions].sort((a, b) => a.value - b.value)
})

const headerRowModel = computed({
  get: () => {
    return props.modelValue.map(value => ({
      value,
      label: `行${value + 1}`
    }))
  },
  set: (newValue: HeaderRowOption[]) => {
    console.log('🚀 ~ newValue:', newValue)

    const values = newValue
      .map((item: any) => {
        // Handle both existing options and newly created ones
        if (typeof item === 'object' && 'value' in item) {
          return item.value
        } else {
          // Handle newly created options (they come as strings like "行5")
          const stringValue = String(item.label)
          const match = stringValue.match(/行(\d+)/)
          if (match && match[1]) {
            const rowNumber = parseInt(match[1])
            return isNaN(rowNumber) ? 0 : rowNumber - 1 // Convert to 0-based index
          }
          // Fallback: try to parse as number directly
          const numValue = parseInt(stringValue) - 1
          return isNaN(numValue) ? 0 : Math.max(0, numValue)
        }
      })
      .filter(value => value >= 0) // Only allow non-negative values

    emit('update:modelValue', values)
  }
})
</script>

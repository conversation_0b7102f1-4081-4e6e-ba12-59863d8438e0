<script setup lang="ts">
import { SUPPORTED_LANGUAGES } from '~/constants/supported-languages'

const selected = ref([])

const props = defineProps({
  show: <PERSON>ole<PERSON>,
  excludes: {
    type: Array as PropType<string[]>,
    default: () => []
  }
})

const emit = defineEmits(['close', 'select'])

const isOpen = computed({
  get: () => props.show,
  set: (value) => {
    if (value === false) emit('close')
  }
})

function onSelect(value: any) {
  isOpen.value = false
  emit('select', value)
}

const supportLanguages = computed(() => {
  return SUPPORTED_LANGUAGES.filter(
    lang => !props.excludes.includes(lang.id)
  )
})
</script>

<template>
  <UModal
    v-model="isOpen"
    :ui="{
      height: 'max-h-[80vh]',
      base: 'scrollbar-thin'
    }"
  >
    <UCommandPalette
      :groups="[{ key: 'support-languages', commands: supportLanguages }]"
      :fuse="{
        fuseOptions: {
          ignoreLocation: true,
          includeMatches: true,
          threshold: 0,
          keys: ['id', 'desciption']
        },
        resultLimit: 100
      }"
      icon="material-symbols:language"
      placeholder="言語検索"
      @update:model-value="onSelect"
    />
  </UModal>
</template>

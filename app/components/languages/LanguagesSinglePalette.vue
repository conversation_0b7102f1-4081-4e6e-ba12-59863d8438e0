<script setup lang="ts">
import { SUPPORTED_LANGUAGES } from '~/constants/supported-languages'

const isOpen = ref(false)

const selected = ref([])
</script>

<template>
  <div>
    <UButton
      label="Open"
      @click="isOpen = true"
    />

    <UModal
      v-model="isOpen"
      :ui="{
        height: 'max-h-[80vh]'
      }"
    >
      <UCommandPalette
        v-model="selected"
        multiple
        :groups="[{ key: 'supported-languages', commands: SUPPORTED_LANGUAGES }]"
        :fuse="{
          fuseOptions: {
            ignoreLocation: true,
            includeMatches: true,
            threshold: 0,
            keys: ['id', 'desciption']
          },
          resultLimit: 100
        }"
        icon="material-symbols:language"
        placeholder="言語検索"
      />
    </UModal>
  </div>
</template>

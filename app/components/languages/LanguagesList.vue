<script setup lang="ts">
const props = defineProps({
  languages: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  defaultLanguage: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['set-default', 'delete', 'add'])

function getItems(lang: any) {
  return [
    [
      // {
      //   label: 'デフォルトに設定',
      //   click: () => emit('set-default', lang),
      //   show: true,
      //   icon: 'material-symbols:language',
      //   disabled: lang.id === props.defaultLanguage
      // },
      {
        label: '言語削除',
        click: () => emit('delete', lang),
        show: lang.id !== '日本語',
        icon: 'material-symbols:delete',
        disabled: lang.id === props.defaultLanguage || lang.id === '日本語'
      }
    ].filter(item => item.show)
  ]
}
</script>

<template>
  <UCard :ui="{ body: { padding: '!p-0' } }">
    <ul
      role="list"
      class="divide-y divide-gray-200 dark:divide-gray-800"
    >
      <li
        v-for="(lang, index) in languages"
        :key="index"
        class="flex items-center justify-between gap-3 py-0.5 pl-3 pr-2"
      >
        <div class="flex items-center gap-3 min-w-0">
          <UIcon
            :name="lang.icon"
            class="text-xl"
          />

          <div class="text-sm min-w-0">
            <p class="text-gray-900 dark:text-white font-medium truncate">
              {{ lang.label }}
            </p>
            <p class="text-gray-500 dark:text-gray-400 truncate">
              {{ lang.description }}
            </p>
          </div>
        </div>

        <div class="flex items-center gap-3">
          <div
            v-if="defaultLanguage === lang.id"
            class="pt-2"
          >
            <UBadge
              size="xs"
              color="primary"
              variant="solid"
              label="デフォルト言語"
              :trailing="false"
              icon="material-symbols:language"
              :ui="{
                rounded: 'rounded-full'
              }"
            />
          </div>
          <UDropdown
            v-if="defaultLanguage !== lang.id"
            :items="getItems(lang)"
            position="bottom-end"
          >
            <UButton
              icon="i-heroicons-ellipsis-vertical"
              color="gray"
              variant="ghost"
              :disabled="disabled"
            />
          </UDropdown>
        </div>
      </li>
      <li
        v-if="!disabled"
        class="flex items-center justify-between gap-3 py-2.5 pl-3 pr-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
        @click="emit('add')"
      >
        <div class="flex items-center gap-3 min-w-0">
          <UIcon
            name="formkit:add"
            class="text-xl text-gray-500 dark:text-gray-500"
          />

          <div class="text-sm min-w-0">
            <p class="text-gray-500 dark:text-gray-500 font-medium truncate">
              言語追加
            </p>
          </div>
        </div>
      </li>
    </ul>
  </UCard>
</template>

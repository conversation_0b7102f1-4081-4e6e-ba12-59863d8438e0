<template>
  <div class="flex justify-end gap-3">
    <UButton
      :label="cancelLabel"
      color="gray"
      variant="ghost"
      @click="emit('close')"
    />
    <UButton
      type="submit"
      :label="submitLabel"
      color="black"
      :loading="props.loading"
      class="px-10"
      @click="emit('submit')"
    />
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  submitLabel: {
    type: String,
    default: '確定'
  },
  cancelLabel: {
    type: String,
    default: 'キャンセル'
  }
})

const emit = defineEmits(['close', 'submit'])
</script>

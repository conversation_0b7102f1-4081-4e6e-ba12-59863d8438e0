<template>
  <div class="space-y-4">
    <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
      <h4 class="text-sm font-medium mb-2">Debug Info:</h4>
      <div class="text-xs text-gray-500 space-y-1">
        <div>Model Value: {{ props.modelValue?.substring(0, 100) }}...</div>
        <div>Is JSON: {{ isJsonContent }}</div>
        <div>Properties Count: {{ parsedProperties.length }}</div>
        <div>Single Content: {{ singleContent?.substring(0, 50) }}...</div>
      </div>
    </div>

    <!-- JSON Properties Editor -->
    <div v-if="isJsonContent && parsedProperties.length > 0">
      <div class="mb-4 flex items-center justify-between">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
          JSONプロパティ編集 ({{ parsedProperties.length }} properties)
        </h4>
        <UButton
          size="xs"
          variant="outline"
          :label="showAllInEditMode ? 'すべてプレビュー' : 'すべて編集'"
          @click="toggleAllMode"
        />
      </div>

      <div class="space-y-3">
        <div
          v-for="(property, index) in parsedProperties"
          :key="`${property.key}-${index}`"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ $t(property.key || property.key) }} ({{ property.mode }})
            </label>
            <UButton
              size="xs"
              variant="ghost"
              :label="property.mode === 'edit' ? 'プレビュー' : '編集'"
              @click="() => togglePropertyMode(index)"
            />
          </div>

          <!-- Edit Mode -->
          <div v-if="property.mode === 'edit'">
            <UTextarea
              v-model="property.value"
              :placeholder="`${$t(property.key || property.key)}の内容を入力してください`"
              autoresize
            />
          </div>

          <!-- Preview Mode -->
          <div v-else class="min-h-[100px] p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
            <div class="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
              {{ property.value || '内容が空です' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Single Content Editor (for non-JSON content) -->
    <div v-else>
      <div class="mb-4 flex items-center justify-between">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
          コンテンツ編集
        </h4>
        <UButton
          size="xs"
          variant="outline"
          :label="contentMode === 'edit' ? 'プレビュー' : '編集'"
          @click="toggleContentMode"
        />
      </div>

      <!-- Edit Mode -->
      <div v-if="contentMode === 'edit'">
        <UTextarea
          v-model="singleContent"
          placeholder="コンテンツを入力してください"
          autoresize
        />
      </div>

      <!-- Preview Mode -->
      <div v-else class="min-h-[200px] p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
        <div class="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
          {{ singleContent || '内容が空です' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

// Check if content is JSON
const isJsonContent = computed(() => {
  if (!props.modelValue || props.modelValue.trim() === '') {
    return false
  }
  try {
    const parsed = JSON.parse(props.modelValue)
    return typeof parsed === 'object' && parsed !== null && !Array.isArray(parsed)
  } catch {
    return false
  }
})

// Parse JSON into properties
const parsedProperties = ref<Array<{
  key: string
  value: string
  mode: 'preview' | 'edit'
}>>([])

// Single content for non-JSON
const singleContent = ref('')
const contentMode = ref<'preview' | 'edit'>('preview')

// Global mode toggle
const showAllInEditMode = ref(false)

// Helper function to properly parse values based on their type
const parseValue = (value: any): string => {
  if (value === null || value === undefined) {
    return ''
  }

  if (typeof value === 'string') {
    return value
  }

  if (typeof value === 'object' || Array.isArray(value)) {
    return JSON.stringify(value, null, 2)
  }

  // For primitives (number, boolean, etc.)
  return String(value)
}

// Helper function to convert string back to original type
const parseStringToValue = (str: string): any => {
  if (str === '') {
    return ''
  }

  // Try to parse as JSON first (for objects and arrays)
  try {
    const parsed = JSON.parse(str)
    // If it's a valid JSON object or array, return it
    if (typeof parsed === 'object') {
      return parsed
    }
    // If it's a primitive value that was JSON-encoded, return it
    return parsed
  } catch {
    // If JSON parsing fails, return as string
    return str
  }
}

// Initialize properties when content changes
watch(() => props.modelValue, (newValue) => {
  console.log('Model value changed:', newValue)

  if (isJsonContent.value) {
    try {
      const parsed = JSON.parse(newValue)
      console.log('Parsed JSON:', parsed)

      parsedProperties.value = Object.entries(parsed).map(([key, value]) => ({
        key,
        value: parseValue(value),
        mode: 'preview' as const
      }))

      console.log('Properties set:', parsedProperties.value)
    } catch (error) {
      console.error('JSON parse error:', error)
      parsedProperties.value = []
    }
  } else {
    singleContent.value = newValue || ''
    console.log('Single content set:', singleContent.value)
  }
}, { immediate: true })

// Watch for changes in properties and emit updates
watch(parsedProperties, (newProperties) => {
  console.log('Properties changed:', newProperties)

  if (isJsonContent.value && newProperties.length > 0) {
    const jsonObject = newProperties.reduce((acc, prop) => {
      acc[prop.key] = parseStringToValue(prop.value)
      return acc
    }, {} as Record<string, any>)

    const newJsonString = JSON.stringify(jsonObject, null, 2)
    console.log('Emitting new JSON:', newJsonString)
    emit('update:modelValue', newJsonString)
  }
}, { deep: true })

// Watch for changes in single content
watch(singleContent, (newContent) => {
  console.log('Single content changed:', newContent)

  if (!isJsonContent.value) {
    emit('update:modelValue', newContent)
  }
})

// Toggle individual property mode
const togglePropertyMode = (index: number) => {
  console.log('Toggling property mode for index:', index)

  if (parsedProperties.value[index]) {
    const currentMode = parsedProperties.value[index].mode
    const newMode = currentMode === 'edit' ? 'preview' : 'edit'

    console.log(`Changing mode from ${currentMode} to ${newMode}`)
    parsedProperties.value[index].mode = newMode
  }
}

// Toggle all properties mode
const toggleAllMode = () => {
  showAllInEditMode.value = !showAllInEditMode.value
  const newMode = showAllInEditMode.value ? 'edit' : 'preview'

  console.log('Toggling all modes to:', newMode)

  parsedProperties.value.forEach((property) => {
    property.mode = newMode
  })
}

// Toggle content mode for non-JSON
const toggleContentMode = () => {
  contentMode.value = contentMode.value === 'edit' ? 'preview' : 'edit'
  console.log('Content mode changed to:', contentMode.value)
}
</script>

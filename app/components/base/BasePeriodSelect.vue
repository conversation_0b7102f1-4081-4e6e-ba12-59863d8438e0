<script setup lang="ts">
import { eachDayOfInterval } from 'date-fns'
import type { Period, Range } from '~/types'

const model = defineModel({
  type: String as PropType<Period>,
  required: true
})

const props = defineProps({
  range: {
    type: Object as PropType<Range>,
    required: true
  }
})

const days = computed(() => eachDayOfInterval(props.range))

const periods = computed<any[]>(() => {
  // if (days.value.length <= 8) {
  //   return [
  //     {
  //       label: "日次",
  //       id: "daily",
  //     },
  //   ];
  // }

  // if (days.value.length <= 31) {
  //   return [
  //     {
  //       label: "日次",
  //       id: "daily",
  //     },
  //     {
  //       label: "週次",
  //       id: "weekly",
  //     },
  //   ];
  // }

  return [
    {
      label: '日次',
      id: 'daily'
    },
    {
      label: '週次',
      id: 'weekly'
    },
    {
      label: '月次',
      id: 'monthly'
    }
  ]
})

const modelLabel = computed(() => {
  return periods.value.find(period => period?.id === model.value)?.label
})

// Ensure the model value is always a valid period
watch(periods, () => {
  if (!periods.value.includes(model.value)) {
    model.value = periods.value[0]?.id
  }
})
</script>

<template>
  <USelectMenu
    v-slot="{ open }"
    v-model="model"
    :options="periods"
    value-attribute="id"
    option-attribute="label"
    :ui-menu="{ width: 'w-32', option: { base: 'capitalize' } }"
    :popper="{ placement: 'bottom-start' }"
  >
    <UButton
      :label="modelLabel"
      color="gray"
      variant="ghost"
      class="capitalize"
      :class="[open && 'bg-gray-50 dark:bg-gray-800']"
      trailing-icon="i-heroicons-chevron-down-20-solid"
    />
  </USelectMenu>
</template>

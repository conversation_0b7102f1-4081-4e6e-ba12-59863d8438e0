<template>
  <div>
    <UCard
      :ui="{
        header: {
          padding: '!py-0 !px-1'
        },
        rounded: 'rounded-md',
        body: {
          padding: '!p-0 !pt-0'
        }
      }"
    >
      <template #header>
        <UHorizontalNavigation :links="links" />
      </template>
      <BaseEditor
        v-if="mode === 'editor'"
        v-model="message"
        placeholder="開始時メッセージ..."
        language="html"
        :disabled="disabled"
      />
      <div
        v-else
        class="px-4 py-6 pb-6"
      >
        <ChatBubble
          :sender-name="currentChatbotName"
          :sender-avatar="currentChatbotAvatarURL"
          :message="message"
          :prompt-tokens="promptTokens"
          :completion-tokens="completionTokens"
          :token-count="tokenCount"
        />
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: [String, Object],
    default: ''
  },
  updatedSuccess: <PERSON><PERSON>an,
  loading: <PERSON><PERSON><PERSON>,
  disabled: Boolean,
  promptTokens: {
    type: Number,
    default: 0
  },
  completionTokens: {
    type: Number,
    default: 0
  },
  tokenCount: {
    type: Number,
    default: 0
  }
})

const settingsStore = useSettingsStore()

const { currentChatbotAvatarURL, currentChatbotName }
  = storeToRefs(settingsStore)

const inputIcon = computed(() => {
  if (props.updatedSuccess) return 'ep:success-filled'
  if (props.loading) return 'eos-icons:loading'
  return 'fluent:slide-text-edit-20-regular'
})

const mode = ref('editor') // editor, preview
const links = computed(() => [
  {
    icon: inputIcon.value,
    label: 'エディタ',
    click: (): void => { mode.value = 'editor' },
    active: mode.value === 'editor',
    iconClass: props.updatedSuccess ? 'text-green-400 dark:text-green-500' : ''
  },
  {
    icon: 'hugeicons:message-preview-01',
    label: 'プレビュー',
    click: (): void => { mode.value = 'preview' },
    active: mode.value === 'preview'
  }
])

const emit = defineEmits(['update:modelValue'])

const message = computed({
  get: () => {
    // If modelValue is an object, convert it to string
    if (typeof props.modelValue === 'object' && props.modelValue !== null) {
      return JSON.stringify(props.modelValue)
    }
    return props.modelValue as string
  },
  set: value => emit('update:modelValue', value)
})
</script>

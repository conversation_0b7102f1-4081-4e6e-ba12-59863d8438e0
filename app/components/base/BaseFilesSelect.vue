<template>
  <div class="max-w-xl">
    <label
      v-if="!props.modelValue"
      v-bind="$attrs"
      class="flex justify-center w-full h-36 px-4 transition bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 border-dashed rounded-md appearance-none cursor-pointer hover:border-gray-400 dark:hover:border-primary-800 focus:outline-none"
      :class="{
        'border-blue-600': dragging,
        'border-gray-300': !dragging
      }"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @drop="handleFileSelect"
    >
      <span class="flex flex-col items-center gap-2 justify-center">
        <UIcon
          name="mdi:file-document-plus"
          class="text-gray-500 text-4xl"
        />
        <span class="font-medium text-gray-600 text-sm">
          <span v-if="dragging"> ここにファイルをドロップしてください </span>
          <span v-else>ここにファイルをドラッグし、または
            <span class="text-primary-600 underline">
              ファイルを選択
            </span></span>
        </span>
      </span>
      <input
        :value="filesInput"
        type="file"
        name="file_upload"
        class="hidden"
        :accept="props.accept"
        multiple
        @change="onSelectFiles"
      >
    </label>
    <div
      v-else
      class="flex items-center gap-2 p-2 bg-white dark:bg-gray-900 dark:border-gray-700 border border-gray-300 rounded-md"
    >
      <UIcon
        :name="contextTypeIcon(props.modelValue.type)"
        class="text-gray-500 text-4xl"
      />
      <div class="flex flex-col gap-1">
        <span class="font-medium dark:text-gray-400 text-sm">
          {{ props.modelValue.name }}
        </span>
        <span class="text-xs text-gray-500 dark:text-gray-400">
          {{ bytesToSize(props.modelValue.size) }}
        </span>
      </div>
      <div class="ml-auto">
        <UButton
          color="white"
          size="xs"
          icon="mdi:close"
          @click="emit('update:modelValue', null)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: File
  accept: string
}>()

const dragging = ref(false)

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  dragging.value = true
}

const handleDragLeave = () => {
  dragging.value = false
}

const handleFileSelect = (event: DragEvent) => {
  event.preventDefault()
  dragging.value = false
  const files = event.dataTransfer?.files
  processFileInput(files, event)
}

const emit = defineEmits(['update:modelValue', 'handle-multiple-files'])

const filesInput = ref<File | null>()
const processFileInput = (files: FileList | undefined, event?: any) => {
  let acceptedFiles: File[] = []
  // check if accepted file types and remove invalid files
  if (files?.length) {
    // Add text/markdown type to .md files in case browser fails to identify
    const mdCheckedFiles = Array.from(files).map((file) => {
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
      if (fileExtension === '.md' && !file.type) {
        return new File([file], file.name, { type: 'text/markdown' })
      }
      return file
    })

    acceptedFiles = Array.from(mdCheckedFiles).filter((file) => {
      console.log('🚀 ~ acceptedFiles=Array.from ~ file:', file)

      const isValid = props.accept
        ? props.accept.split(',').some(type => file.type.includes(type))
        : true
      return isValid
    })

    if (!acceptedFiles.length) {
      console.error('Invalid file type')
      return
    }
  }

  if (acceptedFiles?.length) {
    emit('update:modelValue', acceptedFiles[0])
  }

  if (acceptedFiles?.length && acceptedFiles?.length > 1) {
    const remainFiles = Array.from(acceptedFiles).slice(1)
    emit('handle-multiple-files', remainFiles)
  }

  // reset the input value
  (event?.target as HTMLInputElement).value = ''
  filesInput.value = null
}
const onSelectFiles = (event: Event) => {
  const files = (event.target as HTMLInputElement).files as FileList
  processFileInput(files, event)
}
</script>

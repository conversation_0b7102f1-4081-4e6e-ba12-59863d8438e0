<script setup lang="ts">
const props = defineProps({
  promptTokens: {
    type: Number,
    default: 0
  },
  completionTokens: {
    type: Number,
    default: 0
  },
  tokenCount: {
    type: Number,
    default: 0
  },
  sessionId: {
    type: String,
    default: ''
  },
  isSessionMode: {
    type: Boolean,
    default: false
  }
})

const { isOperator } = useRoleBasedUser()
const logsStore = useLogsStore()

// Get all logs with the same session ID
const sessionLogs = computed(() => {
  if (!props.isSessionMode || !props.sessionId) {
    return []
  }
  return logsStore.logs.filter(log => log.session_id === props.sessionId)
})

// Format number with commas
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

// Calculate total tokens for the session
const sessionPromptTokens = computed(() => {
  if (!props.isSessionMode) return props.promptTokens
  return sessionLogs.value.reduce(
    (sum, log) => sum + (log.prompt_tokens || 0),
    0
  )
})

const sessionCompletionTokens = computed(() => {
  if (!props.isSessionMode) return props.completionTokens
  return sessionLogs.value.reduce(
    (sum, log) => sum + (log.completion_tokens || 0),
    0
  )
})

const sessionTokenCount = computed(() => {
  if (!props.isSessionMode) return props.tokenCount
  return sessionLogs.value.reduce(
    (sum, log) => sum + (log.token_count || 0),
    0
  )
})

// Formatted token values with commas
const formattedPromptTokens = computed(() =>
  formatNumber(sessionPromptTokens.value)
)
const formattedCompletionTokens = computed(() =>
  formatNumber(sessionCompletionTokens.value)
)
const formattedTokenCount = computed(() =>
  formatNumber(sessionTokenCount.value)
)

const showTokenDetails = computed(() => {
  return (
    isOperator()
    && (sessionPromptTokens.value > 0
      || sessionCompletionTokens.value > 0
      || sessionTokenCount.value > 0)
  )
})
</script>

<template>
  <div v-if="showTokenDetails">
    <div class="text-[10px] font-semibold">
      {{
        `入力トークン数: ${formattedPromptTokens}, 出力トークン数: ${formattedCompletionTokens}, 総トークン数: ${formattedTokenCount}`
      }}
    </div>
  </div>
</template>

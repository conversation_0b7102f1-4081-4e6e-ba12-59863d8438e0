<template>
  <div>
    <codemirror
      v-model="jsonContent"
      :placeholder="placeholder"
      :indent-with-tab="true"
      :tab-size="2"
      :extensions="extensions"
      :style="{
        paddingTop: '0.5rem',
        paddingBottom: '0.5rem'
      }"
      :disabled="disabled"
    />
  </div>
</template>

<script setup lang="ts">
import { Codemirror } from 'vue-codemirror'
import { json } from '@codemirror/lang-json'
import { markdown } from '@codemirror/lang-markdown'
import { html } from '@codemirror/lang-html'
import { oneDark } from '@codemirror/theme-one-dark'
import { EditorView, keymap } from '@codemirror/view'

const colorMode = useColorMode()
const isDark = computed({
  get() {
    return colorMode.value === 'dark'
  },
  set() {
    colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
  }
})
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'ここに入力してください'
  },
  language: {
    type: String,
    default: 'markdown'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const isJsonContent = computed(() => {
  return isJson(props.modelValue)
})

const emits = defineEmits(['update:modelValue', 'blur', 'keydown'])
const extensions = computed(() => {
  const exts: any[] = [
    EditorView.lineWrapping,
    // Add keymap for Escape key
    keymap.of([
      {
        key: 'Escape',
        run: () => {
          emits('keydown', { key: 'Escape' })
          return true
        }
      }
    ]),
    // Add blur handler
    EditorView.domEventHandlers({
      blur: () => {
        emits('blur')
        return false
      }
    })
  ]

  if (isJsonContent.value) {
    exts.push(json())
  } else if (props.language === 'html') {
    exts.push(html())
  } else {
    exts.push(markdown())
  }
  if (isDark.value) {
    exts.push(oneDark)
  }
  return exts
})

const jsonContent = computed({
  get: () => {
    try {
      return JSON.stringify(JSON.parse(props.modelValue), null, 2)
    } catch (e) {
      return props.modelValue
    }
  },
  set: (value: string) => {
    try {
      emits('update:modelValue', value)
    } catch (e) {
      console.error(e)
    }
  }
})
</script>

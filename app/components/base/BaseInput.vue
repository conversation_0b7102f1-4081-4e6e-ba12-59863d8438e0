<template>
  <UInput
    autocomplete="off"
    :icon="inputIcon"
    size="md"
    v-bind="$attrs"
    :ui="{
      icon: {
        base: updatedSuccess ? 'text-green-400 dark:text-green-500' : 'text-gray-400 dark:text-gray-500'
      }
    }"
  />
</template>

<script setup lang="ts">
const props = defineProps({
  icon: String,
  updatedSuccess: Boolean,
  loading: Boolean
})

const inputIcon = computed(() => {
  if (props.updatedSuccess) return 'ep:success-filled'
  if (props.loading) return 'eos-icons:loading'
  return props.icon
})
</script>

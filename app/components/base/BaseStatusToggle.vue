<script setup lang="ts">
const isEnabled = defineModel({
  type: Boolean,
  required: true
})

const confirm = useConfirm()

const emit = defineEmits(['toggle'])

const onChange = () => {
  confirm.show({
    title: isEnabled.value ? '確認' : '確認',
    description: isEnabled.value
      ? '無効にしますか？'
      : '有効にしますか？',
    confirmText: isEnabled.value ? '無効にする' : '有効にする',
    onConfirm: async () => {
      emit('toggle', !isEnabled.value)
    }
  })
}
</script>

<template>
  <UBadge
    :label="isEnabled ? '有効' : '無効'"
    :color="isEnabled ? 'green' : 'white'"
    :variant="isEnabled ? 'subtle' : 'solid'"
    :icon="
      isEnabled ? 'lets-icons:check-fill' : 'lets-icons:close-round-duotone'
    "
    :trailing="false"
    size="sm"
    :ui="{
      rounded: 'rounded-full'
    }"
    class="cursor-pointer"
    @click="onChange"
  />
</template>

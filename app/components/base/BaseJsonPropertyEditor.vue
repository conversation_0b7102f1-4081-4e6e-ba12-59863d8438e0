<template>
  <div class="space-y-4">
    <!-- JSON Properties Editor -->
    <div v-if="isJsonContent && parsedProperties.length > 0">
      <div class="mb-4 flex items-center justify-between">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
          {{ viewMode === 'properties' ? 'プロパティ編集' : 'JSON編集' }}
        </h4>
        <UButton
          v-if="!props.disabled"
          size="xs"
          variant="solid"
          color="gray"
          :icon="viewMode === 'properties' ? 'heroicons:code-bracket' : 'heroicons:squares-2x2'"
          :label="viewMode === 'properties' ? 'JSON編集' : 'プロパティ編集'"
          @click="toggleViewMode"
        />
      </div>

      <!-- Properties View -->
      <div
        v-if="viewMode === 'properties'"
        class="space-y-3"
      >
        <div
          v-for="(property, index) in parsedProperties"
          :key="index"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <div class="mb-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ $t(property.key || '') }}
            </label>
          </div>

          <!-- Edit Mode -->
          <div v-if="property.mode === 'edit' && !props.disabled">
            <BaseEditor
              v-model="property.value"
              :placeholder="`${$t(property.key || property.key)}の内容を入力してください`"
              language="markdown"
              @keydown="(event) => handleKeydown(event, index)"
              @blur="() => setPropertyMode(index, 'preview')"
            />
          </div>

          <!-- Preview Mode -->
          <div
            v-else
            class="p-0 bg-gray-100 dark:bg-gray-800 rounded-md cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            :class="{ 'cursor-not-allowed': props.disabled }"
            @click="() => !props.disabled && setPropertyMode(index, 'edit')"
          >
            <div
              v-if="property.value && isMarkdownContent(property.value)"
              class="p-3 prose-sm dark:prose-invert max-w-none break-words overflow-wrap-anywhere whitespace-pre-wrap"
              v-html="renderMarkdown(property.value)"
            />
            <div
              v-else-if="property.value"
              class="p-3 text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-words overflow-wrap-anywhere"
            >
              {{ property.value }}
            </div>
            <div
              v-else
              class="p-3 text-sm text-gray-400 dark:text-gray-500 italic"
            >
              クリックして編集
            </div>
          </div>
        </div>
      </div>

      <!-- JSON View -->
      <div
        v-else
        class="space-y-3"
      >
        <BaseEditor
          v-model="jsonContent"
          placeholder="JSON内容を入力してください"
          language="json"
          :disabled="props.disabled"
        />
      </div>
    </div>

    <!-- Single Content Editor (for non-JSON content) -->
    <div v-else>
      <div class="mb-4">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
          コンテンツ編集
        </h4>
      </div>

      <!-- Edit Mode -->
      <div v-if="contentMode === 'edit' && !props.disabled">
        <BaseEditor
          v-model="singleContent"
          placeholder="コンテンツを入力してください"
          language="markdown"
          @keydown="handleContentKeydown"
          @blur="() => setContentMode('preview')"
        />
      </div>

      <!-- Preview Mode -->
      <div
        v-else
        class="min-h-[200px] p-4 bg-gray-50 dark:bg-gray-800 rounded-md cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        :class="{ 'cursor-not-allowed': props.disabled }"
        @click="() => !props.disabled && setContentMode('edit')"
      >
        <div
          v-if="singleContent && isMarkdownContent(singleContent)"
          class="prose prose-sm dark:prose-invert max-w-none break-words overflow-wrap-anywhere whitespace-pre-wrap"
          v-html="renderMarkdown(singleContent)"
        />
        <div
          v-else-if="singleContent"
          class="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-words overflow-wrap-anywhere"
        >
          {{ singleContent }}
        </div>
        <div
          v-else
          class="text-sm text-gray-400 dark:text-gray-500 italic"
        >
          クリックして編集
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore
import MarkdownIt from 'markdown-it'

const md = new MarkdownIt()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// Check if content is JSON
const isJsonContent = computed(() => {
  if (!props.modelValue || props.modelValue.trim() === '') {
    return false
  }
  try {
    const parsed = JSON.parse(props.modelValue)
    return (
      typeof parsed === 'object' && parsed !== null && !Array.isArray(parsed)
    )
  } catch {
    return false
  }
})

// Parse JSON into properties
const parsedProperties = ref<
  Array<{
    key: string
    value: string
    mode: 'preview' | 'edit'
  }>
>([])

// Single content for non-JSON
const singleContent = ref('')
const contentMode = ref<'preview' | 'edit'>('preview')

// View mode toggle: 'properties' or 'json'
const viewMode = ref<'properties' | 'json'>('properties')

// JSON content for JSON edit mode
const jsonContent = ref('')

// Flag to prevent infinite loops
const isUpdatingFromParent = ref(false)

// Helper function to properly parse values based on their type
const parseValue = (value: any): string => {
  if (value === null || value === undefined) {
    return ''
  }

  if (typeof value === 'string') {
    return value
  }

  if (typeof value === 'object' || Array.isArray(value)) {
    return JSON.stringify(value, null, 2)
  }

  // For primitives (number, boolean, etc.)
  return String(value)
}

// Helper function to convert string back to original type
const parseStringToValue = (str: string): any => {
  if (str === '') {
    return ''
  }

  // Try to parse as JSON first (for objects and arrays)
  try {
    const parsed = JSON.parse(str)
    // If it's a valid JSON object or array, return it
    if (typeof parsed === 'object') {
      return parsed
    }
    // If it's a primitive value that was JSON-encoded, return it
    return parsed
  } catch {
    // If JSON parsing fails, return as string
    return str
  }
}

// Initialize properties when content changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (isUpdatingFromParent.value) return

    if (isJsonContent.value) {
      try {
        const parsed = JSON.parse(newValue)
        // Only update if the structure has changed to preserve modes
        const newKeys = Object.keys(parsed).sort()
        const currentKeys = parsedProperties.value.map(p => p.key).sort()

        if (JSON.stringify(newKeys) !== JSON.stringify(currentKeys)) {
          parsedProperties.value = Object.entries(parsed).map(
            ([key, value]) => ({
              key,
              value: parseValue(value),
              mode: 'preview' as const
            })
          )
        } else {
          // Update values but preserve modes (unless disabled)
          parsedProperties.value.forEach((prop) => {
            if (parsed[prop.key] !== undefined) {
              prop.value = parseValue(parsed[prop.key])
              // Force preview mode if disabled
              if (props.disabled) {
                prop.mode = 'preview'
              }
            }
          })
        }

        // Update JSON content for JSON view
        jsonContent.value = JSON.stringify(parsed, null, 2)
      } catch {
        parsedProperties.value = []
      }
    } else {
      singleContent.value = newValue || ''
    }
  },
  { immediate: true }
)

// Watch for changes in property values only (not modes) and emit updates
watch(
  () => parsedProperties.value.map(p => ({ key: p.key, value: p.value })),
  (newProperties) => {
    if (isJsonContent.value && newProperties.length > 0) {
      isUpdatingFromParent.value = true
      const jsonObject = newProperties.reduce((acc, prop) => {
        acc[prop.key] = parseStringToValue(prop.value)
        return acc
      }, {} as Record<string, any>)
      emit('update:modelValue', JSON.stringify(jsonObject, null, 2))
      nextTick(() => {
        isUpdatingFromParent.value = false
      })
    }
  },
  { deep: true }
)

// Watch for changes in single content
watch(singleContent, (newContent) => {
  if (!isJsonContent.value) {
    emit('update:modelValue', newContent)
  }
})

// Watch for changes in JSON content and sync back to properties
watch(jsonContent, (newJsonContent) => {
  if (isJsonContent.value && viewMode.value === 'json') {
    try {
      const parsed = JSON.parse(newJsonContent)
      if (typeof parsed === 'object' && parsed !== null && !Array.isArray(parsed)) {
        // Update properties from JSON
        parsedProperties.value = Object.entries(parsed).map(([key, value]) => {
          const existingProp = parsedProperties.value.find(p => p.key === key)
          return {
            key,
            value: parseValue(value),
            mode: existingProp?.mode || 'preview' as const
          }
        })

        // Emit the formatted JSON
        emit('update:modelValue', JSON.stringify(parsed, null, 2))
      }
    } catch {
      // Invalid JSON, don't update
    }
  }
})

// Set property mode directly
const setPropertyMode = (index: number, mode: 'preview' | 'edit') => {
  if (props.disabled) return

  if (parsedProperties.value[index]) {
    parsedProperties.value[index].mode = mode
  }
}

// Toggle view mode between properties and json
const toggleViewMode = () => {
  if (props.disabled) return

  if (viewMode.value === 'properties') {
    // Switching to JSON view - sync current properties to JSON
    if (parsedProperties.value.length > 0) {
      const jsonObject = parsedProperties.value.reduce((acc, prop) => {
        acc[prop.key] = parseStringToValue(prop.value)
        return acc
      }, {} as Record<string, any>)
      jsonContent.value = JSON.stringify(jsonObject, null, 2)
    }
    viewMode.value = 'json'
  } else {
    // Switching to properties view
    viewMode.value = 'properties'
  }
}

// Set content mode directly
const setContentMode = (mode: 'preview' | 'edit') => {
  if (props.disabled) return

  contentMode.value = mode
}

// Handle keydown events for properties
const handleKeydown = (event: { key: string }, index: number) => {
  if (event.key === 'Escape') {
    setPropertyMode(index, 'preview')
  }
}

// Handle keydown events for single content
const handleContentKeydown = (event: { key: string }) => {
  if (event.key === 'Escape') {
    setContentMode('preview')
  }
}

// Check if content is markdown
const isMarkdownContent = (content: string): boolean => {
  const markdownPatterns = [
    /^#{1,6}\s/m, // Headers
    /\*\*.*\*\*/, // Bold
    /\*.*\*/, // Italic
    /\[.*\]\(.*\)/, // Links
    /^[-*+]\s/m, // Lists
    /^>\s/m, // Blockquotes
    /```[\s\S]*```/, // Code blocks
    /`.*`/ // Inline code
  ]

  return markdownPatterns.some(pattern => pattern.test(content))
}

// Render markdown to HTML
const renderMarkdown = (content: string): string => {
  try {
    return md.render(content)
  } catch {
    return content
  }
}
</script>

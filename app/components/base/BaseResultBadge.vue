<script setup lang="ts">
const isSuccess = defineModel({
  type: Boolean,
  required: true
})
</script>

<template>
  <UBadge
    :label="isSuccess ? '成功' : '失敗'"
    :color="isSuccess ? 'green' : 'white'"
    :variant="isSuccess ? 'subtle' : 'solid'"
    :icon="
      isSuccess ? 'lets-icons:check-fill' : 'lets-icons:close-round-duotone'
    "
    :trailing="false"
    size="sm"
    :ui="{
      rounded: 'rounded-full'
    }"
    class="cursor-pointer"
  />
</template>

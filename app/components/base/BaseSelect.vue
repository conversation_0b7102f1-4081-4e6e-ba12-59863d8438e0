<template>
  <USelect
    :icon="inputIcon"
    size="md"
    v-bind="$attrs"
    :ui="{
      icon: {
        base: updatedSuccess ? 'text-green-400 dark:text-green-500' : 'text-gray-400 dark:text-gray-500'
      }
    }"
    :options="options"
  />
</template>

<script setup lang="ts">
const props = defineProps({
  icon: String,
  updatedSuccess: Boolean,
  loading: <PERSON>olean,
  options: Array
})

const inputIcon = computed(() => {
  if (props.updatedSuccess) return 'ep:success-filled'
  if (props.loading) return 'eos-icons:loading'
  return props.icon
})
</script>

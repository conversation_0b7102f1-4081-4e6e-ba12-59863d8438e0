<template>
  <UInput
    autocomplete="new-password"
    v-bind="$attrs"
    :type="isShowPassword ? 'text' : 'password'"
    :ui="{ icon: { trailing: { pointer: '' } } }"
  >
    <template #trailing>
      <UButton
        color="gray"
        variant="link"
        :icon="
          isShowPassword
            ? 'heroicons:eye-slash-20-solid'
            : 'i-heroicons-eye-20-solid'
        "
        :padded="false"
        @click="isShowPassword = !isShowPassword"
      />
    </template>
  </UInput>
</template>

<script setup lang="ts">
const isShowPassword = ref(false)

const props = defineProps({
  showPassword: {
    type: Boolean,
    default: false
  }
})

onMounted(() => {
  isShowPassword.value = props.showPassword
})

watch(() => props.showPassword, (value) => {
  isShowPassword.value = value
})
</script>

<script setup lang="ts">
const { open, loading, confirm } = useConfirm()

const onConfirm = async () => {
  loading.value = true
  await confirm.value.onConfirm()
  loading.value = false
  open.value = false
}
</script>

<template>
  <UDashboardModal
    v-model="open"
    :title="confirm.title"
    :description="confirm.description"
    :icon="confirm.icon"
    :ui="{
      icon: { base: 'text-primary-500 dark:text-primary-400' } as any,
      footer: { base: 'ml-16' } as any
    }"
  >
    <template #footer>
      <UButton
        color="primary"
        :label="confirm.confirmText"
        :loading="loading"
        @click="onConfirm"
      />
      <UButton
        color="white"
        :label="confirm.cancelText"
        @click="open = false"
      />
    </template>
  </UDashboardModal>
</template>

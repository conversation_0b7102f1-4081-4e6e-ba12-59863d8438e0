<script setup lang="ts">
const isEnabled = defineModel({
  type: Boolean,
  required: true
})

const props = defineProps({
  textEnable: {
    type: String,
    default: '有効'
  },
  textDisable: {
    type: String,
    default: '無効'
  }
})

const emit = defineEmits(['toggle'])

const onChange = () => {
  emit('toggle', !isEnabled.value)
}
</script>

<template>
  <UBadge
    :label="isEnabled ? props.textEnable : props.textDisable"
    :color="isEnabled ? 'green' : 'white'"
    :variant="isEnabled ? 'subtle' : 'solid'"
    :icon="
      isEnabled ? 'lets-icons:check-fill' : 'lets-icons:close-round-duotone'
    "
    :trailing="false"
    size="sm"
    :ui="{
      rounded: 'rounded-full'
    }"
    class="cursor-pointer"
    @click="onChange"
  />
</template>

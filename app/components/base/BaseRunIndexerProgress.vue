<template>
  <UDashboardToolbar
    v-if="indexerStatus === 2"
    class="bg-gray-100 dark:bg-gray-800/80 text-gray-800 dark:text-gray-400 relative"
  >
    <div class="flex items-center gap-1.5 text-sm">
      <UIcon
        name="eos-icons:loading"
        class="text-lg"
      />
      インデックス更新中です。完了までしばらくお待ちください。
    </div>
  </UDashboardToolbar>
</template>

<script lang="ts" setup>
const searchIndexersStore = useSearchIndexersStore()
const { selectedTenantId, selectedEnvId } = useApp()
const { loadings, indexerStatus } = storeToRefs(searchIndexersStore)

const syncIndexerStatusInterval = ref()
const syncIndexerStatus = () => {
  if (syncIndexerStatusInterval.value) {
    clearInterval(syncIndexerStatusInterval.value)
  }
  syncIndexerStatusInterval.value = setInterval(async () => {
    await searchIndexersStore.getIndexerStatus(
      selectedTenantId.value,
      selectedEnvId.value
    )
    if (indexerStatus.value !== 2) {
      clearInterval(syncIndexerStatusInterval.value)
    }
  }, 30000)
}

onMounted(async () => {
  // check if indexerStatus is not 2
  if (indexerStatus.value === 2) {
    syncIndexerStatus()
  }
})

// watch indexerStatus
watch(
  () => indexerStatus.value,
  () => {
    if (indexerStatus.value === 2) {
      syncIndexerStatus()
    }
  }
)
</script>

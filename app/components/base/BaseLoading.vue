<template>
  <div class="flex flex-col gap-3 items-center justify-center h-full">
    <UIcon
      :name="icon"
      class="text-2xl text-gray-500 dark:text-gray-600"
    />
    <p class="text-sm text-gray-500 dark:text-gray-600">
      {{ text }}
    </p>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  text: {
    type: String,
    default: '読み込み中'
  },
  icon: {
    type: String,
    default: 'eos-icons:loading'
  }
})
</script>

<script setup lang="ts">
const { t } = useI18n()
const props = defineProps({
  analyzedAction: {
    type: Number,
    default: 0
  },
  contextType: {
    type: Number,
    default: 0
  },
  userType: {
    type: String,
    default: ''
  },
  username: {
    type: String,
    default: ''
  },
  sessionId: {
    type: String,
    default: ''
  },
  isSessionMode: {
    type: Boolean,
    default: false
  }
})

const { isOperator } = useRoleBasedUser()
const logsStore = useLogsStore()

// Get all logs with the same session ID
const sessionLogs = computed(() => {
  if (!props.isSessionMode || !props.sessionId) {
    return []
  }
  return logsStore.logs.filter(log => log.session_id === props.sessionId)
})

// Map analyzed_action to human-readable text
const getAnalyzedActionText = (action: number): string => {
  switch (action) {
    case 1:
      return '通常'
    case 2:
      return 'RAG'
    case 3:
      return 'まとめ'
    case 101:
      return '天気'
    case 199:
      return 'キャッシュ'
    default:
      return '不明'
  }
}

// Map context_type to human-readable text
const getContextTypeText = (type: number): string => {
  switch (type) {
    case 0:
      return '不明（以前の記録）'
    case 1:
      return 'ナレッジ'
    case 2:
      return 'ウェブ検索'
    case 99:
      return '対応ナレッジなし'
    default:
      return '不明'
  }
}

// Get analyzed action for display
const displayAnalyzedAction = computed(() => {
  if (props.isSessionMode && sessionLogs.value.length > 0) {
    // For session mode, use the most recent log's analyzed_action
    const mostRecentLog = sessionLogs.value.reduce((latest, current) => {
      if (!latest) return current
      return new Date(current.answer_created_at)
        > new Date(latest.answer_created_at)
        ? current
        : latest
    }, null)
    return getAnalyzedActionText(
      mostRecentLog?.analyzed_action || props.analyzedAction
    )
  }
  return getAnalyzedActionText(props.analyzedAction)
})

// Get context type for display
const displayContextType = computed(() => {
  if (props.isSessionMode && sessionLogs.value.length > 0) {
    // For session mode, use the most recent log's context_type
    const mostRecentLog = sessionLogs.value.reduce((latest, current) => {
      if (!latest) return current
      return new Date(current.answer_created_at)
        > new Date(latest.answer_created_at)
        ? current
        : latest
    }, null)
    return getContextTypeText(mostRecentLog?.context_type || props.contextType)
  }
  return getContextTypeText(props.contextType)
})

// Get user type and username for display
const displayUserType = computed(() => props.userType || '不明')
const displayUsername = computed(() => props.username || '不明')

const showAnalysisDetails = computed(() => {
  return (
    props.analyzedAction !== undefined
    || props.contextType !== undefined
    || props.userType
    || props.username
  )
})

const analysisInfos = computed(() => {
  return [
    {
      label: 'RAGパターン',
      value: displayAnalyzedAction.value
    },
    {
      label: 'ナレッジの種類',
      value: displayContextType.value
    },
    {
      label: 'ユーザータイプ',
      value: t(displayUserType.value),
      hide: !props.userType
    },
    {
      label: 'ユーザーネーム',
      value: displayUsername.value,
      hide: !props.username
    }
  ].filter(info => !info.hide)
})
</script>

<template>
  <div v-if="showAnalysisDetails">
    <div class="text-[10px] text-gray-500 dark:text-gray-400">
      <div
        v-for="(info, index) in analysisInfos"
        :key="index"
        class="inline-flex items-center"
      >
        <UTooltip :text="info.label">
          <div>
            {{ info.value }}
          </div>
        </UTooltip>
        <UDivider
          v-if="index < analysisInfos.length - 1"
          orientation="vertical"
          class="h-3 mx-1.5"
        />
      </div>
    </div>
  </div>
</template>

<template>
  <div
    class="py-2 px-3 border border-gray-200 dark:border-gray-700 rounded-md text-gray-600 w-fit"
    :class="{
      'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800':
        !disabled && !loading,
      'cursor-not-allowed bg-gray-200 dark:bg-gray-800': disabled
    }"
    @click="disabled ? null : $emit('update:modelValue', !modelValue)"
  >
    <div class="flex items-center gap-2">
      <UIcon
        :name="
          loading
            ? 'eos-icons:loading'
            : modelValue
              ? 'material-symbols:check-circle'
              : 'material-symbols-light:circle-outline'
        "
        class="text-xl"
        :class="{
          'text-primary-600': modelValue && !disabled
        }"
      />
      <div>
        <span class="text-sm font-bold dark:text-gray-200">
          {{ label }}
        </span>
        <p class="text-xs text-gray-500">
          {{ help }}
        </p>
      </div>
      <div class="ml-auto">
        <slot name="right" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  modelValue: Boolean,
  label: String,
  help: String,
  value: String,
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: <PERSON>olean,
    default: false
  }
})

defineEmits(['update:modelValue'])
</script>

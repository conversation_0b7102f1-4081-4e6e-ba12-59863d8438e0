<template>
  <UInput
    autocomplete="off"
    v-bind="$attrs"
    type="number"
    :ui="{ icon: { trailing: { pointer: '' } } }"
    :model-value="props.modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    @blur="onBlur"
  />
</template>

<script setup lang="ts">
const MAX_PRIORITY = 9999
const MIN_PRIORITY = 1
const props = defineProps({
  modelValue: {
    type: Number,
    required: true
  }
})
const emit = defineEmits(['update:modelValue'])

// Emit the new value when the input loses focus
const onBlur = () => {
  const value = props.modelValue
  if (value < MIN_PRIORITY) {
    emit('update:modelValue', MIN_PRIORITY)
  } else if (value > MAX_PRIORITY) {
    emit('update:modelValue', MAX_PRIORITY)
  } else {
    emit('update:modelValue', value)
  }
}
</script>

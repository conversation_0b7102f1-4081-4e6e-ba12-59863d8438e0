<template>
  <div class="flex flex-col gap-3 items-center justify-center h-full">
    <UIcon
      :name="icon"
      class="text-7xl text-gray-500 dark:text-gray-600"
    />
    <p class="text-gray-500 dark:text-gray-600">
      {{ text }}
    </p>
    <UButton
      v-if="initButton && !disabled"
      icon="formkit:add"
      size="sm"
      color="primary"
      variant="outline"
      :label="initButtonLabel"
      :trailing="false"
      :ui="{ rounded: 'rounded-full' }"
      class="px-4"
      @click="emit('init')"
    />
  </div>
</template>

<script lang="ts" setup>
defineProps({
  text: {
    type: String,
    default: 'データが見つかりませんでした。'
  },
  icon: {
    type: String,
    default: 'ix:box-open'
  },
  initButton: {
    type: Boolean,
    default: false
  },
  initButtonLabel: {
    type: String,
    default: '新規作成'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['init'])
</script>

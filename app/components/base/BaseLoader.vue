<template>
  <svg
    viewBox="0 -7 114 100"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <path
        id="path-1"
        d="M9,11 L67,11 C69.209139,11 71,12.790861 71,15 L71,51 C71,53.209139 69.209139,55 67,55 L37.6932957,55 L26.3332233,64.3774667 L26.3332233,55 L9,55 C6.790861,55 5,53.209139 5,51 L5,15 C5,12.790861 6.790861,11 9,11 Z"
      />
      <path
        id="path-2"
        d="M0.792362478,35 L42,35 C44.209139,35 46,36.790861 46,39 L46,87 C59.822686,84.4569181 68.4249244,80.615076 71.8067154,75.4744736 C88.1879602,50.5736248 84.7014825,20.5774367 75.0297927,13.9599647 C67.3217444,8.68603691 47.6505507,-3.61507495 23.3946824,2.72389366 C12.9920842,5.44248293 5.45797754,16.201185 0.792362478,35 Z"
      />
    </defs>
    <g
      id="Page-1"
      stroke="none"
      stroke-width="1"
      fill="none"
      fill-rule="evenodd"
    >
      <g id="cb1">
        <use
          fill-rule="evenodd"
          xlink:href="#path-1"
          class="dark:fill-primary-600 fill-primary-400"
        />
        <path
          class="dark:stroke-gray-200 stroke-primary-800"
          stroke-width="5"
          d="M38.5918445,57.5 L23.8332233,69.682887 L23.8332233,57.5 L9,57.5 C5.41014913,57.5 2.5,54.5898509 2.5,51 L2.5,15 C2.5,11.4101491 5.41014913,8.5 9,8.5 L67,8.5 C70.5898509,8.5 73.5,11.4101491 73.5,15 L73.5,51 C73.5,54.5898509 70.5898509,57.5 67,57.5 L38.5918445,57.5 Z"
        />
      </g>
      <g
        id="cb2"
        transform="translate(29.000000, -28.000000)"
      >
        <mask
          id="mask-3"
          fill="white"
        >
          <use xlink:href="#path-2" />
        </mask>
        <g id="Mask" />
        <path
          id="Rectangle-2"
          d="M15,28 L65,28 C67.209139,28 69,29.790861 69,32 L69,65 C69,67.209139 67.209139,69 65,69 L56.2908142,69 L56.2908142,77.8647359 L45.3815482,69 L15,69 C12.790861,69 11,67.209139 11,65 L11,32 C11,29.790861 12.790861,28 15,28 Z"
          mask="url(#mask-3)"
          class="dark:fill-gray-400 fill-gray-800"
        />
      </g>
      <path
        id="line1"
        d="M20.5,23.5 L42.5,23.5"
        class="stroke-white"
        stroke-width="3"
        stroke-linecap="round"
      />
      <path
        id="line2"
        d="M20.5,32.5 L55.5,32.5"
        class="stroke-white"
        stroke-width="3"
        stroke-linecap="round"
      />
      <path
        id="line3"
        d="M20.5,41.5 L55.5,41.5"
        class="stroke-white"
        stroke-width="3"
        stroke-linecap="round"
      />
    </g>
  </svg>
</template>

<style>
#cb1 {
  animation: pop 0.4s linear 0.1s forwards;
  transform-origin: bottom;
  opacity: 0;
}

@keyframes pop {
  0% {
    opacity: 0;
    transform: scale(0.4, 0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.12, 1.12);
  }
  100% {
    opacity: 1;
    transform: scale(1, 1);
  }
}

#line1 {
  fill: none;
  stroke: #fff;
  stroke-width: 3;
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  -webkit-animation: dash 2s linear infinite forwards;
  animation: dash 2s linear infinite forwards;
  animation-delay: 0.5s;
}

#line2 {
  fill: none;
  stroke: #fff;
  stroke-width: 3;
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  -webkit-animation: dash 2s linear infinite forwards;
  animation: dash 2s linear infinite forwards;
  animation-delay: 0.7s;
}

#line3 {
  fill: none;
  stroke: #fff;
  stroke-width: 3;
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  -webkit-animation: dash 2s linear infinite forwards;
  animation: dash 2s linear infinite forwards;
  animation-delay: 0.9s;
}

@-webkit-keyframes dash {
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    opacity: 0;
  }
}
@keyframes dash {
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    opacity: 0;
  }
}

#Rectangle-2 {
  opacity: 0;
  top: -10px;
  animation: pop 0.4s linear 0.25s forwards;
  transform-origin: bottom;
}
</style>

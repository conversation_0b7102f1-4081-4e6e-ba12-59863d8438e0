<template>
  <UInput
    v-bind="$attrs"
    :model-value="modelValue"
    autocomplete="uuid"
    :ui="{ icon: { trailing: { pointer: '' } } }"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template #trailing>
      <UButton
        color="gray"
        variant="link"
        size="xs"
        icon="fluent:clipboard-paste-16-filled"
        :padded="false"
        label="貼り付け"
        @click="onPaste"
      />
    </template>
  </UInput>
</template>

<script setup lang="ts">
defineModel({
  type: String,
  required: true
})

const emit = defineEmits(['update:modelValue'])

const onPaste = async () => {
  const text = await navigator.clipboard.readText()
  emit('update:modelValue', text)
}
</script>

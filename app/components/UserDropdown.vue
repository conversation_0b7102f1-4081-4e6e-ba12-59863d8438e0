<script setup lang="ts">
const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()
const { user, openUserMenu, isChangePasswordModalOpen }
  = storeToRefs(authStore)
const colorMode = useColorMode()

const toggleColorMode = () => {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}

const openChangePasswordModal = () => {
  isChangePasswordModalOpen.value = true
}

const goToSupport = () => {
  router.push(`/${route.params.tenantId}/${route.params.env}/support/tours`)
}

const { canUseFeature, previewBadge } = useFeatures()
const items = computed(() =>
  [
    [
      {
        slot: 'account',
        label: '',
        disabled: true
      }
    ],
    [
      {
        label: 'パスワード変更',
        icon: 'i-heroicons-key',
        click: openChangePasswordModal,
        class: 'change-password'
      },
      {
        label: colorMode.value === 'dark' ? 'ライトモード' : 'ダークモード',
        icon:
          colorMode.value === 'dark' ? 'i-heroicons-sun' : 'i-heroicons-moon',
        click: toggleColorMode
      },
      {
        label: '操作マニュアル',
        icon: 'vaadin:open-book',
        to: 'https://playnext-lab.notion.site/1f384d393a9c805b8944fce3560616f9',
        target: '_blank',
        rel: 'noopener noreferrer',
        external: true
      },
      {
        label: 'サポート',
        icon: 'streamline:manual-book-remix',
        click: goToSupport,
        hide: !canUseFeature('support'),
        badge: previewBadge('support')
      }
    ].filter(item => !item.hide),
    [
      {
        label: '設定',
        icon: 'i-heroicons-cog-6-tooth',
        to: `/${route.params.tenantId}/${route.params.env}/settings`,
        external: false
      }
    ],
    [
      {
        label: 'ログアウト',
        icon: 'i-heroicons-arrow-left-on-rectangle',
        click: () => authStore.logout()
      }
    ]
  ].filter(item => item.some((i: any) => !i.hide))
)
</script>

<template>
  <UDropdown
    v-model:open="openUserMenu"
    mode="click"
    :items="items"
    :ui="{ width: 'w-full', item: { disabled: 'cursor-text select-text' } }"
    :popper="{ strategy: 'absolute', placement: 'top' }"
    class="w-full"
  >
    <template #default="{ open }">
      <UButton
        color="gray"
        variant="ghost"
        class="w-full"
        :label="user?.username"
        :class="[open && 'bg-gray-50 dark:bg-gray-800']"
      >
        <template #leading>
          <UAvatar
            icon="i-heroicons-user-circle"
            size="2xs"
            :ui="{
              icon: {
                base: 'text-gray-500 dark:text-gray-400',
                size: {
                  '2xs': 'w-6 h-6'
                }
              }
            }"
          />
        </template>

        <template #trailing>
          <UIcon
            name="i-heroicons-ellipsis-vertical"
            class="w-5 h-5 ml-auto"
          />
        </template>
      </UButton>
    </template>
    <template #item="{ item }">
      <div class="flex flex-row items-center justify-between w-full">
        <div class="flex flex-row items-center gap-2">
          <UIcon
            v-if="item.icon"
            :name="item.icon"
            class="w-4 h-4"
          />
          <div>
            {{ item.label }}
          </div>
        </div>
        <UBadge
          v-if="item.badge"
          v-bind="item.badge"
        />
      </div>
    </template>
    <template #account>
      <div class="flex flex-row w-full items-center justify-between">
        <div class="text-left w-1/2">
          <p class="truncate font-medium text-gray-900 dark:text-white">
            {{ user?.username }}
          </p>
        </div>
        <div>
          <UBadge>
            {{ $t(user?.role || "") }}
          </UBadge>
        </div>
      </div>
    </template>
  </UDropdown>

  <!-- Change Password Modal -->
  <ChangePasswordModal v-model="isChangePasswordModalOpen" />
</template>

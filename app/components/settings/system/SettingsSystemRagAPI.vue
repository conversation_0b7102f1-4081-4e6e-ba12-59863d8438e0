<template>
  <div>
    <SettingsSystemLoadings v-if="loadings['getSetting']" />
    <UForm
      v-else
      :schema="schema"
      :state="setting"
      class="space-y-4"
      @submit="onSubmit"
    >
      <div class="flex flex-row gap-4 w-full">
        <div class="w-full flex flex-col gap-4 py-2">
          <UFormGroup
            v-for="field in editableSettingFields"
            :key="field.key"
            :label="field.label"
            :name="field.key"
            :required="field.required"
          >
            <BasePasswordInput
              v-if="field.type === 'password'"
              v-model="setting[field.key]"
            />
            <UInput
              v-else
              v-model="setting[field.key]"
              :disabled="field.readonly"
              :color="field.readonly ? 'gray' : 'white'"
            />
          </UFormGroup>
          <UAlert
            v-if="errors['createOrUpdateSetting']"
            icon="material-symbols:error"
            color="red"
            variant="subtle"
            title="エラー"
            :description="errors['createOrUpdateSetting']"
          />
          <BasicFormButtonGroup
            cancel-label="リセット"
            :loading="loadings.createOrUpdateSetting"
            @close="onReset"
          />
        </div>
        <div
          class="w-[45%] rounded-xl flex flex-col gap-4 bg-gray-50 dark:bg-gray-700 px-4 -mt-2 py-4 border border-gray-300 dark:border-gray-800"
        >
          <UFormGroup
            v-for="field in readonlySettingFields"
            :key="field.key"
            :label="field.label"
            :name="field.key"
            :required="field.required"
          >
            <BasePasswordInput
              v-if="field.type === 'password'"
              v-model="setting[field.key]"
            />
            <UInput
              v-else
              v-model="setting[field.key]"
              :disabled="field.readonly"
              :color="field.readonly ? 'gray' : 'white'"
            />
          </UFormGroup>
        </div>
      </div>
    </UForm>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const infraMainStore = useInfraMainStore()
const {
  setting,
  loadings,
  errors,
  editableSettingFields,
  readonlySettingFields
} = storeToRefs(infraMainStore)
const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()
const confirm = useConfirm()
const toast = useToast()
const schema = z.object({
  endpoint: z.string().url('有効なURLを入力してください。'),
  index_name: z
    .string()
    .min(1, 'このフィールドは必須です。')
    .max(100, '100文字以下で入力してください。'),
  key: z
    .string()
    .min(1, 'このフィールドは必須です。')
    .max(100, '100文字以下で入力してください。'),
  embeddings_main_model_name: z
    .string()
    .min(1, 'このフィールドは必須です。')
    .max(100, '100文字以下で入力してください。'),
  embeddings_sub_model_name: z
    .string()
    .max(100, '100文字以下で入力してください。')
    .transform(value => value.trim() === '' ? null : value.trim())
    .nullable()
    .optional(),
  llm_main_model_name: z
    .string()
    .min(1, 'このフィールドは必須です。')
    .max(100, '100文字以下で入力してください。'),
  llm_sub_model_name: z
    .string()
    .max(100, '100文字以下で入力してください。')
    .transform(value => value.trim() === '' ? null : value.trim())
    .optional()
    .nullable()
})

type Schema = z.output<typeof schema>

onMounted(() => {
  infraMainStore.getSetting(selectedTenantId.value, selectedEnvId.value)
})

async function onSubmit(event: FormSubmitEvent<Schema>) {
  confirm.show({
    title: `設定更新の確認`,
    description: `設定を更新しますか？`,
    confirmText: '更新',
    onConfirm: async () => {
      // set to null if any field is empty
      const result = await infraMainStore.createOrUpdateSetting(
        selectedTenantId.value,
        selectedEnvId.value,
        event.data
      )
      if (result) {
        toast.add({
          id: 'setting-update',
          title: '設定更新',
          description: '設定を更新しました。',
          color: 'green'
        })
      }
    }
  })
}

const onReset = () => {
  confirm.show({
    title: `設定リセットの確認`,
    description: `設定をリセットしますか？`,
    confirmText: 'リセット',
    onConfirm: async () => {
      await infraMainStore.getSetting(
        selectedTenantId.value,
        selectedEnvId.value
      )
    }
  })
}
</script>

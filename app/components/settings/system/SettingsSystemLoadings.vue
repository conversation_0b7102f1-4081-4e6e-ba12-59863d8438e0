<template>
  <div>
    <div class="space-y-4">
      <div class="flex flex-row gap-4 w-full">
        <div class="w-full flex flex-col gap-4 py-2">
          <UFormGroup
            v-for="field in 7"
            :key="field"
          >
            <template #label>
              <USkeleton class="h-3 w-[150px] mb-1" />
            </template>
            <USkeleton class="h-9 w-full" />
          </UFormGroup>
          <div class="flex flex-row gap-3 justify-end">
            <USkeleton class="h-9 w-[120px]" />
            <USkeleton class="h-9 w-[120px]" />
          </div>
        </div>
        <div
          class="w-[45%] rounded-xl flex flex-col gap-4 bg-gray-50 dark:bg-gray-700 px-4 -mt-2 py-4 border border-gray-300 dark:border-gray-800"
        >
          <UFormGroup
            v-for="field in 5"
            :key="field"
          >
            <template #label>
              <USkeleton class="h-3 w-[150px] mb-1" />
            </template>
            <USkeleton class="h-9 w-full" />
          </UFormGroup>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

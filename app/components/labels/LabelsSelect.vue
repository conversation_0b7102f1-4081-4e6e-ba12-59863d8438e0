<script setup lang="ts">
const { selectedTenantId, selectedEnvId } = useApp()

const labelsStore = useLabelsStore()
const { labels: labelsOptions, loadings } = storeToRefs(labelsStore)

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const labelsModel = computed({
  get: () => {
    return labelsOptions.value?.filter(option =>
      props.modelValue.includes(option.label)
    )
  },
  set: async (labels) => {
    const promises = labels.map(async (label: any) => {
      if (labelsOptions.value?.some(option => option.label === label.label)) {
        return label
      }

      // In a real app, you would make an API call to create the label
      const newLabel = await labelsStore.createLabel(
        selectedTenantId.value,
        selectedEnvId.value,
        {
          label: label.label
        }
      )

      return newLabel
    })

    const newLabels = await Promise.all(promises)
    emit(
      'update:modelValue',
      newLabels.map((label: any) => label.label || label)
    )
  }
})

onMounted(() => {
  labelsStore.fetchLabels(selectedTenantId.value, selectedEnvId.value)
})
</script>

<template>
  <USelectMenu
    v-model="labelsModel"
    by="key"
    name="labels"
    :options="labelsOptions"
    multiple
    searchable
    creatable
    searchable-placeholder="ラベルを検索"
    :loading="loadings.fetchLabels"
  >
    <template #label>
      <template v-if="labelsModel?.length">
        <span class="flex items-center -space-x-1">
          <span
            v-for="label of labelsModel"
            :key="label.key"
            class="flex-shrink-0 w-2 h-2 mt-px rounded-full"
            :style="{ background: `#${generateColorFromString(label.key)}` }"
          />
        </span>
        <span class="truncate">{{ labelsModel.map((label) => label.label).join(", ") }}
        </span>
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate">
          ラベルを選択
        </span>
      </template>
    </template>

    <template #option="{ option }">
      <span
        class="flex-shrink-0 w-2 h-2 mt-px rounded-full"
        :style="{ background: `#${generateColorFromString(option.key)}` }"
      />
      <span class="truncate">{{ option.label }}</span>
    </template>

    <template #option-create="{ option }">
      <span class="flex-shrink-0">新規ラベル:</span>
      <span class="block truncate">{{ option.label }}</span>
    </template>
  </USelectMenu>
</template>

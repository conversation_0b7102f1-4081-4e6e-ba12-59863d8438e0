<script setup lang="ts">
const { selectedTenantId, selectedEnvId } = useApp()

const labelsStore = useLabelsStore()
const { labels: labelsOptions, loadings } = storeToRefs(labelsStore)

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const labelsModel = computed(() => {
  return labelsOptions.value?.filter(option =>
    props.modelValue.includes(option.label)
  )
})

onMounted(() => {
  labelsStore.fetchLabels(selectedTenantId.value, selectedEnvId.value)
})
</script>

<template>
  <div
    v-if="labelsModel?.length"
    class="flex flex-wrap gap-1"
  >
    <span
      v-for="label of labelsModel"
      :key="label.key"
      class="flex items-center gap-1"
    >
      <span
        class="flex-shrink-0 w-2 h-2 mt-px rounded-full"
        :style="{ background: `#${generateColorFromString(label.key)}` }"
      />
      <span class="text-xs mr-2">
        {{ label.label }}
      </span>
    </span>
  </div>
  <div v-else>
    <span class="text-xs text-gray-500 dark:text-gray-400 truncate">
      ラベルなし
    </span>
  </div>
</template>

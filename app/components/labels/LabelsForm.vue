<script setup lang="ts">
import type { FormError, FormSubmitEvent } from '#ui/types'

const emit = defineEmits(['close', 'submit'])
const props = defineProps<{
  label?: string
  labelKey?: string
  loading?: boolean
  error: any
}>()

const state = reactive({
  label: '',
  labelKey: null
})

const validate = (state: any): FormError[] => {
  const errors = []
  if (!state.label)
    errors.push({ path: 'label', message: 'ラベルが必要です。' })
  return errors
}

async function onSubmit(event: FormSubmitEvent<any>) {
  // Do something with data
  emit('submit', event.data)
  // emit('close')
}

onMounted(() => {
  if (props.labelKey) {
    state.labelKey = props.labelKey
  }
  if (props.label) {
    state.label = props.label
  }
})

const formRef = ref()
// watch props.error
watch(
  () => props.error,
  (error) => {
    if (error) {
      formRef.value?.setErrors([
        { path: 'label', message: error?.error_message }
      ])
    }
  }
)
</script>

<template>
  <UForm
    ref="formRef"
    :validate="validate"
    :validate-on="['submit']"
    :state="state"
    class="space-y-4"
    @submit="onSubmit"
  >
    <UFormGroup
      label="ラベル"
      name="label"
      required
    >
      <UInput
        v-model="state.label"
        type="text"
        placeholder="例: 税金"
        autofocus
      />
    </UFormGroup>
    <BasicFormButtonGroup
      class="justify-end"
      :loading="props.loading"
      @close="emit('close')"
    />
  </UForm>
</template>

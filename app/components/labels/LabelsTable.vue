<script setup lang="ts">
const props = defineProps({
  labels: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})
const { isSelectedEnvIsProd } = useApp()
const search = ref('')

const emit = defineEmits(['edit', 'delete', 'create', 'refresh'])

function getItems(label: any) {
  return [
    [
      {
        label: 'ラベル編集',
        click: () => emit('edit', label),
        disabled: isSelectedEnvIsProd.value
      },
      {
        label: 'ラベル削除',
        labelClass: 'text-red-500 dark:text-red-400',
        click: () => emit('delete', label),
        disabled: isSelectedEnvIsProd.value
      }
    ]
  ]
}

const defaultColumns = [
  {
    key: 'label',
    label: 'ラベル',
    sortable: false
  },
  {
    key: 'key',
    label: 'キー',
    sortable: false
  },
  {
    key: 'created_at',
    label: '作成日時',
    sortable: false
  },
  {
    key: 'updated_at',
    label: '更新日時',
    sortable: false
  },
  {
    key: 'actions',
    label: '操作',
    sortable: false,
    class: 'w-fit text-right'
  }
]

const page = ref(1)
const pageTotal = computed(() => props.labels.length)
const pageCount = ref(7)

const labelsFiltered = computed(() => {
  return props.labels.filter((label: any) => {
    return (
      label.label.toLowerCase().includes(search.value.toLowerCase())
      || label.key.toLowerCase().includes(search.value.toLowerCase())
    )
  })
})

const labelsFilteredPaginated = computed(() => {
  return labelsFiltered.value.slice(
    (page.value - 1) * pageCount.value,
    page.value * pageCount.value
  )
})
// watch pageCount, if pageCount changes, reset page to 1
watch(pageCount, () => {
  page.value = 1
})
</script>

<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-3 !py-3' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700'
      },
      footer: { padding: 'p-4' }
    }"
  >
    <template #header>
      <div
        class="flex items-center justify-between"
        data-tour="header"
      >
        <h2
          class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
        >
          ラベル一覧
          <UBadge
            v-if="labelsFiltered.length"
            :label="labelsFiltered.length"
            variant="solid"
            color="gray"
          />
        </h2>
        <UButton
          data-tour="label-create"
          label="ラベル追加"
          icon="formkit:add"
          color="gray"
          size="sm"
          :disabled="isSelectedEnvIsProd"
          @click="emit('create')"
        />
      </div>
    </template>

    <!-- Filters -->
    <div
      class="flex items-center justify-between gap-3 px-4 py-3"
      data-tour="label-search"
    >
      <UInput
        v-model="search"
        icon="i-heroicons-magnifying-glass-20-solid"
        placeholder="検索..."
      />
      <UButton
        icon="prime:sync"
        color="gray"
        size="sm"
        @click="emit('refresh')"
      />
    </div>
    <UTable
      v-if="labels"
      data-tour="table"
      :rows="labelsFilteredPaginated"
      :columns="defaultColumns"
      class="w-full"
      :ui="{
        divide: 'divide-gray-200 dark:divide-gray-800',
        tr: { base: 'group' }
      }"
      :loading="loading"
    >
      <template #label-data="{ row }">
        <div class="flex items-center gap-2 flex-row">
          <span
            class="flex-shrink-0 w-3 h-3 rounded-full"
            :style="{ background: `#${generateColorFromString(row.key)}` }"
          />
          <span class="truncate">{{ row.label }}</span>
        </div>
      </template>
      <template #created_at-data="{ row }">
        <div>
          <div>
            {{ formatDateTime(row.created_at) }}
          </div>
          <div class="text-gray-500 dark:text-gray-500 text-xs">
            作成者: {{ row.created_username }} ({{
              formatDistanceStrictDateTime(row.created_at)
            }})
          </div>
        </div>
      </template>

      <template #updated_at-data="{ row }">
        <div
          v-if="row.updated_at === row.created_at"
          class="text-gray-500 dark:text-gray-500"
        >
          --
        </div>
        <div v-else>
          <div>
            {{ formatDateTime(row.updated_at) }}
          </div>
          <div class="text-gray-500 dark:text-gray-500 text-xs">
            更新者: {{ row. updated_username }} ({{
              formatDistanceStrictDateTime(row.updated_at)
            }})
          </div>
        </div>
      </template>

      <template #actions-data="{ row }">
        <div class="flex flex-row items-end justify-end">
          <UDropdown
            data-tour="dropdown"
            class="hidden group-hover:block"
            :items="getItems(row)"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              class="row-menu"
              color="white"
              icon="charm:menu-meatball"
              size="xs"
              square
            />
          </UDropdown>
        </div>
      </template>
    </UTable>

    <!-- Number of rows & Pagination -->
    <template #footer>
      <div
        class="flex flex-wrap justify-between items-center"
        data-tour="label-footer"
      >
        <div class="flex items-center gap-1.5">
          <span class="text-sm leading-5">表示件数:</span>

          <USelect
            v-model="pageCount"
            data-tour="label-pagecount"
            :options="[5, 7, 10, 20, 30, 50]"
            class="w-20"
          />
        </div>

        <UPagination
          v-model="page"
          data-tour="label-pagination"
          :page-count="pageCount"
          :total="pageTotal"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline'
              }
            }
          }"
        />
      </div>
    </template>
  </UCard>
</template>

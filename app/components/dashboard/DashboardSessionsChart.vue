<script setup lang="ts">
import { format } from 'date-fns'
import {
  VisXYContainer,
  VisBulletLegend,
  VisAxis,
  VisGroupedBar,
  VisCrosshair,
  VisTooltip,
  VisLine
} from '@unovis/vue'
import type { Period, Range } from '~/types'

const reportsStore = useReportsStore()
const {
  xAxis,
  sessionsChartDataset,
  sessionsByCategoriesChartDataset,
  categoriesLegends,
  sessionsTotalOfSelectedCategories
} = storeToRefs(reportsStore)
const cardRef = ref<HTMLElement | null>(null)

const props = defineProps({
  period: {
    type: String as PropType<Period>,
    required: true
  },
  range: {
    type: Object as PropType<Range>,
    required: true
  }
})

type DataRecord = {
  date: Date
  count: number
  data?: {
    [id: string]: number
  }
}

const { width } = useElementSize(cardRef)

// We use `useAsyncData` here to have same random data on the client and server
// const { data } = await useAsyncData<DataRecord[]>(
//   async () => {
//     return xAxis.value.map((date, index) => ({
//       date: new Date(date),
//       count: sessionsChartDataset.value[index] || 0
//     }))
//   },
//   {
//     watch: [() => props.period, () => props.range],
//     default: () => []
//   }
// )

const data = computed(() =>
  xAxis.value.map((date, index) => ({
    date: new Date(date),
    count: sessionsChartDataset.value[index] || 0,
    data: sessionsByCategoriesChartDataset.value[index]
  }))
)

const x = (_: DataRecord, i: number) => i
const y = computed(() => {
  return categoriesLegends.value.map(({ id }) => {
    return (d: DataRecord) => d.data?.[id] || 0
  })
})

const color = (d: DataRecord, i: number) => categoriesLegends.value[i].color
const total = computed(() => sessionsTotalOfSelectedCategories.value)

const formatDate = (date: Date): string => {
  return {
    daily: format(date, 'MM月dd日'),
    weekly: format(date, 'MM月dd日の週'),
    monthly: format(date, 'yyyy年MM月')
  }[props.period]
}

const xTicks = (i: number) => {
  if (i === 0 || i === data.value.length - 1 || !data.value[i]) {
    return ''
  }

  return formatDate(data.value[i].date)
}

const yTicks = (i: number) => {
  if (i === 0 || i === data.value.length - 1 || !data.value[i]) {
    return ''
  }

  return formatNumber(data.value[i].count)
}

const template = (d: DataRecord) =>
  `<div class="text-xs">
    ${formatDate(d.date)} </br>
    ・合計: ${formatNumber(d.count)}件 </br>
  ${categoriesLegends.value
    .filter(({ id }) => d.data?.[id])
    .map(({ id, name }) => {
      const count = d.data?.[id] || 0
      return `・${name}: ${formatNumber(count)}件`
    })
    .join('</br>')}
  </div>`
</script>

<template>
  <UDashboardCard
    ref="cardRef"
    :ui="{ body: { padding: '!pb-3 !px-0' } as any }"
  >
    <template #header>
      <div class="flex items-center justify-between w-full">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400 font-medium mb-1">
            会話セッション数
          </p>
          <p class="text-3xl text-gray-900 dark:text-white font-semibold">
            {{ total }}
          </p>
        </div>
        <VisBulletLegend :items="categoriesLegends" />
      </div>
    </template>
    <VisXYContainer
      :key="props.period"
      :data="data"
      :padding="{ top: 10 }"
      class="h-96"
      :width="width"
      :prevent-empty-domain="true"
    >
      <VisGroupedBar
        :key="sessionsByCategoriesChartDataset.length"
        :x="x"
        :y="y"
        :color="color"
      />

      <VisAxis
        type="x"
        :x="x"
        :tick-format="xTicks"
      />

      <VisCrosshair
        :color="color"
        :template="template"
      />

      <VisTooltip />
    </VisXYContainer>
  </UDashboardCard>
</template>

<style scoped>
.unovis-xy-container {
  --vis-crosshair-line-stroke-color: rgb(var(--color-primary-500));
  --vis-crosshair-circle-stroke-color: #fff;

  --vis-axis-grid-color: rgb(var(--color-gray-200));
  --vis-axis-tick-color: rgb(var(--color-gray-200));
  --vis-axis-tick-label-color: rgb(var(--color-gray-400));

  --vis-tooltip-background-color: #fff;
  --vis-tooltip-border-color: rgb(var(--color-gray-200));
  --vis-tooltip-text-color: rgb(var(--color-gray-900));
}

.dark {
  .unovis-xy-container {
    --vis-crosshair-line-stroke-color: rgb(var(--color-primary-400));
    --vis-crosshair-circle-stroke-color: rgb(var(--color-gray-900));

    --vis-axis-grid-color: rgb(var(--color-gray-800));
    --vis-axis-tick-color: rgb(var(--color-gray-800));
    --vis-axis-tick-label-color: rgb(var(--color-gray-500));

    --vis-tooltip-background-color: rgb(var(--color-gray-900));
    --vis-tooltip-border-color: rgb(var(--color-gray-800));
    --vis-tooltip-text-color: #fff;
  }
}
</style>

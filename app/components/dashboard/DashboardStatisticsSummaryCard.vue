<template>
  <UDashboardCard
    :ui="{
      body: { padding: '!py-0 !px-0' },
      base: 'overflow-hidden group'
    }"
  >
    <div
      class="flex flex-row items-center gap-2 justify-between pt-4 px-4 pb-0"
    >
      <div>
        <p
          class="inline-flex gap-1 items-center text-xs text-gray-500 dark:text-gray-400 font-medium mb-1"
          :class="[`text-${color}-500 dark:text-${color}-400`]"
        >
          {{ title }}
          <UTooltip
            :text="tooltip"
            :popper="{ placement: 'top' }"
          >
            <UIcon
              name="icon-park-solid:info"
              class="text-xs"
            />
          </UTooltip>
        </p>
        <p
          class="text-3xl font-semibold"
          :class="[`text-${color}-500 dark:text-${color}-400`]"
        >
          <NumberAnimation
            :from="0"
            :to="props.value || 0"
            :format="formatNumber"
            :duration="1"
            autoplay
          />
          <span class="text-sm font-medium">
            {{ unit }}
          </span>
        </p>
      </div>
      <template v-if="right">
        <UDivider
          orientation="vertical"
          class="h-11"
        />
        <div>
          <p
            class="inline-flex gap-1 items-center text-xs text-gray-500 dark:text-gray-400 font-medium mb-1"
            :class="[`text-${right?.color}-500 dark:text-${right?.color}-300`]"
          >
            {{ right?.title }}
            <UTooltip
              :text="right?.tooltip"
              :popper="{ placement: 'top' }"
            >
              <UIcon
                name="icon-park-solid:info"
                class="text-xs"
              />
            </UTooltip>
          </p>
          <p
            class="text-3xl font-semibold"
            :class="[`text-${right?.color}-500 dark:text-${right?.color}-300`]"
          >
            <NumberAnimation
              :from="0"
              :to="right?.value || 0"
              :format="formatNumber"
              :duration="1"
              autoplay
            />
            <span class="text-sm font-medium">
              {{ right?.unit }}
            </span>
          </p>
        </div>
      </template>

      <UAvatar
        v-else
        :icon="icon"
        size="md"
        :ui="{
          icon: {
            base: `text-${color}-500 dark:text-${color}-800`
          },
          rounded: 'rounded-lg',
          background: `bg-${color}-100 dark:bg-${color}-800`
        }"
        class="group-hover:scale-125 transition-all duration-200"
      />
    </div>
    <!-- <div class="">
      <VisXYContainer
        :data="data"
        class="h-12"
      >
        <VisArea
          :data="data"
          :x="x"
          :y="y"
          :opacity="0.7"
          :color="colorHex"
        />
        <VisTooltip />
      </VisXYContainer>
    </div> -->
  </UDashboardCard>
</template>

<script setup lang="ts">
import { VisXYContainer, VisArea, VisTooltip } from '@unovis/vue'
import colors from 'tailwindcss/colors'
import NumberAnimation from 'vue-number-animation'

export interface Statistics {
  title: string
  value: string
  unit: string
  color: string
  description: string
  icon: string
}

const props = defineProps({
  title: String,
  value: String,
  unit: String,
  color: String,
  icon: {
    type: String,
    default: 'material-symbols:chart-line'
  },
  range: Object,
  tooltip: String,
  right: {
    type: Object,
    default: null,
    required: false
  },
  chartDataset: Array
})

type DataRecord = { x: number, y: number }
const x = (d: DataRecord) => d.x
const y = (d: DataRecord) => d.y

const data = computed(() => {
  return props.chartDataset?.map((d: any, i: number) => {
    // total all values of d (object)
    if (typeof d === 'object') {
      d = Object.values(d).reduce((a: number, b: number) => a + b, 0)
    }
    return {
      x: i,
      y: d
    }
  })
})

watch(
  () => props.range,
  () => {
    // random data from range.start to range.end
    const { start, end } = props.range
    data.value = Array.from(
      { length: Math.abs(end.getDate() - start.getDate()) },
      (_, i) =>
        ({
          x: i,
          y: Math.floor(Math.random() * 100)
        } as DataRecord)
    )
  }
)

const colorHex = computed(() => {
  return colors[props.color]?.[500] || props.color
})
</script>

<template>
  <UDashboardCard
    :ui="{
      body: { padding: '!py-0 !px-0' },
      base: 'overflow-hidden group'
    }"
  >
    <div
      class="flex flex-row items-center gap-2 justify-between pt-4 px-4 pb-0"
    >
      <div>
        <template v-if="loading">
          <USkeleton class="h-4 w-20 mb-1" />
          <USkeleton class="h-10 w-16" />
        </template>
        <template v-else>
          <p
            class="inline-flex gap-1 items-center text-xs text-gray-500 dark:text-gray-400 font-medium mb-1"
            :class="{
              [`text-${color}-500 dark:text-${color}-400`]: props.value
            }"
          >
            {{ title }}
            <UTooltip
              :text="tooltip"
              :popper="{ placement: 'top' }"
            >
              <UIcon
                name="icon-park-solid:info"
                class="text-xs"
              />
            </UTooltip>
          </p>
          <p
            class="text-4xl"
            :class="{
              [`text-${color}-500 dark:text-${color}-400 font-semibold`]:
                props.value,
              'text-gray-500 dark:text-gray-400 font-light': !props.value
            }"
          >
            <NumberAnimation
              :from="0"
              :to="props.value || 0"
              :format="formatNumber"
              :duration="1"
              autoplay
            />
            <span class="text-sm font-medium">
              {{ unit }}
            </span>
          </p>
        </template>
      </div>
      <template v-if="right">
        <UDivider
          orientation="vertical"
          class="h-11"
        />
        <div>
          <template v-if="loading">
            <USkeleton class="h-4 w-16 mb-1" />
            <USkeleton class="h-8 w-12" />
          </template>
          <template v-else>
            <p
              class="inline-flex gap-1 items-center text-xs text-gray-500 dark:text-gray-400 font-medium mb-1"
              :class="{
                [`text-${right?.color}-500 dark:text-${right?.color}-300`]: right?.value
              }"
            >
              {{ right?.title }}
              <UTooltip
                :text="right?.tooltip"
                :popper="{ placement: 'top' }"
              >
                <UIcon
                  name="icon-park-solid:info"
                  class="text-xs"
                />
              </UTooltip>
            </p>
            <p
              class="text-3xl font-semibold"
              :class="{
                [`text-${right?.color}-500 dark:text-${right?.color}-400`]:
                  right?.value,
                'text-gray-500 dark:text-gray-400 font-light': !right?.value
              }"
            >
              <NumberAnimation
                :from="0"
                :to="right?.value || 0"
                :format="formatNumber"
                :duration="1"
                autoplay
              />
              <span class="text-sm font-medium">
                {{ right?.unit }}
              </span>
            </p>
          </template>
        </div>
      </template>

      <template v-if="loading">
        <USkeleton class="h-12 w-12 rounded-lg" />
      </template>
      <UAvatar
        v-else-if="!right"
        :icon="icon"
        size="md"
        :ui="{
          icon: {
            base: props.value ? `text-${color}-500 dark:text-${color}-900` : ''
          },
          rounded: 'rounded-lg',
          background: props.value
            ? `bg-${color}-100 dark:bg-gray-800`
            : 'bg-gray-50 dark:bg-gray-800'
        }"
        class="group-hover:scale-125 transition-all duration-200"
      />
    </div>
    <div
      class="text-xs text-gray-500 dark:text-gray-400 font-medium mt-2 text-center"
    >
      <template v-if="loading">
        <USkeleton class="h-3 w-24 mx-auto" />
      </template>
      <template v-else>
        {{ hint }}
      </template>
    </div>
    <!-- <div class="">
      <VisXYContainer
        :data="data"
        class="h-12"
      >
        <VisArea
          :data="data"
          :x="x"
          :y="y"
          :opacity="0.7"
          :color="colorHex"
        />
        <VisTooltip />
      </VisXYContainer>
    </div> -->
  </UDashboardCard>
</template>

<script setup lang="ts">
import { VisXYContainer, VisArea, VisTooltip } from '@unovis/vue'
import colors from 'tailwindcss/colors'
import NumberAnimation from 'vue-number-animation'

export interface Statistics {
  title: string
  value: string
  unit: string
  color: string
  description: string
  icon: string
}

const props = defineProps({
  title: String,
  value: String,
  unit: String,
  color: String,
  icon: {
    type: String,
    default: 'material-symbols:chart-line'
  },
  range: Object,
  tooltip: String,
  right: {
    type: Object,
    default: null,
    required: false
  },
  chartDataset: Array,
  hint: String,
  loading: {
    type: Boolean,
    default: false
  }
})

type DataRecord = { x: number, y: number }
const x = (d: DataRecord) => d.x
const y = (d: DataRecord) => d.y

const data = computed(() => {
  return props.chartDataset?.map((d: any, i: number) => {
    // total all values of d (object)
    if (typeof d === 'object') {
      d = Object.values(d).reduce((a: number, b: number) => a + b, 0)
    }
    return {
      x: i,
      y: d
    }
  })
})

watch(
  () => props.range,
  () => {
    // random data from range.start to range.end
    const { start, end } = props.range
    data.value = Array.from(
      { length: Math.abs(end.getDate() - start.getDate()) },
      (_, i) =>
        ({
          x: i,
          y: Math.floor(Math.random() * 100)
        } as DataRecord)
    )
  }
)

const colorHex = computed(() => {
  return colors[props.color]?.[500] || props.color
})
</script>

<template>
  <div class="flex flex-col gap-4">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2">
      <div>
        <DashboardStatisticsCard
          v-bind="statisticsData[0]"
          :range="range"
        />
      </div>
      <div>
        <DashboardStatisticsCard
          v-bind="statisticsData[1]"
          :range="range"
        />
      </div>
      <div>
        <DashboardStatisticsCard
          v-for="index in [2, 3, 4, 5, 6]"
          :key="index"
          v-bind="statisticsData[index]"
          :range="range"
        />
      </div>
      <div>
        <DashboardStatisticsChartCard
          :items="chartItems"
          :rate="
            (responseRateSummary.success / responseRateSummary.total) * 100
          "
        />
        <DashboardStatisticsChartCard
          :items="surveyReportsChartDataset"
          title="アンケートの割合"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import colors from 'tailwindcss/colors'

const reportsStore = useReportsStore()
const {
  sessionsTotalOfSelectedCategories,
  sessionsByCategoriesChartDataset,
  totalUnansweredReports,
  responseRateSummary,
  satisfiedTotal,
  unsatisfiedTotal,
  neutralTotal,
  surveyReportsChartDataset,
  reportSummary
} = storeToRefs(reportsStore)
const statisticsData = computed(() => {
  return [
    {
      title: '全質問数',
      value: reportSummary.value?.total,
      unit: '件',
      color: 'sky',
      icon: 'mdi:frequently-asked-questions',
      tooltip: 'チャットボットへの質問数'
    },
    {
      title: '未回答数',
      value: reportSummary.value?.unanswered?.total,
      unit: '件',
      color: 'red',
      icon: 'teenyicons:message-no-access-outline',
      tooltip: '回答が入力されていないモノ'
    },
    {
      title: 'ナレッジのチューニング必要',
      value:
        reportSummary.value?.unanswered?.knowledge_tuning_required
        + reportSummary.value?.unanswered?.knowledge_tuning_required_from_cache,
      unit: '件',
      color: 'teal',
      icon: 'material-symbols:quickreply-outline',
      tooltip: '未回答の中に、ナレッジのチューニングが必要なモノ'
    },
    {
      title: 'WEBのチューニング必要',
      value:
        reportSummary.value?.unanswered?.web_tuning_required
        + reportSummary.value?.unanswered?.web_tuning_required_from_cache,
      unit: '件',
      color: 'indigo',
      icon: 'mdi:web',
      tooltip: '未回答の中に、WEBのチューニングが必要なモノ'
    },
    {
      title: 'ナレッジ未登録',
      value: reportSummary.value?.unanswered?.no_knowledge_found,
      unit: '件',
      color: 'yellow',
      icon: 'iconoir:file-not-found',
      tooltip: 'ナレッジでもWEB検索でも結果なし'
    },
    {
      title: 'その他',
      value:
        (reportSummary.value?.unanswered?.from_normal || 0)
        + (reportSummary.value?.unanswered?.from_weather || 0),
      unit: '件',
      color: 'violet',
      icon: 'iconamoon:cloud-error-fill',
      tooltip: '天気、日常会話、挨拶（「こんにちは」等）',
      right: {
        title: 'エラー',
        value: reportSummary.value?.unanswered?.errors,
        unit: '件',
        color: 'red',
        icon: 'iconamoon:cloud-error-fill',
        tooltip: 'システムエラーなど'
      }
    }
  ]
})

const chartItems = computed(() => {
  return [
    {
      name: '未回答数',
      value: totalUnansweredReports.value,
      color: colors['sky'][500]
    },
    {
      name: '回答数',
      value: responseRateSummary.value?.success,
      color: colors['green'][500]
    }
  ]
})

const surveyStatisticsData = computed(() => {
  return [
    {
      title: '満足数',
      value: satisfiedTotal.value,
      unit: '件',
      color: 'green',
      icon: 'twemoji:slightly-smiling-face',
      tooltip: '満足度アンケートで「３また４」点数の選択肢を選択した数'
    },
    {
      title: '中性数',
      value: neutralTotal.value,
      unit: '件',
      color: 'pink',
      icon: 'twemoji:neutral-face',
      tooltip: '満足度アンケートで「２」点数の選択肢を選択した数'
    },
    {
      title: '未満足数',
      value: unsatisfiedTotal.value,
      unit: '件',
      color: 'amber',
      icon: 'twemoji:worried-face',
      tooltip: '満足度アンケートで「０また１」点数の選択肢を選択した数'
    }
  ]
})

const surveyChartItems = computed(() => {
  return [
    {
      name: '満足数',
      value: satisfiedTotal.value,
      color: colors['green'][500]
    },
    {
      name: '中性数',
      value: neutralTotal.value,
      color: colors['pink'][500]
    },
    {
      name: '未満足数',
      value: unsatisfiedTotal.value,
      color: colors['amber'][500]
    }
  ]
})

defineProps({
  range: Object
})
</script>

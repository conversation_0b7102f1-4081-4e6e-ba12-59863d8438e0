<template>
  <UDashboardCard
    :ui="{
      body: { padding: '!px-0 !py-2' },
      base: 'overflow-hidden group'
    }"
  >
    <div>
      <p
        class="inline-flex text-center justify-center w-full gap-1 items-center text-xs text-red-500 dark:text-red-400 font-medium mb-1"
      >
        未回答の原因
      </p>
    </div>

    <template v-if="loading">
      <div class="space-y-2 px-4">
        <div v-for="i in 5" :key="i" class="flex items-center justify-between py-2">
          <div class="flex items-center gap-3">
            <USkeleton class="h-5 w-5 rounded" />
            <USkeleton class="h-4 w-32" />
          </div>
          <USkeleton class="h-6 w-8" />
        </div>
      </div>
    </template>

    <UDashboardSidebarLinks
      v-else
      :links="links"
      :ui="{
        active: 'before:bg-transparent dark:before:bg-transparent'
      }"
    >
      <template #badge="{ link }">
        <NumberAnimation
          class="ml-auto text-xl"
          :class="{
            'font-bold': link.badge > 0
          }"
          :from="0"
          :to="link.badge || 0"
          :format="formatNumber"
          :duration="1"
          autoplay
        />
      </template>
    </UDashboardSidebarLinks>
  </UDashboardCard>
</template>

<script setup lang="ts">
import NumberAnimation from 'vue-number-animation'

const props = defineProps({
  unanswered: Object,
  loading: {
    type: Boolean,
    default: false
  }
})

const links = computed(() => {
  return [
    {
      label: 'ナレッジのチューニング必要',
      icon: 'material-symbols:quickreply-outline',
      badge:
        props.unanswered?.knowledge_tuning_required
        + props.unanswered?.knowledge_tuning_required_from_cache,
      tooltip: {
        text: '未回答の中に、ナレッジのチューニングが必要なモノ'
      },
      id: 'knowledge-tuning-required'
    },
    {
      label: 'WEBのチューニング必要',
      icon: 'mdi:web',
      badge:
        props.unanswered?.web_tuning_required
        + props.unanswered?.web_tuning_required_from_cache,
      tooltip: {
        text: '未回答の中に、WEBのチューニングが必要なモノ'
      }
    },
    {
      label: 'ナレッジ未登録',
      icon: 'iconoir:file-not-found',
      badge: props.unanswered?.no_knowledge_found,
      tooltip: {
        text: 'ナレッジでもWEB検索でも結果なし'
      }
    },
    {
      label: 'その他',
      icon: 'material-symbols-light:other-admission-outline',
      badge: props.unanswered?.from_normal + props.unanswered?.from_weather,
      tooltip: {
        text: '天気、日常会話、挨拶（「こんにちは」等）'
      }
    },
    {
      label: 'エラー',
      icon: 'iconamoon:cloud-error-fill',
      badge: props.unanswered?.errors,
      tooltip: {
        text: 'システムエラーなど'
      }
    }
  ].map((link: any) => ({
    ...link,
    active: link.badge > 0
  }))
})
</script>

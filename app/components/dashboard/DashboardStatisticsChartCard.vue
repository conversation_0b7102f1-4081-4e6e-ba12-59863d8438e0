<template>
  <UDashboardCard
    :ui="{
      body: { padding: '!py-0 !px-0 flex-col h-full' },
      base: 'group'
    }"
  >
    <div class="flex flex-1 pt-2 px-4 flex-col items-start gap-0 justify-between pb-2 h-full">
      <template v-if="loading">
        <div class="flex items-center justify-center h-full w-full">
          <USkeleton class="h-32 w-32 rounded-full" />
        </div>
        <div class="text-xs pt-4 h-full w-full mt-auto">
          <USkeleton class="h-4 w-20 mb-2" />
          <div class="flex gap-4">
            <div class="flex items-center gap-2">
              <USkeleton class="h-3 w-3 rounded-full" />
              <USkeleton class="h-3 w-16" />
            </div>
            <div class="flex items-center gap-2">
              <USkeleton class="h-3 w-3 rounded-full" />
              <USkeleton class="h-3 w-16" />
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <VisSingleContainer
          :data="data"
          class="h-full"
          height="100%"
          width="100%"
        >
          <VisDonut
            :value="value"
            :show-empty-segments="true"
            :pad-angle="0.03"
            :arc-width="15"
            :color="color"
            :central-label="rate ? `${rate.toFixed(0)}%` : null"
          />
          <VisTooltip :triggers="triggers" />
        </VisSingleContainer>
        <div class="text-xs pt-4 h-full w-full mt-auto">
          <div
            class="w-fit text-gray-500 whitespace-nowrap dark:text-gray-400 font-medium mb-1"
          >
            {{ title }}
          </div>
          <VisBulletLegend
            :items="items"
            orientation="horizontal"
          />
        </div>
      </template>
    </div>
  </UDashboardCard>
</template>

<script setup lang="ts">
import {
  VisSingleContainer,
  VisDonut,
  VisTooltip,
  VisBulletLegend,
  VisAnnotations
} from '@unovis/vue'
import { Donut } from '@unovis/ts'

interface Item {
  name: string
  value: number
  color: string
}
const props = defineProps({
  items: Array as PropType<Item[]>,
  rate: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    default: '全体の割合'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const data = computed(() => {
  return props.items.map(i => i.value)
})
const value = (d: number) => d

const color = (d: number, i: number) => props.items[i].color

const triggers = {
  [Donut.selectors.segment]: ({ data, index }) =>
    `<div class="text-xs">${props.items?.[index]?.name}: ${data}件</div>`
}
</script>

<style>
:root {
  --vis-donut-central-label-font-size: 20px !important;
  --vis-donut-central-label-text-color: rgb(20, 184, 166) !important;
  --vis-donut-central-label-font-weight: 550 !important;
}
</style>

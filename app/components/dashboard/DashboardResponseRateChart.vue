<script setup lang="ts">
import { format } from 'date-fns'
import {
  VisXYContainer,
  VisBulletLegend,
  VisAxis,
  VisGroupedBar,
  VisCrosshair,
  VisTooltip
} from '@unovis/vue'
import type { Period, Range } from '~/types'

const reportsStore = useReportsStore()
const {
  xAxis,
  responseRateByCategoriesChartDataset,
  categoriesLegends,
  responseRateSummary
} = storeToRefs(reportsStore)
const cardRef = ref<HTMLElement | null>(null)

const props = defineProps({
  period: {
    type: String as PropType<Period>,
    required: true
  },
  range: {
    type: Object as PropType<Range>,
    required: true
  }
})

type DataRecord = {
  date: Date
  count: number
  data?: {
    [id: string]: {
      total: number
      success: number
    }
  }
}

const { width } = useElementSize(cardRef)

const data = computed(() =>
  xAxis.value.map((date, index) => ({
    date: new Date(date),
    count: responseRateByCategoriesChartDataset.value[index]?.total,
    data: responseRateByCategoriesChartDataset.value[index]
  }))
)

const x = (_: DataRecord, i: number) => i
const y = computed(() => {
  return categoriesLegends.value.map(({ id }) => {
    return (d: DataRecord) =>
      d.data?.[id]?.total ? d.data?.[id]?.success / d.data?.[id]?.total : 0
  })
})

const color = (d: DataRecord, i: number) => categoriesLegends.value[i].color
const formatDate = (date: Date): string => {
  return {
    daily: format(date, 'MM月dd日'),
    weekly: format(date, 'MM月dd日の週'),
    monthly: format(date, 'yyyy年MM月')
  }[props.period]
}

const xTicks = (i: number) => {
  if (i === 0 || i === data.value.length - 1 || !data.value[i]) {
    return ''
  }

  return formatDate(data.value[i].date)
}

const template = (d: DataRecord) =>
  `<div class="text-xs">
    ${formatDate(d.date)} </br>
  ${categoriesLegends.value
    .filter(({ id }) => d.data?.[id])
    .map(({ id, name }) => {
      const summary = d.data?.[id] || ({} as any)
      const rate = summary?.total
        ? (summary?.success / summary?.total) * 100
        : 0
      return `・${name}: ${rate.toFixed(0)}% (${formatNumber(
        summary?.success
      )}/${formatNumber(summary?.total)}件)`
    })
    .join('</br>')}
  </div>`
const rateSummary = computed(() => {
  return responseRateSummary.value?.total
    ? (responseRateSummary.value?.success / responseRateSummary.value?.total)
    * 100
    : 0
})
</script>

<template>
  <UDashboardCard
    ref="cardRef"
    :ui="{ body: { padding: '!pb-3 !px-0' } as any }"
  >
    <template #header>
      <div class="flex items-center justify-between w-full">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400 font-medium mb-1">
            回答率 (回答数 / 全質問数)
          </p>
          <p class="text-3xl text-gray-900 dark:text-white font-semibold">
            {{ rateSummary.toFixed(0) }}%
            <small class="text-gray-500 dark:text-gray-400 font-light">
              ({{ responseRateSummary?.success }} /
              {{ responseRateSummary?.total }})
            </small>
          </p>
        </div>
        <VisBulletLegend :items="categoriesLegends" />
      </div>
    </template>
    <VisXYContainer
      :key="props.period"
      :data="data"
      :padding="{ top: 10 }"
      class="h-96"
      :width="width"
    >
      <VisGroupedBar
        :x="x"
        :y="y"
        :color="color"
      />

      <VisAxis
        type="x"
        :x="x"
        :tick-format="xTicks"
      />

      <VisCrosshair
        :color="color"
        :template="template"
      />

      <VisTooltip />
    </VisXYContainer>
  </UDashboardCard>
</template>

<style scoped>
.unovis-xy-container {
  --vis-crosshair-line-stroke-color: rgb(var(--color-primary-500));
  --vis-crosshair-circle-stroke-color: #fff;

  --vis-axis-grid-color: rgb(var(--color-gray-200));
  --vis-axis-tick-color: rgb(var(--color-gray-200));
  --vis-axis-tick-label-color: rgb(var(--color-gray-400));

  --vis-tooltip-background-color: #fff;
  --vis-tooltip-border-color: rgb(var(--color-gray-200));
  --vis-tooltip-text-color: rgb(var(--color-gray-900));
}

.dark {
  .unovis-xy-container {
    --vis-crosshair-line-stroke-color: rgb(var(--color-primary-400));
    --vis-crosshair-circle-stroke-color: rgb(var(--color-gray-900));

    --vis-axis-grid-color: rgb(var(--color-gray-800));
    --vis-axis-tick-color: rgb(var(--color-gray-800));
    --vis-axis-tick-label-color: rgb(var(--color-gray-500));

    --vis-tooltip-background-color: rgb(var(--color-gray-900));
    --vis-tooltip-border-color: rgb(var(--color-gray-800));
    --vis-tooltip-text-color: #fff;
  }
}
</style>

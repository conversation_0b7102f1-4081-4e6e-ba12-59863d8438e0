<script setup lang="ts">
import type { PropType } from 'vue'

const props = defineProps({
  path: {
    type: String,
    required: true
  },
  fallback: {
    type: Boolean,
    default: false
  }
})

const pagePermissionsStore = usePagePermissionsStore()
const { isPageAccessible } = usePagePermissions()

// Find the page that matches the path
const page = computed(() => {
  return pagePermissionsStore.pagePermissions.find(p => p.path === props.path)
})

// Check if the page is accessible
const canAccess = computed(() => {
  if (!page.value) {
    return true // If no matching page is found, allow access by default
  }
  
  return isPageAccessible(page.value)
})
</script>

<template>
  <slot v-if="canAccess" />
  <slot v-else-if="fallback" name="fallback" />
</template>

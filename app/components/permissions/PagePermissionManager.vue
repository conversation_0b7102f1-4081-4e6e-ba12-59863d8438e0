<template>
  <div class="page-permission-manager">
    <!-- Role summary table - visible to all users but with role-based column restrictions -->
    <div
      v-if="selectedRole === 'summary'"
      class="mb-8"
    >
      <UCard
        :ui="{
          body: {
            padding: '!p-0'
          }
        }"
      >
        <template #header>
          <div class="flex items-center gap-2">
            <UIcon
              name="i-heroicons-chart-pie"
              class="text-xl"
            />
            <div>
              <h3 class="text-base font-semibold">
                ロール別ページ権限サマリー
              </h3>
              <p class="text-xs text-gray-500 mt-1">
                <template v-if="isOperator">
                  全てのロールの権限が表示されています
                </template>
                <template v-else-if="userRole === UserType.ADMIN">
                  管理者とスタッフの権限が表示されています
                </template>
                <template v-else>
                  スタッフの権限のみ表示されています
                </template>
              </p>
            </div>
          </div>
        </template>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
            <thead class="bg-gray-100 dark:bg-gray-900">
              <tr>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider"
                >
                  カテゴリー / ページ
                </th>
                <!-- Operator column - only visible to operators -->
                <th
                  v-if="isOperator"
                  scope="col"
                  class="px-6 py-3 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider"
                >
                  PNLオペレーター
                </th>
                <!-- Admin column - visible to operators and admins -->
                <th
                  v-if="isOperator || userRole === UserType.ADMIN"
                  scope="col"
                  class="px-6 py-3 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider"
                >
                  管理者
                </th>
                <!-- Staff column - visible to all roles -->
                <th
                  scope="col"
                  class="px-6 py-3 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider"
                >
                  スタッフ
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-900">
              <!-- Loop through categories -->
              <template
                v-for="category in pagePermissionsStore.categories"
                :key="category.id"
              >
                <!-- Category row -->
                <tr class="bg-gray-50 dark:bg-gray-900 dark:divide-gray-900">
                  <td class="px-6 py-3 whitespace-nowrap">
                    <div class="flex items-center gap-2">
                      <UIcon
                        :name="category.icon || 'i-heroicons-folder'"
                        class="text-md"
                      />
                      <span class="font-medium">{{ category.name }}</span>
                    </div>
                  </td>
                  <!-- Operator column - only visible to operators -->
                  <td
                    v-if="isOperator"
                    class="px-6 py-3 whitespace-nowrap text-center"
                  >
                    <div
                      v-if="hasAccess(category.id, 'operator')"
                      class="text-green-500 font-bold"
                    >
                      <UIcon name="mingcute:check-fill" />
                    </div>
                  </td>
                  <!-- Admin column - visible to operators and admins -->
                  <td
                    v-if="isOperator || userRole === UserType.ADMIN"
                    class="px-6 py-3 whitespace-nowrap text-center"
                  >
                    <div
                      v-if="hasAccess(category.id, 'admin')"
                      class="text-green-500 font-bold"
                    >
                      <UIcon name="mingcute:check-fill" />
                    </div>
                  </td>
                  <!-- Staff column - visible to all roles -->
                  <td class="px-6 py-3 whitespace-nowrap text-center">
                    <div
                      v-if="hasAccess(category.id, 'staff')"
                      class="text-green-500 font-bold"
                    >
                      <UIcon name="mingcute:check-fill" />
                    </div>
                  </td>
                </tr>

                <!-- Page rows for this category -->
                <tr
                  v-for="page in getPagesByCategory(category.id)"
                  :key="page.id"
                  class="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td class="px-6 py-2 whitespace-nowrap pl-10">
                    <div class="flex items-center gap-2">
                      <UIcon
                        :name="page.icon || 'i-heroicons-document'"
                        class="text-sm text-gray-500"
                      />
                      <span class="text-sm">{{ page.name }}</span>
                    </div>
                  </td>
                  <!-- Operator column - only visible to operators -->
                  <td
                    v-if="isOperator"
                    class="px-6 py-2 whitespace-nowrap text-center"
                  >
                    <div
                      v-if="hasPageAccess(page, 'operator')"
                      class="text-green-500 text-sm"
                    >
                      <UIcon name="mingcute:check-fill" />
                    </div>
                  </td>
                  <!-- Admin column - visible to operators and admins -->
                  <td
                    v-if="isOperator || userRole === UserType.ADMIN"
                    class="px-6 py-2 whitespace-nowrap text-center"
                  >
                    <div
                      v-if="hasPageAccess(page, 'admin')"
                      class="text-green-500 text-sm"
                    >
                      <UIcon name="mingcute:check-fill" />
                    </div>
                  </td>
                  <!-- Staff column - visible to all roles -->
                  <td class="px-6 py-2 whitespace-nowrap text-center">
                    <div
                      v-if="hasPageAccess(page, 'staff')"
                      class="text-green-500 text-sm"
                    >
                      <UIcon name="mingcute:check-fill" />
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </UCard>
    </div>

    <!-- Categories - shown when not in summary view -->
    <div
      v-if="selectedRole !== 'summary'"
      class="space-y-8"
    >
      <div
        v-for="category in pagePermissionsStore.categories"
        :key="category.id"
        class="category-section"
      >
        <UCard>
          <template #header>
            <div class="flex justify-between items-center">
              <div class="flex items-center gap-2">
                <UIcon
                  :name="category.icon || 'i-heroicons-folder'"
                  class="text-xl"
                />
                <h3 class="text-lg font-semibold">
                  {{ category.name }}
                </h3>
              </div>
            </div>

            <p
              v-if="category.description"
              class="text-sm text-gray-500"
            >
              {{ category.description }}
            </p>
          </template>

          <div class="space-y-4">
            <div
              v-for="page in getFilteredPagesByCategory(category.id)"
              :key="page.id"
              class="page-item"
            >
              <div
                class="flex justify-between items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg"
              >
                <div class="flex items-center gap-3">
                  <div>
                    <div class="flex items-center gap-2">
                      <UIcon
                        :name="page.icon || 'i-heroicons-document'"
                        class="text-lg"
                      />
                      <span class="font-medium">{{ page.name }}</span>
                    </div>

                    <p
                      v-if="page.description"
                      class="text-xs text-gray-500"
                    >
                      {{ page.description }}
                    </p>

                    <div class="flex flex-wrap gap-1 mt-1">
                      <UBadge
                        v-for="permission in page.requiredPermissions"
                        :key="permission"
                        color="gray"
                        variant="subtle"
                        size="xs"
                      >
                        {{ formatPermission(permission) }}
                      </UBadge>
                    </div>
                  </div>
                </div>

                <UToggle
                  v-model="page.enabled"
                  @change="togglePagePermission(page.id)"
                />
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// These imports are automatically available in Nuxt 3
import { computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { UserType } from '~/types/index.d'
import type {
  PagePermission,
  PagePermissionCategory
} from '~/types/page-permission'

const pagePermissionsStore = usePagePermissionsStore()
const toast = useToast()
const authStore = useAuthStore()
const { userRole } = storeToRefs(authStore)
const { selectedRole } = storeToRefs(pagePermissionsStore)

// Check if the current user is an operator
const isOperator = computed(() => userRole.value === UserType.PNL_ADMIN)

// Initialize page permissions if not already initialized
onMounted(() => {
  if (pagePermissionsStore.pagePermissions.length === 0) {
    pagePermissionsStore.initializePagePermissions()
  }
})

// Get pages by category
const getPagesByCategory = (categoryId: string): PagePermission[] => {
  return pagePermissionsStore.pagePermissions.filter(
    (page: PagePermission) => page.category === categoryId
  )
}

// Get filtered pages by category based on user role
const getFilteredPagesByCategory = (categoryId: string): PagePermission[] => {
  // If the user is an operator and has selected a specific role
  if (isOperator.value && selectedRole.value !== 'summary') {
    // Filter pages based on the selected role
    return getPagesByCategory(categoryId).filter((page: PagePermission) => {
      // For 'operator', show pages with operator permissions
      if (selectedRole.value === 'operator') {
        return page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.operatorPermissions.includes(permission)
        )
      }

      // For 'admin', show pages with admin permissions
      if (selectedRole.value === 'admin') {
        return page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.adminPermissions.includes(permission)
        )
      }

      // For 'staff', show pages with staff permissions
      if (selectedRole.value === 'staff') {
        return page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.staffPermissions.includes(permission)
        )
      }

      // For 'default', show all pages
      return true
    })
  }

  // If the user is not an operator, filter pages based on user role
  if (!isOperator.value) {
    return getPagesByCategory(categoryId).filter((page: PagePermission) => {
      // Admin users can see admin and staff pages
      if (userRole.value === UserType.ADMIN) {
        return (
          page.requiredPermissions.some(
            (permission: string) =>
              pagePermissionsStore.adminPermissions.includes(permission)
              || pagePermissionsStore.staffPermissions.includes(permission)
          ) || page.requiredPermissions.length === 0
        )
      }

      // Staff users can only see staff pages
      if (userRole.value === UserType.STAFF) {
        return (
          page.requiredPermissions.some(
            (permission: string) =>
              pagePermissionsStore.staffPermissions.includes(permission)
          ) || page.requiredPermissions.length === 0
        )
      }

      // Default: show no pages for unknown roles
      return false
    })
  }

  // Default: return all pages
  return getPagesByCategory(categoryId)
}

// Check if a specific page is accessible for a role
const hasPageAccess = (page: PagePermission, role: string): boolean => {
  if (role === 'operator') {
    // Operators have access to all permissions
    return (
      page.requiredPermissions.some((permission: string) =>
        pagePermissionsStore.operatorPermissions.includes(permission)
      ) || page.requiredPermissions.length === 0
    )
  } else if (role === 'admin') {
    return (
      page.requiredPermissions.some((permission: string) =>
        pagePermissionsStore.adminPermissions.includes(permission)
      ) || page.requiredPermissions.length === 0
    )
  } else if (role === 'staff') {
    return (
      page.requiredPermissions.some((permission: string) =>
        pagePermissionsStore.staffPermissions.includes(permission)
      ) || page.requiredPermissions.length === 0
    )
  }

  return false
}

// Check if a category has access for a specific role
const hasAccess = (categoryId: string, role: string): boolean => {
  const pages = getPagesByCategory(categoryId)

  // If there are no pages in this category, return false
  if (pages.length === 0) return false

  // Check if any page in the category is accessible for the role
  for (const page of pages) {
    if (hasPageAccess(page, role)) {
      return true
    }
  }

  return false
}

// Format permission string for display
const formatPermission = (permission: string): string => {
  return permission
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

// Toggle page permission
const togglePagePermission = (pageId: string) => {
  pagePermissionsStore.togglePagePermission(pageId)
}

// Enable all permissions
const _enableAllPermissions = () => {
  // If operator has selected a specific role, only enable pages for that role
  if (
    isOperator.value
    && selectedRole.value !== 'summary'
    && selectedRole.value !== 'guest'
  ) {
    pagePermissionsStore.pagePermissions.forEach((page: PagePermission) => {
      if (
        selectedRole.value === 'operator'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.operatorPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: true })
      } else if (
        selectedRole.value === 'admin'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.adminPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: true })
      } else if (
        selectedRole.value === 'staff'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.staffPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: true })
      }
    })
  } else {
    pagePermissionsStore.enableAllPagePermissions()
  }

  toast.add({
    title: 'すべてのページ権限を有効にしました',
    color: 'green'
  })
}

// Disable all permissions
const _disableAllPermissions = () => {
  // If operator has selected a specific role, only disable pages for that role
  if (
    isOperator.value
    && selectedRole.value !== 'summary'
    && selectedRole.value !== 'guest'
  ) {
    pagePermissionsStore.pagePermissions.forEach((page: PagePermission) => {
      if (
        selectedRole.value === 'operator'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.operatorPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: false })
      } else if (
        selectedRole.value === 'admin'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.adminPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: false })
      } else if (
        selectedRole.value === 'staff'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.staffPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: false })
      }
    })
  } else {
    pagePermissionsStore.disableAllPagePermissions()
  }

  toast.add({
    title: 'すべてのページ権限を無効にしました',
    color: 'red'
  })
}

// Enable category permissions
const _enableCategoryPermissions = (categoryId: string) => {
  // If operator has selected a specific role, only enable pages for that role in the category
  if (
    isOperator.value
    && selectedRole.value !== 'summary'
    && selectedRole.value !== 'guest'
  ) {
    getPagesByCategory(categoryId).forEach((page: PagePermission) => {
      if (
        selectedRole.value === 'operator'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.operatorPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: true })
      } else if (
        selectedRole.value === 'admin'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.adminPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: true })
      } else if (
        selectedRole.value === 'staff'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.staffPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: true })
      }
    })
  } else {
    pagePermissionsStore.enablePagePermissionsByCategory(categoryId)
  }

  toast.add({
    title: `${
      pagePermissionsStore.categories.find(
        (c: PagePermissionCategory) => c.id === categoryId
      )?.name
    }のページ権限を有効にしました`,
    color: 'green'
  })
}

// Disable category permissions
const _disableCategoryPermissions = (categoryId: string) => {
  // If operator has selected a specific role, only disable pages for that role in the category
  if (
    isOperator.value
    && selectedRole.value !== 'summary'
    && selectedRole.value !== 'guest'
  ) {
    getPagesByCategory(categoryId).forEach((page: PagePermission) => {
      if (
        selectedRole.value === 'operator'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.operatorPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: false })
      } else if (
        selectedRole.value === 'admin'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.adminPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: false })
      } else if (
        selectedRole.value === 'staff'
        && page.requiredPermissions.some((permission: string) =>
          pagePermissionsStore.staffPermissions.includes(permission)
        )
      ) {
        pagePermissionsStore.updatePagePermission(page.id, { enabled: false })
      }
    })
  } else {
    pagePermissionsStore.disablePagePermissionsByCategory(categoryId)
  }

  toast.add({
    title: `${
      pagePermissionsStore.categories.find(
        (c: PagePermissionCategory) => c.id === categoryId
      )?.name
    }のページ権限を無効にしました`,
    color: 'red'
  })
}

// Reset permissions
const _resetPermissions = () => {
  pagePermissionsStore.resetPagePermissions()
  toast.add({
    title: 'ページ権限をリセットしました',
    color: 'blue'
  })
}
</script>

<style scoped>
.category-section {
  transition: all 0.3s ease;
}

.page-item {
  transition: all 0.2s ease;
}

.role-summary {
  padding: 0.5rem 0;
}

.category-summary {
  padding: 0.5rem;
  border-radius: 0.375rem;
  background-color: #f9fafb;
}
</style>

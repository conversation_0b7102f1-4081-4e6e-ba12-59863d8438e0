<script setup lang="ts">
const props = defineProps({
  permission: {
    type: String,
    required: false
  },
  permissions: {
    type: Array as PropType<string[]>,
    required: false
  },
  requireAll: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: 'primary'
  },
  size: {
    type: String,
    default: 'md'
  },
  variant: {
    type: String,
    default: 'solid'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const { hasPermission, hasAnyPermission, hasAllPermissions } = useAppPermissions()

const canAccess = computed(() => {
  if (props.permission) {
    return hasPermission(props.permission)
  }

  if (props.permissions) {
    return props.requireAll
      ? hasAllPermissions(props.permissions)
      : hasAnyPermission(props.permissions)
  }

  return true
})

const onClick = (event: Event) => {
  if (canAccess.value && !props.disabled) {
    emit('click', event)
  }
}
</script>

<template>
  <UButton
    v-if="canAccess"
    :label="label"
    :icon="icon"
    :color="color"
    :size="size"
    :variant="variant"
    :disabled="disabled"
    @click="onClick"
  >
    <slot />
  </UButton>
</template>

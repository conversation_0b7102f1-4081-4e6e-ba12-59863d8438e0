<script setup lang="ts">
import { PERMISSIONS } from '~/stores/permissions'

const props = defineProps({
  items: {
    type: Array as PropType<any[]>,
    required: true
  }
})

const { hasPermission, hasAnyPermission } = useAppPermissions()

const filteredItems = computed(() => {
  return props.items.filter(item => {
    // If no permission requirements, show the item
    if (!item.requiredPermission && !item.requiredPermissions) {
      return true
    }

    // Check for single permission requirement
    if (item.requiredPermission) {
      return hasPermission(item.requiredPermission)
    }

    // Check for multiple permission requirements
    if (item.requiredPermissions) {
      return hasAnyPermission(item.requiredPermissions)
    }

    return true
  })
})
</script>

<template>
  <slot :filtered-items="filteredItems" />
</template>

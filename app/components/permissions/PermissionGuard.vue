<script setup lang="ts">
import type { PropType } from 'vue'

const props = defineProps({
  permission: {
    type: String,
    required: false
  },
  permissions: {
    type: Array as PropType<string[]>,
    required: false
  },
  requireAll: {
    type: Boolean,
    default: false
  },
  fallback: {
    type: Boolean,
    default: false
  }
})

const { hasPermission, hasAnyPermission, hasAllPermissions } = useAppPermissions()

const canAccess = computed(() => {
  if (props.permission) {
    return hasPermission(props.permission)
  }

  if (props.permissions) {
    return props.requireAll
      ? hasAllPermissions(props.permissions)
      : hasAnyPermission(props.permissions)
  }

  return true
})
</script>

<template>
  <slot v-if="canAccess" />
  <slot v-else-if="fallback" name="fallback" />
</template>

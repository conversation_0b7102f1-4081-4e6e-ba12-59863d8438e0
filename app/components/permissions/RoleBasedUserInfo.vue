<script setup lang="ts">
import { UserType } from '~/types/index.d'
import type { RoleBasedUser } from '~/types/role-based-user'

const {
  getCurrentUser,
  getUserRole,
  getUserPermissions,
  isAdmin,
  isStaff,
  isGuest,
  isOperator
} = useRoleBasedUser()

const user = computed<RoleBasedUser | null>(() => getCurrentUser())
const role = computed<UserType | null>(() => getUserRole())
const permissions = computed<string[]>(() => getUserPermissions())

const userRoleInfo = computed(() => {
  if (isAdmin()) return { label: '管理者', color: 'primary' }
  if (isOperator()) return { label: 'オペレーター', color: 'yellow' }
  if (isStaff()) return { label: 'スタッフ', color: 'blue' }
  if (isGuest()) return { label: 'ゲスト', color: 'gray' }
  return { label: '不明', color: 'gray' }
})
</script>

<template>
  <div v-if="user" class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
    <div class="flex items-center gap-4 mb-4">
      <UAvatar
        v-if="user.avatar"
        v-bind="user.avatar"
        :alt="user.display_name || user.username"
        size="lg"
      />
      <UAvatar
        v-else
        :alt="user.display_name || user.username"
        size="lg"
      />
      <div>
        <h2 class="text-lg font-semibold">
          {{ user.display_name || user.username }}
        </h2>
        <p class="text-sm text-gray-500">{{ user.email }}</p>
        <UBadge
          :color="userRoleInfo.color"
          :label="userRoleInfo.label"
          class="mt-1"
        />
      </div>
    </div>

    <div class="mt-4">
      <h3 class="text-md font-medium mb-2">権限</h3>
      <div class="flex flex-wrap gap-2">
        <UBadge
          v-for="permission in permissions"
          :key="permission"
          color="gray"
          variant="subtle"
          :label="permission"
        />
      </div>
    </div>

    <div class="mt-4" v-if="user.groups && user.groups.length">
      <h3 class="text-md font-medium mb-2">グループ</h3>
      <div class="flex flex-wrap gap-2">
        <UBadge
          v-for="group in user.groups"
          :key="group"
          color="blue"
          variant="subtle"
          :label="group"
        />
      </div>
    </div>
  </div>
  <div v-else class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
    <p class="text-center text-gray-500">ユーザ情報がありません</p>
  </div>
</template>

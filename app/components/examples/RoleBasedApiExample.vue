<template>
  <div>
    <h2 class="text-xl font-bold mb-4">Role-Based API Example</h2>
    
    <!-- Tenants Section -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-2">Tenants</h3>
      
      <div class="flex gap-2 mb-4">
        <UButton
          label="Fetch Tenants"
          color="primary"
          :loading="loadings.fetchTenants"
          @click="fetchTenants"
        />
        
        <UButton
          label="Create Tenant"
          color="green"
          :loading="loadings.createTenant"
          @click="showCreateTenantModal = true"
        />
      </div>
      
      <!-- Tenants List -->
      <div v-if="tenants.length" class="border rounded-lg p-4 mb-4">
        <div v-for="tenant in tenants" :key="tenant.id" class="flex justify-between items-center p-2 border-b last:border-b-0">
          <div>
            <div class="font-medium">{{ tenant.description || tenant.id }}</div>
            <div class="text-sm text-gray-500">ID: {{ tenant.id }}</div>
          </div>
          
          <div class="flex gap-2">
            <UButton
              icon="i-heroicons-pencil"
              color="blue"
              variant="ghost"
              :loading="loadings.updateTenant === tenant.id"
              @click="() => editTenant(tenant)"
            />
            
            <UButton
              icon="i-heroicons-trash"
              color="red"
              variant="ghost"
              :loading="loadings.deleteTenant === tenant.id"
              @click="() => confirmDeleteTenant(tenant)"
            />
          </div>
        </div>
      </div>
      
      <div v-else-if="!loadings.fetchTenants" class="text-gray-500 italic">
        No tenants found
      </div>
    </div>
    
    <!-- Users Section -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-2">Users</h3>
      
      <div class="flex gap-2 mb-4">
        <UButton
          label="Fetch Users"
          color="primary"
          :loading="loadings.fetchUsers"
          @click="fetchUsers"
        />
      </div>
      
      <!-- Users List -->
      <div v-if="users.length" class="border rounded-lg p-4">
        <div v-for="user in users" :key="user.id" class="p-2 border-b last:border-b-0">
          <div class="font-medium">{{ user.username }}</div>
          <div class="text-sm text-gray-500">Role: {{ user.role }}</div>
        </div>
      </div>
      
      <div v-else-if="!loadings.fetchUsers" class="text-gray-500 italic">
        No users found
      </div>
    </div>
    
    <!-- Create Tenant Modal -->
    <UModal v-model="showCreateTenantModal">
      <UCard>
        <template #header>
          <div class="text-xl font-bold">Create Tenant</div>
        </template>
        
        <div class="space-y-4">
          <UFormGroup label="Tenant Name" name="description">
            <UInput v-model="newTenant.description" placeholder="Enter tenant name" />
          </UFormGroup>
          
          <UFormGroup label="Tenant ID" name="id">
            <UInput v-model="newTenant.id" placeholder="Enter tenant ID (optional)" />
          </UFormGroup>
          
          <UFormGroup label="Enabled" name="enabled">
            <UToggle v-model="newTenant.enabled" />
          </UFormGroup>
        </div>
        
        <template #footer>
          <div class="flex justify-end gap-2">
            <UButton
              label="Cancel"
              color="gray"
              variant="outline"
              @click="showCreateTenantModal = false"
            />
            
            <UButton
              label="Create"
              color="green"
              :loading="loadings.createTenant"
              @click="createTenant"
            />
          </div>
        </template>
      </UCard>
    </UModal>
    
    <!-- Edit Tenant Modal -->
    <UModal v-model="showEditTenantModal">
      <UCard v-if="selectedTenant">
        <template #header>
          <div class="text-xl font-bold">Edit Tenant</div>
        </template>
        
        <div class="space-y-4">
          <UFormGroup label="Tenant Name" name="description">
            <UInput v-model="selectedTenant.description" placeholder="Enter tenant name" />
          </UFormGroup>
          
          <UFormGroup label="Enabled" name="enabled">
            <UToggle v-model="selectedTenant.enabled" />
          </UFormGroup>
        </div>
        
        <template #footer>
          <div class="flex justify-end gap-2">
            <UButton
              label="Cancel"
              color="gray"
              variant="outline"
              @click="showEditTenantModal = false"
            />
            
            <UButton
              label="Update"
              color="blue"
              :loading="loadings.updateTenant === selectedTenant.id"
              @click="updateTenant"
            />
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { Tenant } from '~/types/tenant'

// Use our role-based API calls composable
const roleBasedApiCalls = useRoleBasedApiCalls()
const toast = useToast()
const confirm = useConfirm()

// State
const tenants = ref<Tenant[]>([])
const users = ref<any[]>([])
const loadings = reactive({
  fetchTenants: false,
  createTenant: false,
  updateTenant: null as string | null,
  deleteTenant: null as string | null,
  fetchUsers: false
})
const errors = reactive({
  fetchTenants: null,
  createTenant: null,
  updateTenant: null,
  deleteTenant: null,
  fetchUsers: null
})

// Modal state
const showCreateTenantModal = ref(false)
const showEditTenantModal = ref(false)
const newTenant = reactive({
  description: '',
  id: '',
  enabled: true
})
const selectedTenant = ref<Tenant | null>(null)

// Fetch tenants using role-based API
async function fetchTenants() {
  try {
    loadings.fetchTenants = true
    errors.fetchTenants = null
    
    const response = await roleBasedApiCalls.tenants.fetch<{ tenants: Tenant[] }>()
    tenants.value = response?.tenants || []
    
    toast.add({
      title: 'Tenants fetched successfully',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Error fetching tenants:', error)
    errors.fetchTenants = error?.response?.data || error
    
    toast.add({
      title: 'Failed to fetch tenants',
      description: error?.message || 'Unknown error',
      color: 'red'
    })
  } finally {
    loadings.fetchTenants = false
  }
}

// Create tenant using role-based API
async function createTenant() {
  try {
    loadings.createTenant = true
    errors.createTenant = null
    
    const payload = {
      description: newTenant.description,
      enabled: newTenant.enabled
    }
    
    if (newTenant.id) {
      Object.assign(payload, { id: newTenant.id })
    }
    
    const response = await roleBasedApiCalls.tenants.create(payload)
    tenants.value.push(response)
    
    // Reset form and close modal
    newTenant.description = ''
    newTenant.id = ''
    newTenant.enabled = true
    showCreateTenantModal.value = false
    
    toast.add({
      title: 'Tenant created successfully',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Error creating tenant:', error)
    errors.createTenant = error?.response?.data || error
    
    toast.add({
      title: 'Failed to create tenant',
      description: error?.message || 'Unknown error',
      color: 'red'
    })
  } finally {
    loadings.createTenant = false
  }
}

// Edit tenant
function editTenant(tenant: Tenant) {
  selectedTenant.value = { ...tenant }
  showEditTenantModal.value = true
}

// Update tenant using role-based API
async function updateTenant() {
  if (!selectedTenant.value) return
  
  try {
    const tenantId = selectedTenant.value.id
    loadings.updateTenant = tenantId
    errors.updateTenant = null
    
    const payload = {
      description: selectedTenant.value.description,
      enabled: selectedTenant.value.enabled
    }
    
    const response = await roleBasedApiCalls.tenants.update(tenantId, payload)
    
    // Update tenant in list
    tenants.value = tenants.value.map(tenant => {
      if (tenant.id === tenantId) {
        return response
      }
      return tenant
    })
    
    // Close modal
    showEditTenantModal.value = false
    selectedTenant.value = null
    
    toast.add({
      title: 'Tenant updated successfully',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Error updating tenant:', error)
    errors.updateTenant = error?.response?.data || error
    
    toast.add({
      title: 'Failed to update tenant',
      description: error?.message || 'Unknown error',
      color: 'red'
    })
  } finally {
    loadings.updateTenant = null
  }
}

// Confirm delete tenant
function confirmDeleteTenant(tenant: Tenant) {
  confirm.require({
    message: `Are you sure you want to delete tenant "${tenant.description || tenant.id}"?`,
    onConfirm: () => deleteTenant(tenant.id)
  })
}

// Delete tenant using role-based API
async function deleteTenant(id: string) {
  try {
    loadings.deleteTenant = id
    errors.deleteTenant = null
    
    await roleBasedApiCalls.tenants.delete(id)
    
    // Remove tenant from list
    tenants.value = tenants.value.filter(tenant => tenant.id !== id)
    
    toast.add({
      title: 'Tenant deleted successfully',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Error deleting tenant:', error)
    errors.deleteTenant = error?.response?.data || error
    
    toast.add({
      title: 'Failed to delete tenant',
      description: error?.message || 'Unknown error',
      color: 'red'
    })
  } finally {
    loadings.deleteTenant = null
  }
}

// Fetch users using role-based API
async function fetchUsers() {
  try {
    loadings.fetchUsers = true
    errors.fetchUsers = null
    
    const response = await roleBasedApiCalls.users.fetch<{ users: any[] }>()
    users.value = response?.users || []
    
    toast.add({
      title: 'Users fetched successfully',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Error fetching users:', error)
    errors.fetchUsers = error?.response?.data || error
    
    toast.add({
      title: 'Failed to fetch users',
      description: error?.message || 'Unknown error',
      color: 'red'
    })
  } finally {
    loadings.fetchUsers = false
  }
}

// Fetch tenants on component mount
onMounted(() => {
  fetchTenants()
})
</script>

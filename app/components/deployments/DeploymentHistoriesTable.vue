<script setup lang="ts">
const props = defineProps({
  rows: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    required: true
  },
  pageTotal: {
    type: Number,
    default: false
  },
  totalRow: {
    type: Number,
    default: 0
  },
  range: {
    type: Object as PropType<Range>,
    default: () => ({
      start: new Date(),
      end: new Date()
    })
  }
})
const emit = defineEmits(['refresh', 'update:range', 'view-logs'])
function getItems(label: any) {
  return [
    [
      {
        label: '同期をキャンセル',
        labelClass: 'text-red-500 dark:text-red-400'
      }
    ]
  ]
}

const defaultColumns = [
  {
    key: 'type',
    label: '種別',
    sortable: true
  },
  {
    key: 'created_at',
    label: '作成日時',
    sortable: true
  },
  {
    key: 'source_env_id',
    label: '内容',
    sortable: true
  },
  {
    key: 'started_at',
    label: '開始・終了日時',
    sortable: true
  },
  {
    key: 'status',
    label: 'ステータス',
    sortable: true
  },
  {
    key: 'result',
    label: 'デプロイ結果',
    sortable: true
  },
  {
    key: 'logs',
    label: 'ログ',
    sortable: false,
    class: 'w-fit'
  }
  // {
  //   key: 'actions',
  //   label: '操作',
  //   sortable: false,
  //   class: 'w-fit text-right'
  // }
]

const expand = ref({
  openedRows: [],
  row: {}
})
</script>

<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-3 !py-3' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700'
      },
      footer: { padding: 'p-4' }
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
        >
          デプロイ履歴
          <UBadge
            v-if="totalRow > 0"
            :label="totalRow"
          />
        </h2>
      </div>
    </template>

    <!-- Filters -->
    <div class="flex items-center justify-between gap-3 px-4 py-3">
      <BaseDateRangePicker
        :model-value="range"
        @update:model-value="(newValue) => emit('update:range', newValue)"
      />
      <UButton
        icon="prime:sync"
        color="gray"
        size="sm"
        @click="emit('refresh')"
      />
    </div>
    <UTable
      v-if="rows"
      v-model:expand="expand"
      :rows="rows"
      :columns="defaultColumns"
      class="w-full"
      :ui="{
        divide: 'divide-gray-200 dark:divide-gray-800',
        tr: { base: 'group' }
      }"
      :loading="loading"
    >
      <template #expand="{ row }">
        <div
          class="p-2 text-xs "
          :class="{
            'text-red-500 dark:text-red-500': row.error_details,
            'text-gray-500 dark:text-gray-500': !row.error_details
          }"
        >
          <pre>{{ row?.updated_details || row?.error_details || "なし" }}</pre>
        </div>
      </template>
      <template #type-data="{ row }">
        <div class="">
          同期
        </div>
      </template>
      <template #source_env_id-data="{ row }">
        <div class="text-xs flex flex-col justify-center items-center gap-0.5">
          <div>
            {{ row.source_env_id }}
          </div>
          <div class="flex items-center justify-center text-base">
            <UIcon name="prime:arrow-down" />
          </div>
          <div>
            {{ row.target_env_id }}
          </div>
        </div>
      </template>
      <template #created_at-data="{ row }">
        <div>
          <div>
            {{ formatDateTime(row.created_at) }}
          </div>
          <div class="text-gray-500 dark:text-gray-500 text-xs">
            作成者: {{ row.created_username }} ({{
              formatDistanceStrictDateTime(row.created_at)
            }})
          </div>
        </div>
      </template>
      <template #started_at-data="{ row }">
        <div class="text-xs flex flex-col justify-center items-center gap-0.5">
          <div v-if="row.started_at">
            開始：{{ formatDateTime(row.started_at) }}
          </div>
          <div v-if="row.finished_at">
            終了：{{ formatDateTime(row.finished_at) }}
          </div>
        </div>
      </template>
      <template #updated_at-data="{ row }">
        <div
          v-if="row.updated_at === row.created_at"
          class="text-gray-500 dark:text-gray-500"
        >
          --
        </div>
        <div v-else>
          <div>
            {{ formatDateTime(row.updated_at) }}
          </div>
          <div class="text-gray-500 dark:text-gray-500 text-xs">
            更新者: {{ row. updated_username }} ({{
              formatDistanceStrictDateTime(row.updated_at)
            }})
          </div>
        </div>
      </template>
      <template #status-data="{ row }">
        <UBadge
          :label="deployStatusObject(row.status).label"
          :color="deployStatusObject(row.status).color"
          :icon="deployStatusObject(row.status).icon"
          :variant="'subtle'"
          :trailing="false"
          size="sm"
          :ui="{
            rounded: 'rounded-full'
          }"
        />
      </template>
      <template #result-data="{ row }">
        <UBadge
          :label="deployResultObject(row.result).label"
          :color="deployResultObject(row.result).color"
          :icon="deployResultObject(row.result).icon"
          :variant="'subtle'"
          :trailing="false"
          size="sm"
          :ui="{
            rounded: 'rounded-full'
          }"
        />
      </template>
      <template #logs-data="{ row }">
        <div class="flex flex-row items-center justify-center">
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-archive-box"
            size="xs"
            @click="emit('view-logs', row)"
          />
        </div>
      </template>
      <!-- <template #actions-data="{ row }">
        <div class="flex flex-row items-end justify-end">
          <UDropdown
            v-if="row.status === 0"
            :items="getItems(row)"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              class="row-menu"
              color="white"
              icon="charm:menu-meatball"
              size="xs"
              square
            />
          </UDropdown>
        </div>
      </template> -->
    </UTable>

    <!-- Number of rows & Pagination -->
    <template #footer>
      <div class="flex flex-wrap justify-between items-center">
        <div class="flex items-center gap-1.5">
          <span class="text-sm leading-5">表示件数:</span>

          <USelect
            v-model="pagination.pageCount"
            :options="[3, 5, 10, 20, 30, 40]"
            class="w-20"
          />
        </div>

        <UPagination
          v-if="pagination.pageCount < pageTotal"
          v-model="pagination.page"
          :page-count="pagination.pageCount"
          :total="pageTotal"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline'
              }
            }
          }"
        />
      </div>
    </template>
  </UCard>
</template>

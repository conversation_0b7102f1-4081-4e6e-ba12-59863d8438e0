<script setup lang="ts">
const props = defineProps({
  rows: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['view-logs'])

// Table columns
const columns = [
  {
    key: 'source_version',
    label: '処理',
    sortable: false
  },
  {
    key: 'created_at',
    label: '作成日時',
    sortable: false
  },
  {
    key: 'source_env_id',
    label: '内容',
    sortable: false,
    class: 'text-center'
  },
  {
    key: 'started_at',
    label: '開始・終了日時',
    sortable: false,
    class: 'text-center'
  },
  {
    key: 'result',
    label: '処理結果',
    sortable: false
  },
  {
    key: 'logs',
    label: 'ログ',
    sortable: false,
    class: 'w-fit'
  }
]

// Table expand state
const expand = ref({
  openedRows: [],
  row: {}
})

// Helper functions
function getItems(_label: any) {
  return [
    [
      {
        label: '同期をキャンセル',
        labelClass: 'text-red-500 dark:text-red-400'
      }
    ]
  ]
}
</script>

<template>
  <div>
    <BaseLoading
      v-if="loading"
      class="py-6"
    />
    <div v-else>
      <UTable
        v-model:expand="expand"
        :rows="rows"
        :columns="columns"
        class="w-full"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
        :loading="loading"
      >
        <template #source_version-data="{ row }">
          <div class="text-sm">
            {{
              row.source_version < row.target_version ? "ロールバック" : "同期"
            }}
          </div>
        </template>
        <template #expand="{ row }">
          <div
            class="p-2 text-xs"
            :class="{
              'text-red-500 dark:text-red-500': row.error_details,
              'text-gray-500 dark:text-gray-500': !row.error_details
            }"
          >
            <pre>{{
              row?.updated_details || row?.error_details || "なし"
            }}</pre>
          </div>
        </template>
        <template #type-data="{ row }">
          <div class="">
            同期
          </div>
        </template>
        <template #source_env_id-data="{ row }">
          <div
            class="text-xs flex flex-col justify-center items-center gap-0.5"
          >
            <div>{{ row.source_env_id }} (v.{{ row.source_version }})</div>
            <div class="flex items-center justify-center text-base">
              <UIcon name="prime:arrow-down" />
            </div>

            <div>{{ row.target_env_id }} (v.{{ row.target_version }})</div>
          </div>
        </template>
        <template #created_at-data="{ row }">
          <div>
            <div>
              {{ formatDateTime(row.created_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              作成者: {{ row.created_username }} ({{
                formatDistanceStrictDateTime(row.created_at)
              }})
            </div>
          </div>
        </template>
        <template #started_at-data="{ row }">
          <div
            class="text-xs flex flex-col justify-center items-center gap-0.5"
          >
            <div v-if="row.started_at">
              開始：{{ formatDateTime(row.started_at) }}
            </div>
            <div v-if="row.finished_at">
              終了：{{ formatDateTime(row.finished_at) }}
            </div>
          </div>
        </template>
        <template #updated_at-data="{ row }">
          <div
            v-if="row.updated_at === row.created_at"
            class="text-gray-500 dark:text-gray-500"
          >
            --
          </div>
          <div v-else>
            <div>
              {{ formatDateTime(row.updated_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              更新者: {{ row. updated_username }} ({{
                formatDistanceStrictDateTime(row.updated_at)
              }})
            </div>
          </div>
        </template>
        <template #status-data="{ row }">
          <UBadge
            :label="deployStatusObject(row.status).label"
            :color="deployStatusObject(row.status).color"
            :icon="deployStatusObject(row.status).icon"
            :variant="'subtle'"
            :trailing="false"
            size="sm"
            :ui="{
              rounded: 'rounded-full'
            }"
          />
        </template>
        <template #result-data="{ row }">
          <UBadge
            :label="deployResultObject(row.result).label"
            :color="deployResultObject(row.result).color"
            :icon="deployResultObject(row.result).icon"
            :variant="'subtle'"
            :trailing="false"
            size="sm"
            :ui="{
              rounded: 'rounded-full'
            }"
          />
        </template>
        <template #logs-data="{ row }">
          <div class="flex flex-row items-center justify-center">
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-archive-box"
              size="xs"
              @click="emit('view-logs', row)"
            />
          </div>
        </template>
      </UTable>
    </div>
  </div>
</template>

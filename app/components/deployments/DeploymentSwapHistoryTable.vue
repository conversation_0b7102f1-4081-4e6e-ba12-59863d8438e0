<script setup lang="ts">
const props = defineProps({
  rows: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Table columns
const columns = [
  {
    key: 'type',
    label: '処理',
    sortable: false
  },
  {
    key: 'env_id',
    label: 'スワップ後本番環境',
    sortable: false
  },
  {
    key: 'version',
    label: 'スワップ後バージョン',
    sortable: false
  },
  {
    key: 'status',
    label: '処理結果',
    sortable: false
  },
  {
    key: 'created_at',
    label: '実施日時',
    sortable: false
  },
  {
    key: 'created_username',
    label: '実施者',
    sortable: false
  }
]
</script>

<template>
  <div>
    <BaseLoading
      v-if="loading"
      class="py-6"
    />
    <div v-else>
      <UTable
        v-if="rows"
        :rows="rows"
        :columns="columns"
        class="w-full"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
        :loading="loading"
      >
        <template #type-data>
          <div class="text-sm">
            スワップ
          </div>
        </template>
        <template #status-data>
          <UBadge
            label="成功"
            color="green"
            icon="heroicons:check-circle"
            variant="subtle"
            size="sm"
            :ui="{
              rounded: 'rounded-full'
            }"
          />
        </template>

        <template #created_at-data="{ row }">
          <div class="text-sm">
            {{ formatDateTime(row.created_at) }}
          </div>
        </template>

        <template #updated_at-data="{ row }">
          <div class="text-sm">
            {{ formatDateTime(row.updated_at) }}
          </div>
        </template>
      </UTable>
    </div>
  </div>
</template>

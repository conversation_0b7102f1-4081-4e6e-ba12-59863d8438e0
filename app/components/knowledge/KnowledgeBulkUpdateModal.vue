<script setup lang="ts">
const { selectedTenantId, selectedEnvId } = useApp()
const trainingDatasStore = useTrainingDatasStore()
const { loadings } = storeToRefs(trainingDatasStore)
const route = useRoute()
const props = defineProps({
  show: Bo<PERSON>an,
  targets: Array as PropType<any[]>
})
const toast = useToast()
const bulkUpdateForm = ref({
  priority: '',
  labels: [],
  enabled: null
})
watch(
  () => props.show,
  (value) => {
    if (value) {
      bulkUpdateForm.value = {
        priority: '',
        labels: [],
        enabled: null
      }
    }
  }
)
const uiClass = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const ui = {
  formGroup: {
    container: 'col-span-7',
    inner: 'col-span-5'
  }
}
const emit = defineEmits(['close'])

const isOpen = computed({
  get: () => props.show,
  set: (value) => {
    if (value === false) emit('close')
  }
})

const onSubmit = async () => {
  let bulkUpdates = {}
  if (bulkUpdateForm.value.priority) {
    bulkUpdates = {
      ...bulkUpdates,
      priority: +bulkUpdateForm.value.priority
    }
  }
  if (bulkUpdateForm.value.labels.length) {
    bulkUpdates = {
      ...bulkUpdates,
      labels: bulkUpdateForm.value.labels
    }
  }
  if (bulkUpdateForm.value.enabled) {
    bulkUpdates = {
      ...bulkUpdates,
      enabled: bulkUpdateForm.value.enabled === '有効'
    }
  }
  const result = await trainingDatasStore.bulkUpdateKnowledges(
    props.targets || [],
    bulkUpdates
  )
  if (result) {
    isOpen.value = false
    toast.add({
      title: '成功',
      description: 'ナレッジを一括編集しました。',
      color: 'green'
    })
    trainingDatasStore.fetchTrainingDataDetail(
      route.params.id as string,
      selectedTenantId.value,
      selectedEnvId.value
    )
  }
}
</script>

<template>
  <UModal
    v-model="isOpen"
    :ui="{
      height: 'max-h-[80vh]',
      base: 'scrollbar-thin',
      width: 'w-full sm:max-w-screen-md'
    }"
  >
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!p-0'
        }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div>ナレッジを一括編集 ({{ props.targets?.length }}件)</div>
        </div>
      </template>

      <div class="flex flex-col gap-5 px-4 py-6">
        <UFormGroup
          name="priority"
          label="優先度"
          description="1~9999の範囲の数字を設定ください。"
          help="数字が小さいほど回答生成の際に優先して使用されます。"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <BasePriorityInput
            v-model="bulkUpdateForm.priority"
            placeholder="優先度"
          />
        </UFormGroup>
        <UFormGroup
          v-if="bulkUpdateForm.labels"
          name="label"
          label="ラベル"
          description="ラベルを設定することによりデータソースの分類が可能になります。"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <LabelsSelect v-model="bulkUpdateForm.labels" />
        </UFormGroup>
        <UFormGroup
          name="enabled"
          label="ステータス"
          description="ナレッジの有効/無効を設定してください。"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <USelect
            v-model="bulkUpdateForm.enabled"
            :options="['', '有効', '無効']"
            placeholder="有効/無効"
          />
        </UFormGroup>
      </div>

      <template #footer>
        <div class="flex flex-row justify-between items-center">
          <div />
          <div class="flex justify-end gap-3">
            <UButton
              label="キャンセル"
              color="gray"
              @click="emit('close')"
            />
            <UButton
              :loading="loadings.bulkUpdateKnowledges"
              label="確定"
              color="primary"
              class="w-24 justify-center"
              @click="onSubmit"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

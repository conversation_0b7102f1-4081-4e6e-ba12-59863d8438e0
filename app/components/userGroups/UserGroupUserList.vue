<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-3 !py-3' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700'
      },
      footer: { padding: 'p-4' }
    }"
  >
    <UTable
      v-if="groupUsers"
      :rows="dataPaginated"
      :columns="defaultColumns"
      :model-value="selectedRemoveUsers"
      :loading="loading"
      class="w-full"
      :ui="{
        divide: 'divide-gray-200 dark:divide-gray-800',
        tr: { base: 'group' }
      }"
      @update:model-value="$emit('update:selectedRemoveUsers', $event)"
    >
      <template #username-data="{ row }">
        <div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ row.username }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-500">
            {{ row.email }}
          </div>
        </div>
      </template>

      <template #display_name-data="{ row }">
        <div class="flex items-center gap-1">
          <UAvatar
            v-if="row.display_name"
            size="xs"
            :alt="row?.display_name"
          />
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ row.display_name }}
          </div>
        </div>
      </template>
      <template #user_type-data="{ row }">
        <UBadge
          size="sm"
          variant="subtle"
          :trailing="false"
          v-bind="userTypeObject(row.user_type)"
        />
      </template>

      <template #actions-data="{ row }">
        <div class="flex flex-row items-end justify-end">
          <UButton
            class="row-menu"
            color="red"
            icon="i-heroicons-trash-20-solid"
            size="xs"
            square
            @click="onRemoveUser(row)"
          />
        </div>
      </template>
    </UTable>

    <!-- Number of rows & Pagination -->
    <template #footer>
      <div class="flex flex-wrap justify-between items-center">
        <div class="flex items-center gap-1.5">
          <span class="text-sm leading-5">表示件数:</span>

          <USelect
            v-model="pageCount"
            :options="[3, 5, 10, 20, 30, 40]"
            class="w-20"
          />
        </div>

        <UPagination
          v-model="page"
          :page-count="pageCount"
          :total="pageTotal"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline'
              }
            }
          }"
        />
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
import type { UserAccount } from '~/types/user'
import type { UserGroup } from '~/types/user-group'

const props = defineProps({
  groupUsers: {
    type: Array as PropType<UserAccount[]>,
    default: () => []
  },
  selectedGroup: {
    type: Object as PropType<UserGroup>,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  selectedRemoveUsers: {
    type: Array as PropType<any[]>,
    default: () => []
  }
})

const data = computed(() => {
  return props.groupUsers.filter((user: any) => {
    return (
      user.email?.toLowerCase().includes(search.value?.toLowerCase())
      || user.username?.toLowerCase().includes(search.value?.toLowerCase())
      || user.display_name?.toLowerCase().includes(search.value?.toLowerCase())
      || !search.value
    )
  })
})
const dataPaginated = computed(() => {
  return data.value.slice(
    (page.value - 1) * pageCount.value,
    page.value * pageCount.value
  )
})

const defaultColumns = [
  {
    key: 'display_name',
    label: '名前',
    sortable: true
  },
  {
    key: 'username',
    label: 'アカウント情報',
    sortable: true
  },
  {
    key: 'user_type',
    label: '権限',
    sortable: true
  },
  {
    key: 'actions',
    label: '操作',
    sortable: false,
    class: 'w-fit text-right'
  }
]
const page = ref(1)
const pageTotal = computed(() => props.groupUsers?.length)
const pageCount = ref(5)

const search = ref('')

// watch pageCount, if pageCount changes, reset page to 1
watch(pageCount, () => {
  page.value = 1
})

const emit = defineEmits(['addUser', 'addUsers', 'removeUser', 'update:selectedRemoveUsers'])

const onRemoveUser = (user: UserAccount) => {
  emit('removeUser', user)
}
</script>

<template>
  <UForm
    ref="formRef"
    :schema="schema"
    :state="state"
    class="space-y-4"
    @submit="onSubmit"
  >
    <UFormGroup
      label="ユーザグループ名"
      name="name"
      required
    >
      <UInput
        v-model="state.name"
        type="text"
        placeholder="ユーザグループの名前"
        aria-autocomplete="none"
      />
    </UFormGroup>
    <UFormGroup
      label="ユーザグループの説明"
      name="description"
    >
      <UInput
        v-model="state.description"
        type="text"
        placeholder="ユーザグループの説明"
        aria-autocomplete="none"
      />
    </UFormGroup>
    <UFormGroup
      label="ステータス"
      name="enabled"
    >
      <UToggle
        v-model="state.enabled"
        size="md"
      />
    </UFormGroup>
    <BasicFormButtonGroup
      class="justify-end"
      :loading="props.loading"
      @close="emit('close')"
    />
  </UForm>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const schema = z.object({
  name: z
    .string()
    .min(1, 'ユーザグループ名は 1 文字以上必要です。')
    .max(100, 'ユーザグループ名は 100 文字以下必要です。'),
  description: z
    .string()
    .max(255, 'ユーザグループ名は 255 文字以下必要です。')
    .optional(),
  enabled: z
    .boolean()
})

type Schema = z.output<typeof schema>

const emit = defineEmits(['close', 'submit'])
const props = defineProps<{
  name?: string
  description?: string
  enabled?: boolean
  loading?: boolean
  error: any
}>()

const state = reactive({
  name: '',
  description: '',
  enabled: true
})

async function onSubmit(event: FormSubmitEvent<Schema>) {
  emit('submit', {
    name: event.data.name,
    description: event.data.description && event.data.description.length > 0 ? event.data.description : null,
    enabled: event.data.enabled
  })
}

onMounted(() => {
  if (props.name) {
    state.name = props.name
  }
  if (props.description) {
    state.description = props.description
  }
  if (props.enabled !== undefined) {
    state.enabled = props.enabled
  }
})

const formRef = ref()
// watch props.error
watch(
  () => props.error,
  (error) => {
    if (error) {
      formRef.value?.setErrors([
        { path: 'label', message: error?.error_message }
      ])
    }
  }
)
</script>

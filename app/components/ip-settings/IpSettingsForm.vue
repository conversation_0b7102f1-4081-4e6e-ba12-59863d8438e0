<script setup lang="ts">
import { z } from 'zod'
import type { FormError, FormSubmitEvent } from '#ui/types'

const schema = z.object({
  ip_address: z
    .string()
    .min(1, 'IPアドレスが必要です。')
    .regex(
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(?:[0-9]|[1-2][0-9]|3[0-2]))?$/,
      'IPアドレスの形式が正しくありません。'
    ),
  status: z.boolean(),
  priority: z
    .number()
    .min(1, '優先度は1以上である必要があります。')
    .max(999, '優先度は999以下である必要があります。')
})

type Schema = z.output<typeof schema>

const emit = defineEmits(['close', 'submit'])
const props = defineProps<{
  loading?: boolean
  error: any
  ip_address?: string
  status?: boolean
  priority?: number
  id?: string
  isUpdateMode?: boolean
}>()

const state = reactive({
  ip_address: '',
  status: true,
  priority: 1
})

const validate = (state: any): FormError[] => {
  const errors = []

  // IP address validation
  if (!state.ip_address) {
    errors.push({ path: 'ip_address', message: 'IPアドレスが必要です。' })
  } else {
    const ipRegex
      = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(?:[0-9]|[1-2][0-9]|3[0-2]))?$/
    if (!ipRegex.test(state.ip_address)) {
      errors.push({
        path: 'ip_address',
        message: 'IPアドレスの形式が正しくありません。'
      })
    }
  }

  // Priority validation
  if (state.priority < 1 || state.priority > 999) {
    errors.push({
      path: 'priority',
      message: '優先度は1から999の間で入力してください。'
    })
  }

  return errors
}

async function onSubmit(event: FormSubmitEvent<Schema>) {
  emit('submit', event.data)
}

onMounted(() => {
  if (props.ip_address) {
    state.ip_address = props.ip_address
  }
  if (props.status !== undefined) {
    state.status = props.status
  }
  if (props.priority !== undefined) {
    state.priority = props.priority
  }
})

const formRef = ref()

// Watch props.error for server-side validation errors
watch(
  () => props.error,
  (error) => {
    if (error) {
      const errors = []
      if (error.error_message) {
        errors.push({ path: 'ip_address', message: error.error_message })
      }
      if (error.ip_address) {
        errors.push({ path: 'ip_address', message: error.ip_address })
      }
      if (error.priority) {
        errors.push({ path: 'priority', message: error.priority })
      }
      if (errors.length > 0) {
        formRef.value?.setErrors(errors)
      }
    }
  }
)

// Status options for the toggle
const statusOptions = [
  { label: '有効', value: true },
  { label: '無効', value: false }
]
</script>

<template>
  <UForm
    ref="formRef"
    :schema="schema"
    :validate="validate"
    :validate-on="['submit']"
    :state="state"
    class="space-y-4"
    @submit="onSubmit"
  >
    <UFormGroup
      label="IPアドレス"
      name="ip_address"
      required
      description="制御対象のIPアドレスを入力してください（例: ***********）"
    >
      <UInput
        v-model="state.ip_address"
        type="text"
        placeholder="例: ***********"
        autofocus
      />
    </UFormGroup>

    <UFormGroup
      label="ステータス"
      name="status"
      description="IPアドレス制御の有効/無効を設定します"
    >
      <UToggle
        v-model="state.status"
        :ui="{
          active: 'bg-primary dark:bg-primary',
          inactive: 'bg-gray-200 dark:bg-gray-700'
        }"
      />
      <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
        {{ state.status ? "有効" : "無効" }}
      </span>
    </UFormGroup>

    <UFormGroup
      label="優先度"
      name="priority"
      required
      description="優先度を1から999の間で設定してください（数値が小さいほど高優先度）"
    >
      <UInput
        v-model.number="state.priority"
        type="number"
        placeholder="例: 1"
        min="1"
        max="999"
      />
    </UFormGroup>

    <BasicFormButtonGroup
      class="justify-end"
      :loading="props.loading"
      @close="emit('close')"
    />
  </UForm>
</template>

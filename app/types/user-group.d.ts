export interface UserGroupBase {
  name: string
  description?: string
  enabled: boolean
}

export interface UserGroup extends UserGroupBase {
  id: string
  tenant_id: string
  created_username: string
  updated_username: string
  created_at: string
  updated_at: string
  users_count: number
}

export interface GetAllUserGroups {
  groups: UserGroup[]
}

export interface GetAllUserGroupsFilter {
  name?: string
  enabled?: boolean
}

import type { UserType } from './index.d'
import type { Avatar } from '#ui/types'

/**
 * Base interface for user information
 */
export interface RoleBasedUserBase {
  // Basic user information
  username: string
  email: string
  display_name?: string
  avatar?: Avatar

  // Role-based properties
  role: UserType
  user_type: UserType // For compatibility with existing code

  // Tenant information
  tenant_id: string

  // Metadata
  created_at: string
  updated_at: string
  created_username: string
  updated_username: string
}

/**
 * Extended user interface with permissions
 */
export interface Role<PERSON><PERSON><PERSON><PERSON><PERSON> extends RoleBasedUserBase {
  // Permissions
  permissions: string[]

  // User groups
  groups?: string[]

  // Status
  enabled: boolean

  // Additional properties
  settings?: Record<string, any>
  preferences?: Record<string, any>
}

/**
 * Interface for user creation
 */
export interface CreateRoleBasedUserPayload {
  username: string
  email: string
  password: string
  display_name?: string
  user_type: UserType
  tenant_id: string
}

/**
 * Interface for user update
 */
export interface UpdateRoleBasedUserPayload {
  email?: string
  display_name?: string
  user_type?: UserType
  enabled?: boolean
  password?: string
}

/**
 * Interface for user with JWT token information
 */
export interface Authenticated<PERSON><PERSON><PERSON><PERSON>d<PERSON><PERSON> extends Role<PERSON>asedUser {
  token: string
  refresh_token: string
  exp: number // Token expiration timestamp
  iat: number // Token issued at timestamp
}

/**
 * Interface for user list response
 */
export interface RoleBasedUserListResponse {
  users: RoleBasedUser[]
  total: number
  page: number
  page_size: number
}

/**
 * Type guard to check if a user has a specific role
 * @param user The user to check
 * @param role The role to check for
 * @returns True if the user has the specified role
 */
export function hasRole(user: RoleBasedUser, role: UserType): boolean {
  return user.role === role
}

/**
 * Type guard to check if a user has a specific permission
 * @param user The user to check
 * @param permission The permission to check for
 * @returns True if the user has the specified permission
 */
export function hasPermission(user: RoleBasedUser, permission: string): boolean {
  return user.permissions.includes(permission)
}

/**
 * Type guard to check if a user belongs to a specific group
 * @param user The user to check
 * @param groupId The group ID to check for
 * @returns True if the user belongs to the specified group
 */
export function belongsToGroup(user: RoleBasedUser, groupId: string): boolean {
  return user.groups?.includes(groupId) || false
}

export interface RagKnowledgeDocumentBase {
  name: string
  original_filename?: string
  original_context_type?: string
  source_url?: string
  enabled?: boolean
  extra_settings?: {
    dynamic: boolean
    ignore_tags: string[]
    ignore_classes: string[]
  }
}

export interface RagKnowledgeDocument extends RagKnowledgeDocumentBase {
  id: string
  env_id: string
  tenant_id: string
  deleted?: boolean
  created_username: string
  updated_username: string
  deleted_username?: string
  created_at: string
  updated_at: string
  deleted_at?: string
}

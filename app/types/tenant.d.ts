/**
 * Base interface for tenant creation and update
 */
export interface TenantBase {
  /** Tenant description or display name */
  description?: string
  /** Whether the tenant is enabled */
  enabled: boolean
  /** Optional color theme for the tenant UI */
  color?: string
  /** Optional custom settings for the tenant */
  settings?: Record<string, any>
}

/**
 * Interface for tenant creation payload
 */
export interface CreateTenantPayload extends TenantBase {
  /** Optional tenant ID. If not provided, a UUID will be generated */
  id?: string
}

/**
 * Interface for tenant update payload
 */
export interface UpdateTenantPayload {
  /** Tenant description or display name */
  description?: string
  /** Whether the tenant is enabled */
  enabled?: boolean
  /** Optional color theme for the tenant UI */
  color?: string
  /** Optional custom settings for the tenant */
  settings?: Record<string, any>
}

/**
 * Main tenant interface with all properties
 */
export interface Tenant extends TenantBase {
  /** Unique identifier for the tenant */
  id: string
  /** Username of the user who created the tenant */
  created_username: string
  /** Username of the user who last updated the tenant */
  updated_username: string
  /** Creation timestamp in ISO format */
  created_at: string
  /** Last update timestamp in ISO format */
  updated_at: string
}

/**
 * Extended tenant interface with environments
 */
export interface TenantWithEnvironments extends Tenant {
  /** Array of environments associated with this tenant */
  environments: Array<any>
}

/**
 * Interface for tenant list response
 */
export interface TenantListResponse {
  /** Array of tenants */
  tenants: Tenant[]
  /** Total number of tenants */
  total: number
  /** Current page number */
  page: number
  /** Number of tenants per page */
  page_size: number
}

/**
 * Interface for tenant statistics
 */
export interface TenantStatistics {
  /** Tenant ID */
  tenant_id: string
  /** Number of active users */
  active_users: number
  /** Number of environments */
  environments_count: number
  /** Number of knowledge bases */
  knowledge_count: number
  /** Total storage used in bytes */
  storage_used: number
  /** Number of API calls in the last 30 days */
  api_calls_30d: number
  /** Last activity timestamp */
  last_activity: string
}

/**
 * Interface for tenant quota
 */
export interface TenantQuota {
  /** Tenant ID */
  tenant_id: string
  /** Maximum number of users allowed */
  max_users: number
  /** Maximum number of environments allowed */
  max_environments: number
  /** Maximum storage allowed in bytes */
  max_storage: number
  /** Maximum API calls allowed per month */
  max_api_calls: number
  /** Whether the tenant has exceeded its quota */
  quota_exceeded: boolean
}

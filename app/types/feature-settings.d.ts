export interface FeatureSettingsBase {
  max_turns?: int
  max_questions_in_query?: int
  max_accesses_per_day?: int
  max_sessions_per_day?: int
  knowledge_count?: int
  relevance_limit?: float
  trust_relevance_limit?: float
  is_input_check_enabled: bool
  is_translate_enabled: bool
  is_web_search_enabled: bool
  is_guest_allowed: bool
}

export interface FeatureSettings extends FeatureSettingsBase {
  env_id: string
  tenant_id: string
  created_username?: string
  updated_username?: string
  created_at?: string
  updated_at?: string
}

import type { Avatar } from '#ui/types'

export type UserStatus = 'subscribed' | 'unsubscribed' | 'bounced'

// GUEST: Guest
// STAFF: Staff
// ADMIN: Admin
// PNL_ADMIN: Operator
export enum UserType {
  GUEST = 'guest',
  STAFF = 'staff',
  ADMIN = 'admin',
  PNL_ADMIN = 'operator'
}

export interface User {
  id: number
  name: string
  email: string
  avatar?: Avatar
  status: UserStatus
  location: string
}

export interface Mail {
  id: number
  unread?: boolean
  from: User
  subject: string
  body: string
  date: string
}

export interface Member {
  name: string
  username: string
  role: 'member' | 'owner'
  avatar: Avatar
}

export interface Notification {
  id: number
  unread?: boolean
  sender: User
  body: string
  date: string
}

export type Period = 'daily' | 'weekly' | 'monthly'

export interface Range {
  start: Date
  end: Date
}

export interface Chat {
  chat_id: string
  message: string
  type: 'human' | 'ai'
  sending?: boolean
  related_documents?: any[]
  prompt_tokens?: number
  completion_tokens?: number
  token_count?: number
}

export type TourName = string

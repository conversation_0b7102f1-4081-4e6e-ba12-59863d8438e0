/**
 * Base interface for environment creation and update
 */
export interface EnvironmentBase {
  /** Environment type (1 = Production, 2 = Development) */
  environment: number
  /** Version information */
  version?: string
  /** Whether the environment is enabled */
  enabled?: boolean
  /** Optional description */
  description?: string
}

/**
 * Interface for environment creation payload
 */
export interface CreateEnvironmentPayload extends EnvironmentBase {
  /** Optional environment ID. If not provided, a UUID will be generated */
  id?: string
}

/**
 * Interface for environment update payload
 */
export interface UpdateEnvironmentPayload {
  /** Environment type (1 = Production, 2 = Development) */
  environment?: number
  /** Optional version information */
  version?: string
  /** Whether the environment is enabled */
  enabled?: boolean
  /** Optional description */
  description?: string
}

/**
 * Main environment interface with all properties
 */
export interface Environment extends EnvironmentBase {
  /** Unique identifier for the environment */
  id: string
  /** Tenant ID this environment belongs to */
  tenant_id: string
  /** Username of the user who created the environment */
  created_username: string
  /** Username of the user who last updated the environment */
  updated_username: string
  /** Creation timestamp in ISO format */
  created_at: string
  /** Last update timestamp in ISO format */
  updated_at: string
  /** Latest update timestamp in ISO format */
  latest_updated_at?: string
}

/**
 * Interface for environment list response
 */
export interface EnvironmentListResponse {
  /** Array of environments */
  environments: Environment[]
  /** Total number of environments */
  total: number
  /** Current page number */
  page: number
  /** Number of environments per page */
  page_size: number
}

/**
 * Interface for environment with settings
 */
export interface EnvironmentWithSettings extends Environment {
  /** Custom settings for this environment */
  customSettings?: Record<string, any>
  /** Basic settings for this environment */
  basicSettings?: Record<string, any>
  /** UI properties */
  slot?: string
  /** UI label */
  label?: string
  /** UI click handler */
  click?: () => void
}

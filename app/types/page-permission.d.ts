/**
 * Interface for page permission settings
 */
export interface PagePermission {
  /** Unique identifier for the page */
  id: string
  
  /** Display name of the page */
  name: string
  
  /** Page route path */
  path: string
  
  /** Icon for the page */
  icon?: string
  
  /** Description of the page */
  description?: string
  
  /** Required permissions to access this page */
  requiredPermissions: string[]
  
  /** Category of the page (e.g., settings, dashboard, etc.) */
  category: string
  
  /** Whether the page is enabled */
  enabled: boolean
}

/**
 * Interface for page permission category
 */
export interface PagePermissionCategory {
  /** Unique identifier for the category */
  id: string
  
  /** Display name of the category */
  name: string
  
  /** Icon for the category */
  icon?: string
  
  /** Description of the category */
  description?: string
}

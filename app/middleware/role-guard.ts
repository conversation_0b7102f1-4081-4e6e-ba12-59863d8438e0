export default defineNuxtRouteMiddleware((to) => {
  const authStore = useAuthStore()
  const { authenticated, userRole } = storeToRefs(authStore)

  // If not authenticated, redirect to login
  if (!authenticated.value) {
    return navigateTo('/auth/login')
  }

  // Check if the route has required permissions
  const requiredPermissions = (to.meta.requiredPermissions as string[]) || []
  const requiredRole = to.meta.requiredRole as string

  // If no permissions or role requirements, allow access
  if (!requiredPermissions.length && !requiredRole) {
    return
  }

  // Check if user has required role
  if (requiredRole && userRole.value !== requiredRole) {
    // Redirect to unauthorized page or dashboard
    return navigateTo('/')
  }

  // Check if user has required permissions
  // if (requiredPermissions.length) {
  //   // Use the useAppPermissions composable to check permissions with environment awareness
  //   const { hasAnyPermission } = useAppPermissions()
  //   const hasRequiredPermissions = hasAnyPermission(requiredPermissions)

  //   if (!hasRequiredPermissions) {
  //     // Redirect to unauthorized page or dashboard
  //     // return navigateTo('/')
  //   }
  // }
  return
})

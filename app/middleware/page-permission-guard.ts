export default defineNuxtRouteMiddleware((to) => {
  // Skip for auth routes
  if (to.path.startsWith('/auth')) {
    return
  }

  // Get page permissions store
  const pagePermissionsStore = usePagePermissionsStore()

  // Initialize page permissions if not already initialized
  if (pagePermissionsStore.pagePermissions.length === 0) {
    pagePermissionsStore.initializePagePermissions()
  }

  // Check if the current route is accessible
  const { isCurrentRouteAccessible } = usePagePermissions()

  // If the route is not accessible, redirect to the dashboard
  if (!isCurrentRouteAccessible()) {
    return navigateTo('/')
  }
})

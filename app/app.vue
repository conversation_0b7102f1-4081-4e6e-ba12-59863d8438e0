<script setup lang="ts">
import 'animate.css'
import 'driver.js/dist/driver.css'

const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const ragsStore = useRagsStore()

const { llmRagChatbot } = storeToRefs(ragsStore)
const colorMode = useColorMode()

const appConfig = useAppConfig()

const settingsStore = useSettingsStore()

const { currentColorPrimary } = storeToRefs(settingsStore)
const color = computed(() =>
  colorMode.value === 'dark' ? '#111827' : 'white'
)

useHead({
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { key: 'theme-color', name: 'theme-color', content: color }
  ],
  link: [{ rel: 'icon', href: '/favicon.ico' }],
  htmlAttrs: {
    lang: 'en'
  },
  bodyAttrs: {
    style:
      'font-family: "游ゴシック体", YuGothic, "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック", "Yu Gothic", sans-serif;'
  }
})

const title = 'スマート公共ラボ AIコンシェルジュ'
const description = 'スマート公共ラボ AIコンシェルジュ'

useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description,
  ogImage: 'https://dashboard-template.nuxt.dev/social-card.png',
  twitterImage: 'https://dashboard-template.nuxt.dev/social-card.png',
  twitterCard: 'summary_large_image'
})

// watch currentColorPrimary and update the theme color
watch(
  () => currentColorPrimary.value,
  (value) => {
    if (value) {
      appConfig.ui.primary = value
    }
  }
)

onMounted(() => {
  try {
    if (window?.LLMRagChatbot?.instance) {
      llmRagChatbot.value = window?.LLMRagChatbot?.instance

      // hide the chatbot bubble by default
      llmRagChatbot.value?.hideBubble()
    }
  } catch (error) {
    console.error('Error initializing chatbot:', error)
  }

  // set locale to ja
  useI18n().locale.value = 'ja'
})
</script>

<template>
  <div>
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>

    <UNotifications />
    <UModals />

    <BaseLoaderOverlay />
    <BaseConfirmModal />
  </div>
</template>

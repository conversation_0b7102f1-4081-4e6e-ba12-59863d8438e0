import {
  format,
  formatDistanceStrict,
  formatDuration,
  intervalToDuration
} from 'date-fns'
import { ja } from 'date-fns/locale'
import { UserType } from './../types/index.d'
import { SUPPORTED_LANGUAGES } from '~/constants/supported-languages'

export const contextTypeIcon = (contextType: string) => {
  // check if contextType and contextType is start with http or https
  if (contextType && contextType.startsWith('http')) {
    return 'mdi:web'
  }
  switch (contextType) {
    case 'application/pdf':
      return 'vscode-icons:file-type-pdf2'
    case 'text/plain':
      return 'vscode-icons:file-type-text'
    case 'application/json':
      return 'catppuccin:json'
    case 'text/csv':
      return 'catppuccin:csv'
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      return 'vscode-icons:file-type-excel'
    case 'text/markdown':
      return 'vscode-icons:file-type-markdown'
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return 'vscode-icons:file-type-word'
    case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
      return 'vscode-icons:file-type-powerpoint'
    default:
      return 'mdi:file-document'
  }
}

export const shorterFileName = (fileName: string) => {
  // keep the first 10 characters and the last 5 characters
  return fileName?.length > 20
    ? `${fileName.slice(0, 10)}...${fileName.slice(-10)}`
    : fileName
}

export const formatDateTime = (
  date: string,
  formatString = 'yyyy年MM月dd日 HH:mm:ss'
) => {
  if (!date) return ''
  // return format(new Date(date), 'yyyy年MM月dd日 HH:mm:ss')
  // format date from utc to local
  return format(new Date(date), formatString, { locale: ja })
}

export const formatDistanceStrictDateTime = (date: string) => {
  if (!date) return ''
  return formatDistanceStrict(new Date(date), new Date(), {
    locale: ja,
    addSuffix: true
  })
}

export const formatDurationByDate = (start: string, end: string) => {
  if (!start || !end) return ''
  const duration = intervalToDuration({
    start: new Date(start),
    end: new Date(end)
  })
  return formatDuration(duration, {
    locale: ja
  })
}

export const bytesToSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 Byte'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + sizes[i]
}

export const environmentObject = (env: number) => {
  switch (env) {
    case 1:
      return {
        label: '本番',
        color: 'sky',
        icon: 'fluent-emoji:star'
      }
    case 2:
      return {
        label: '検証',
        color: 'orange',
        icon: 'tdesign:logo-codesandbox'
      }
    default:
      return {
        label: '不明',
        color: 'gray',
        icon: 'mdi:help-circle'
      }
  }
}

export const languageObject = (lang: string) => {
  return SUPPORTED_LANGUAGES.find(l => l.id === lang)
}

export const formatJson = (json: any) => {
  return JSON.stringify(json, null, 2)
}

function hashCode(str) {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }
  return hash
}

function intToRGB(i) {
  const c = (i & 0x00ffffff).toString(16).toUpperCase()

  return '00000'.substring(0, 6 - c.length) + c
}

export function generateColorFromString(str: string) {
  if (!str) return '000000'
  return intToRGB(hashCode(str))
}

export const deployStatusObject = (status: number) => {
  // BEFORE_START = 0
  // PROCESSING = 1
  // FINISHED = 2
  // CANCELLED = 3
  switch (status) {
    case 0:
      return {
        label: '待機',
        color: 'gray',
        icon: 'icon-park-twotone:loading-three'
      }
    case 1:
      return {
        label: '処理中',
        color: 'orange',
        icon: 'line-md:loading-alt-loop'
      }
    case 2:
      return {
        label: '完了',
        color: 'green',
        icon: 'ep:success-filled'
      }
    case 3:
      return {
        label: 'キャンセル済',
        color: 'gray',
        icon: 'mdi:cancel'
      }
    default:
      return {
        label: '不明',
        color: 'gray',
        icon: 'mdi:help-circle'
      }
  }
}
export const deployResultObject = (result: boolean) => {
  switch (result) {
    case true:
      return {
        label: '成功',
        color: 'green',
        icon: 'ep:success-filled'
      }
    case false:
      return {
        label: '失敗',
        color: 'red',
        icon: 'mdi:cancel'
      }
    default:
      return {
        label: '不明',
        color: 'gray',
        icon: 'mdi:help-circle'
      }
  }
}

export const reportRagObject = (result: number) => {
  switch (result) {
    case 1:
      return {
        label: '通常',
        color: 'green'
      }
    case 2:
      return {
        label: 'RAG',
        color: 'blue'
      }
    case 3:
      return {
        label: 'まとめ',
        color: 'yellow'
      }
    case 99:
      return {
        label: '対応ナレッジなし',
        color: 'red'
      }
    case 101:
      return {
        label: '天気',
        color: 'pink'
      }
    default:
      return {
        label: '不明',
        color: 'gray'
      }
  }
}

export const reportContextObject = (result: number) => {
  switch (result) {
    case 0:
      return {
        label: '不明',
        color: 'pink'
      }
    case 1:
      return {
        label: 'ナレッジ',
        color: 'green'
      }
    case 2:
      return {
        label: 'ウェブ検索',
        color: 'blue'
      }
    case 99:
      return {
        label: '対応ナレッジなし',
        color: 'yellow'
      }
    default:
      return {
        label: 'その他',
        color: 'gray'
      }
  }
}

export const reportProcessedObject = (result: boolean) => {
  switch (result) {
    case true:
      return {
        label: '正常',
        color: 'green',
        icon: 'ep:success-filled'
      }
    case false:
      return {
        label: '失敗',
        color: 'red',
        icon: 'mdi:cancel'
      }
    default:
      return {
        label: '不明',
        color: 'gray',
        icon: 'mdi:help-circle'
      }
  }
}

export const userTypeObject = (user_type: UserType) => {
  // GUEST: Guest
  // STAFF: Staff
  // ADMIN: Admin
  // PNL_ADMIN: Operator
  switch (user_type) {
    case UserType.GUEST:
      return {
        label: 'ゲスト',
        color: 'gray',
        icon: 'fluent:guest-20-regular'
      }
    case UserType.STAFF:
      return {
        label: 'スタッフ',
        color: 'blue',
        icon: 'solar:user-id-linear'
      }
    case UserType.ADMIN:
      return {
        label: '管理者',
        color: 'primary',
        icon: 'clarity:administrator-line'
      }
    case UserType.PNL_ADMIN:
      return {
        label: 'オペレーター',
        color: 'yellow',
        icon: 'dashicons:superhero-alt'
      }
    default:
      return {
        label: '不明',
        color: 'gray',
        icon: 'mdi:help-circle'
      }
  }
}

export const isJson = (str: string) => {
  try {
    JSON.parse(str)
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    return false
  }
  return true
}

export const tryParseJson = (str: string, defaultValue = {}) => {
  try {
    return JSON.parse(str)
  } catch (e) {
    return defaultValue
  }
}

export const formatDateTimeForAPI = (date: Date) => {
  if (!date) return ''
  return format(date, 'yyyy-MM-dd')
}

export const shorterString = (str: string) => {
  // keep the first 5 characters and the last 5 characters
  return str?.length > 10 ? `${str.slice(0, 5)}...${str.slice(-5)}` : str
}

export const formatNumber = (number: number) => {
  // format integer number withou decimal
  return Intl.NumberFormat('en-US', {
    notation: 'compact',
    maximumFractionDigits: 0
  }).format(number)
}

export const getElementBySelector = async (selector: string): Promise<Element | null> => {
  const timeout = 1000
  const interval = 100
  let elapsedTime = 0

  return new Promise<Element | null>((resolve) => {
    const checkExistence = () => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
      } else if (elapsedTime >= timeout) {
        resolve(null)
      } else {
        elapsedTime += interval
        setTimeout(checkExistence, interval)
      }
    }
    checkExistence()
  })
}

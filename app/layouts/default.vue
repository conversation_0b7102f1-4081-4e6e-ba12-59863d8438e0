<script setup lang="ts">
const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()
const app = useApp()
const { statisticsNavigators, trainingDataNavigators } = useNavigators()
const trainingDatasStore = useTrainingDatasStore()
const ragsStore = useRagsStore()
const { trainingDatasNavigators, loadings } = storeToRefs(trainingDatasStore)
const {
  roleBasedFooterNavigators,
  roleBasedConnectNavigators,
  roleBasedSupportNavigators
} = useRoleBasedNavigators()

const groups = computed(() => {
  return [
    {
      key: 'documents',
      label: 'データソース',
      commands: trainingDatasNavigators.value.map(link => ({
        ...link,
        icon: contextTypeIcon(link.original_context_type || link.source_url)
      }))
    },
    {
      key: 'contents',
      label: (q: any) => q && `「${q}」に関連するデータソース`,
      search: async (q: any) => {
        const result = await trainingDatasStore.searchRelatedContents(q)
        return result?.map((item: any) => ({
          label: item.blob_path,
          icon: contextTypeIcon(item.original_context_type || item.source_url),
          to: `/${app.selectedTenantId.value}/${app.selectedEnvId.value}/training-data/${item.document_id}/${item.id}`
        }))
      }
    }
  ]
})

// Add a reactive variable to track initialization status
const isAppInitialized = ref(false)

onMounted(async () => {
  try {
    // Fetch user first
    await authStore.fetchUser()

    // Initialize app
    await app.initialApp()

    // Mark as initialized
    isAppInitialized.value = true
    ragsStore.llmRagChatbot?.showBubble()
  } catch (error) {
    console.error('Error initializing app:', error)
  }
})

onUnmounted(() => {
  ragsStore.llmRagChatbot?.hideBubble()
})

watchEffect(async () => {
  await ragsStore.ragTenantLogin()
})
</script>

<template>
  <!-- Show loading indicator when app is not initialized -->
  <div
    v-if="!isAppInitialized"
    class="flex items-center justify-center h-screen w-screen"
  >
    <BaseLoader class="w-16" />
  </div>

  <!-- Show dashboard layout when app is initialized -->
  <UDashboardLayout v-else>
    <UDashboardPanel
      :width="250"
      :resizable="{ min: 200, max: 300 }"
      collapsible
    >
      <div class="flex flex-col items-center gap-2 w-full mt-3 mb-1 px-1">
        <TenantsDropdown data-tour="tenants-dropdown" />
        <EnvironmentsDropdown class="px-3" />
      </div>

      <UDashboardSidebar
        :ui="{
          body: 'scrollbar-thin'
        }"
      >
        <template #header>
          <UDashboardSearchButton
            label="検索"
            data-tour="search-button"
          />
        </template>
        <div class="pt-2">
          <div data-tour="statistics-nav">
            <UDashboardSidebarLinks :links="statisticsNavigators" />
          </div>

          <UDivider class="py-2" />

          <div data-tour="training-data-nav">
            <UDashboardSidebarLinks :links="trainingDataNavigators">
              <template #badge="{ link }">
                <UBadge
                  v-if="link.badge"
                  class="flex-shrink-0 ml-auto rounded"
                  color="gray"
                  variant="solid"
                  size="xs"
                >
                  {{ link.badge }}
                </UBadge>

                <template
                  v-else-if="link.original_context_type || link.source_url"
                >
                  <UTooltip
                    class="flex-shrink-0 ml-auto relative rounded"
                    :text="link.original_context_type"
                  >
                    <UIcon
                      :name="
                        contextTypeIcon(
                          link.original_context_type || link.source_url
                        )
                      "
                    />
                  </UTooltip>
                </template>
              </template>
            </UDashboardSidebarLinks>
          </div>

          <UDivider class="py-2" />

          <div data-tour="connects-nav">
            <UDashboardSidebarLinks :links="roleBasedConnectNavigators" />
          </div>

          <UDivider class="py-2" />

          <!-- <div>
            <UDashboardSidebarLinks :links="roleBasedManagementNavigators">
              <template #item="{ link, isActive }">
                <NuxtLink
                  :to="link.to"
                  :class="[
                    'flex items-center gap-2 px-3 py-2 rounded-md text-sm',
                    isActive
                      ? 'text-primary-500 dark:text-primary-400 font-medium'
                      : 'text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800'
                  ]"
                >
                  <UIcon
                    v-if="link.icon"
                    :name="link.icon"
                    class="flex-shrink-0 h-5 w-5"
                  />
                  <span class="flex-1 truncate">{{ link.label }}</span>
                </NuxtLink>
              </template>
            </UDashboardSidebarLinks>
            <div
              v-if="roleBasedManagementOptionsNavigators.length > 0"
              class="pl-6 mt-1"
            >
              <UDashboardSidebarLinks :links="roleBasedManagementOptionsNavigators" />
            </div>
          </div> -->
        </div>

        <div class="flex-1" />

        <UDivider class="sticky bottom-0" />

        <template #footer>
          <div class="flex flex-col gap-1 w-full">
            <!-- <div @click="rerunTour">
              Tour
            </div> -->
            <!-- <UButton
              label="ガイドツアー"
              color="gray"
              variant="ghost"
              size="sm"
              icon="i-heroicons-question-mark-circle"
              @click="rerunTour"
            /> -->
            <div data-tour="logs-nav">
              <UDashboardSidebarLinks
                :links="
                  roleBasedFooterNavigators.filter((link) => link.id === 'logs')
                "
              />
            </div>
            <div data-tour="settings-nav">
              <UDashboardSidebarLinks
                :links="
                  roleBasedFooterNavigators.filter(
                    (link) => link.id === 'settings'
                  )
                "
              />
            </div>

            <div data-tour="user-dropdown">
              <UserDropdown />
            </div>
          </div>
        </template>
      </UDashboardSidebar>
      <div
        class="pt-1 pb-2 bg-gray-50 rounded-t-xl dark:bg-gray-800 px-6 hover:shadow-2xl cursor-pointer hover:scale-105 transition-all duration-200 border-t dark:border-gray-800"
        @click="router.push('/')"
      >
        <BaseLogo />
        <BaseVersion class="text-[8px] pl-1.5" />
      </div>
    </UDashboardPanel>
    <!-- Show content when route is loaded -->
    <slot />
    <!-- ~/components/NotificationsSlideover.vue -->
    <NotificationsSlideover />

    <PreviewSlideover />

    <ClientOnly>
      <LazyUDashboardSearch
        :groups="groups"
        :debounce="1000"
        :placeholder="'データソースの検索'"
      >
        <template #empty-state>
          <div
            class="flex flex-col items-center justify-center p-4 text-center"
          >
            <template v-if="loadings.searchRelatedContents">
              <UIcon
                name="i-heroicons-arrow-path-20-solid"
                class="mb-2 h-8 w-8 text-gray-400 dark:text-gray-500 animate-spin"
              />
              <p class="text-sm text-gray-500 dark:text-gray-400">
                検索中...
              </p>
            </template>
            <template v-else>
              <UIcon
                name="i-heroicons-magnifying-glass-20-solid"
                class="mb-2 h-8 w-8 text-gray-400 dark:text-gray-500"
              />
              <p class="text-sm text-gray-500 dark:text-gray-400">
                データが見つかりませんでした
              </p>
            </template>
          </div>
        </template>
      </LazyUDashboardSearch>
    </ClientOnly>
  </UDashboardLayout>
</template>

// SUPPORTED_LANGUAGES = [
//   "英語",
//   "中国語（繁体字）",
//   "中国語（簡体字）",
//   "広東語",
//   "韓国語",
//   "ベトナム語",
//   "スペイン語",
//   "フランス語",
//   "ドイツ語",
//   "アラビア語",
//   "ポルトガル語",
//   "イタリア語",
//   "オランダ語",
//   "ロシア語",
//   "ヒンディー語",
//   "ベンガル語",
//   "トルコ語",
//   "ギリシャ語",
//   "ポーランド語",
//   "チェコ語",
//   "スウェーデン語",
//   "フィンランド語",
//   "フィリピン語",
//   "ネパール語",
//   "インドネシア語",
//   "ミャンマー語",
//   "タイ語"
// ]

// https://github.com/playnext-lab/llm-rag-api/blob/develop/src/admin-api/multi_language/core/entity/supported_languages.py

export const SUPPORTED_LANGUAGES = [
  {
    id: '日本語',
    label: '日本語',
    desciption: 'japanese',
    icon: 'emojione:flag-for-japan'
  },
  {
    id: '英語',
    label: '英語',
    desciption: 'english',
    icon: 'emojione:flag-for-united-states'
  },
  {
    id: '中国語（繁体字）',
    label: '中国語（繁体字）',
    desciption: 'chinese (traditional)',
    icon: 'emojione:flag-for-taiwan'
  },
  {
    id: '中国語（簡体字）',
    label: '中国語（簡体字）',
    desciption: 'chinese (simplified)',
    icon: 'emojione:flag-for-china'
  },
  {
    id: '広東語',
    label: '広東語',
    desciption: 'cantonese',
    icon: 'emojione:flag-for-hong-kong-sar-china'
  },
  {
    id: '韓国語',
    label: '韓国語',
    desciption: 'korean',
    icon: 'emojione:flag-for-south-korea'
  },
  {
    id: 'ベトナム語',
    label: 'ベトナム語',
    desciption: 'vietnamese',
    icon: 'emojione:flag-for-vietnam'
  },
  {
    id: 'スペイン語',
    label: 'スペイン語',
    desciption: 'spanish',
    icon: 'emojione:flag-for-spain'
  },
  {
    id: 'フランス語',
    label: 'フランス語',
    desciption: 'french',
    icon: 'emojione:flag-for-france'
  },
  {
    id: 'ドイツ語',
    label: 'ドイツ語',
    desciption: 'german',
    icon: 'emojione:flag-for-germany'
  },
  {
    id: 'アラビア語',
    label: 'アラビア語',
    desciption: 'arabic',
    icon: 'emojione:flag-for-saudi-arabia'
  },
  {
    id: 'ポルトガル語',
    label: 'ポルトガル語',
    desciption: 'portuguese',
    icon: 'emojione:flag-for-portugal'
  },
  {
    id: 'イタリア語',
    label: 'イタリア語',
    desciption: 'italian',
    icon: 'emojione:flag-for-italy'
  },
  {
    id: 'オランダ語',
    label: 'オランダ語',
    desciption: 'dutch',
    icon: 'emojione:flag-for-netherlands'
  },
  {
    id: 'ロシア語',
    label: 'ロシア語',
    desciption: 'russian',
    icon: 'emojione:flag-for-russia'
  },
  {
    id: 'ヒンディー語',
    label: 'ヒンディー語',
    desciption: 'hindi',
    icon: 'emojione:flag-for-india'
  },
  {
    id: 'ベンガル語',
    label: 'ベンガル語',
    desciption: 'bengali',
    icon: 'emojione:flag-for-bangladesh'
  },
  {
    id: 'トルコ語',
    label: 'トルコ語',
    desciption: 'turkish',
    icon: 'emojione:flag-for-turkey'
  },
  {
    id: 'ギリシャ語',
    label: 'ギリシャ語',
    desciption: 'greek',
    icon: 'emojione:flag-for-greece'
  },
  {
    id: 'ポーランド語',
    label: 'ポーランド語',
    desciption: 'polish',
    icon: 'emojione:flag-for-poland'
  },
  {
    id: 'チェコ語',
    label: 'チェコ語',
    desciption: 'czech',
    icon: 'emojione:flag-for-czechia'
  },
  {
    id: 'スウェーデン語',
    label: 'スウェーデン語',
    desciption: 'swedish',
    icon: 'emojione:flag-for-sweden'
  },
  {
    id: 'フィンランド語',
    label: 'フィンランド語',
    desciption: 'finnish',
    icon: 'emojione:flag-for-finland'
  },
  {
    id: 'フィリピン語',
    label: 'フィリピン語',
    desciption: 'filipino',
    icon: 'emojione:flag-for-philippines'
  },
  {
    id: 'ネパール語',
    label: 'ネパール語',
    desciption: 'nepali',
    icon: 'emojione:flag-for-nepal'
  },
  {
    id: 'インドネシア語',
    label: 'インドネシア語',
    desciption: 'indonesian',
    icon: 'emojione:flag-for-indonesia'
  },
  {
    id: 'ミャンマー語',
    label: 'ミャンマー語',
    desciption: 'burmese',
    icon: 'emojione:flag-for-myanmar'
  },
  {
    id: 'タイ語',
    label: 'タイ語',
    desciption: 'thai',
    icon: 'emojione:flag-for-thailand'
  }
]

export const DEFAULT_ERROR_MESSAGES = {
  1002: '会話が長くなってきましたね。いったんリセットしますので、再度お尋ねしてもらえますか？',
  8001: 'すみませんが、その質問は答えづらいですね。他におたずねはありますか？',
  8002: '一度にたくさん質問されると説明しづらいです。一つずつ聞いてもらえますか？',
  8003: 'すみませんが、その質問は答えづらいですね。他におたずねはありますか？',
  9999: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？'
}

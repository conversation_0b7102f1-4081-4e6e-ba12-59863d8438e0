import { UserType } from '~/types/index.d'

export const USER_TYPES = [
  {
    id: UserType.GUEST,
    label: userTypeObject(UserType.GUEST).label,
    icon: userTypeObject(UserType.GUEST).icon,
    color: userTypeObject(UserType.GUEST).color
  },
  {
    id: UserType.STAFF,
    label: userTypeObject(UserType.STAFF).label,
    icon: userTypeObject(UserType.STAFF).icon,
    color: userTypeObject(UserType.STAFF).color
  },
  {
    id: UserType.ADMIN,
    label: userTypeObject(UserType.ADMIN).label,
    icon: userTypeObject(UserType.ADMIN).icon,
    color: userTypeObject(UserType.ADMIN).color
  },
  {
    id: UserType.PNL_ADMIN,
    label: userTypeObject(UserType.PNL_ADMIN).label,
    icon: userTypeObject(UserType.PNL_ADMIN).icon,
    color: userTypeObject(UserType.PNL_ADMIN).color
  }
]

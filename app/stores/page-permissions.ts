import { PERMISSIONS } from './permissions'
import type {
  PagePermission,
  PagePermissionCategory
} from '~/types/page-permission'

/**
 * Define page permission categories
 */
const PAGE_CATEGORIES: PagePermissionCategory[] = [
  {
    id: 'dashboard',
    name: '統計',
    icon: 'hugeicons:dashboard-browsing',
    description: '統計情報に関するページ'
  },
  {
    id: 'logs',
    name: 'ログ',
    icon: 'mdi:math-log',
    description: 'ログ情報に関するページ'
  },
  {
    id: 'training_data',
    name: 'データソース',
    icon: 'iconoir:learning',
    description: 'データソースに関するページ'
  },
  {
    id: 'knowledge',
    name: 'ナレッジ',
    icon: 'carbon:ibm-knowledge-catalog',
    description: 'ナレッジに関するページ'
  },
  {
    id: 'basic_settings',
    name: '基本設定',
    icon: 'i-heroicons-cog-8-tooth',
    description: '基本設定に関するページ'
  },

  {
    id: 'knowledge_settings',
    name: 'ナレッジ設定',
    icon: 'quill:label',
    description: 'ナレッジ設定に関するページ'
  },
  {
    id: 'admin_settings',
    name: '管理者設定',
    icon: 'tdesign:system-setting-filled',
    description: '管理者設定に関するページ'
  },
  {
    id: 'deployment',
    name: 'デプロイメント',
    icon: 'mdi:rocket-launch',
    description: 'デプロイメントに関するページ'
  },
  {
    id: 'rag_settings',
    name: 'PNL管理者専用',
    icon: 'carbon:settings-services',
    description: 'RAG設定に関するページ'
  }
]

/**
 * Define default page permissions
 */
const DEFAULT_PAGE_PERMISSIONS: PagePermission[] = [
  // Dashboard pages
  {
    id: 'dashboard',
    name: 'ダッシュボード',
    path: '/',
    icon: 'hugeicons:dashboard-browsing',
    description: 'メインダッシュボード',
    requiredPermissions: [PERMISSIONS.VIEW_DASHBOARD],
    category: 'dashboard',
    enabled: true
  },
  {
    id: 'surveys',
    name: '回答後のアンケート',
    path: '/statistics/surveys',
    icon: 'mdi:poll',
    description: '回答後のアンケート統計',
    requiredPermissions: [PERMISSIONS.VIEW_STATISTICS],
    category: 'dashboard',
    enabled: true
  },
  {
    id: 'unanswered-questions',
    name: '未回答の質問',
    path: '/statistics/unanswered-questions',
    icon: 'mdi:help-circle',
    description: '未回答の質問一覧',
    requiredPermissions: [PERMISSIONS.VIEW_STATISTICS],
    category: 'dashboard',
    enabled: true
  },

  // Logs pages
  {
    id: 'logs',
    name: 'ログ情報',
    path: '/logs',
    icon: 'mdi:math-log',
    description: 'ログ情報一覧',
    requiredPermissions: [PERMISSIONS.VIEW_LOGS],
    category: 'logs',
    enabled: true
  },

  // Training data pages
  {
    id: 'training-data',
    name: 'データソース',
    path: '/training-data',
    icon: 'iconoir:learning',
    description: 'データソース一覧',
    requiredPermissions: [PERMISSIONS.VIEW_TRAINING_DATA],
    category: 'training_data',
    enabled: true
  },
  {
    id: 'training-data-new',
    name: 'データソース追加',
    path: '/training-data/new',
    icon: 'gg:add',
    description: '新しいデータソースの追加',
    requiredPermissions: [PERMISSIONS.CREATE_TRAINING_DATA],
    category: 'training_data',
    enabled: true
  },

  // Knowledge pages
  {
    id: 'knowledge',
    name: 'ナレッジ',
    path: '/knowledge',
    icon: 'carbon:ibm-knowledge-catalog',
    description: 'ナレッジ一覧',
    requiredPermissions: [PERMISSIONS.VIEW_KNOWLEDGE],
    category: 'knowledge',
    enabled: true
  },

  // Basic settings pages
  {
    id: 'basic-settings',
    name: '基本設定',
    path: '/settings',
    icon: 'i-heroicons-cog-8-tooth',
    description: '基本設定',
    requiredPermissions: [PERMISSIONS.VIEW_BASIC_SETTINGS],
    category: 'basic_settings',
    enabled: true
  },
  {
    id: 'error-messages',
    name: 'エラーメッセージ',
    path: '/settings/error-messages',
    icon: 'mdi:alert',
    description: 'エラーメッセージ設定',
    requiredPermissions: [PERMISSIONS.VIEW_ERROR_MESSAGES],
    category: 'basic_settings',
    enabled: true
  },
  {
    id: 'survey-settings',
    name: 'アンケート設定',
    path: '/settings/survey',
    icon: 'mdi:poll',
    description: 'アンケート設定',
    requiredPermissions: [PERMISSIONS.VIEW_SURVEY_SETTINGS],
    category: 'basic_settings',
    enabled: true
  },

  // RAG settings pages
  {
    id: 'rag-settings',
    name: 'RAG設定',
    path: '/settings/feature-settings',
    icon: 'carbon:settings-services',
    description: 'RAG機能設定',
    requiredPermissions: [PERMISSIONS.VIEW_RAG_SETTINGS],
    category: 'rag_settings',
    enabled: true
  },

  // Knowledge settings pages
  {
    id: 'labels',
    name: 'ラベル管理',
    path: '/settings/labels',
    icon: 'quill:label',
    description: 'ラベル管理',
    requiredPermissions: [PERMISSIONS.VIEW_LABELS],
    category: 'knowledge_settings',
    enabled: true
  },
  {
    id: 'categories',
    name: 'カテゴリ管理',
    path: '/settings/categories',
    icon: 'mdi:folder',
    description: 'カテゴリ管理',
    requiredPermissions: [PERMISSIONS.VIEW_CATEGORIES],
    category: 'knowledge_settings',
    enabled: true
  },

  // Admin settings pages
  {
    id: 'tenants',
    name: 'テナント管理',
    path: '/settings/tenants',
    icon: 'mdi:domain',
    description: 'テナント管理',
    requiredPermissions: [PERMISSIONS.VIEW_TENANTS],
    category: 'rag_settings',
    enabled: true
  },
  {
    id: 'operator-users',
    name: 'PNL管理者ユーザ',
    path: '/settings/operator-users',
    icon: 'dashicons:superhero-alt',
    description: 'PNL管理者ユーザ管理',
    requiredPermissions: [PERMISSIONS.VIEW_TENANTS],
    category: 'rag_settings',
    enabled: true
  },
  {
    id: 'users',
    name: 'ユーザ管理',
    path: '/settings/users',
    icon: 'mdi:account-group',
    description: 'ユーザ管理',
    requiredPermissions: [PERMISSIONS.VIEW_USERS],
    category: 'admin_settings',
    enabled: true
  },
  {
    id: 'user-groups',
    name: 'ユーザグループ',
    path: '/settings/user-groups',
    icon: 'mdi:account-group-outline',
    description: 'ユーザグループ管理',
    requiredPermissions: [PERMISSIONS.VIEW_USER_GROUPS],
    category: 'admin_settings',
    enabled: true
  },
  {
    id: 'system-settings',
    name: 'システム設定',
    path: '/settings/system',
    icon: 'tdesign:system-setting-filled',
    description: 'システム設定',
    requiredPermissions: [PERMISSIONS.VIEW_SYSTEM_SETTINGS],
    category: 'rag_settings',
    enabled: true
  },
  {
    id: 'api-action-logs',
    name: 'API操作ログ',
    path: '/settings/api-action-logs',
    icon: 'mdi:api',
    description: 'API操作ログの閲覧',
    requiredPermissions: [PERMISSIONS.VIEW_API_ACTION_LOGS],
    category: 'rag_settings',
    enabled: true
  },

  // Deployment pages
  {
    id: 'deployment',
    name: 'デプロイメント',
    path: '/deployment',
    icon: 'mdi:rocket-launch',
    description: 'デプロイメント管理',
    requiredPermissions: [PERMISSIONS.DEPLOY],
    category: 'deployment',
    enabled: true
  },
  {
    id: 'deployment-history',
    name: 'デプロイ履歴',
    path: '/deployment/history',
    icon: 'mdi:history',
    description: 'デプロイ履歴',
    requiredPermissions: [PERMISSIONS.VIEW_DEPLOY_HISTORY],
    category: 'deployment',
    enabled: true
  }

]

export const usePagePermissionsStore = defineStore('pagePermissionsStore', {
  state: () => ({
    pagePermissions: [] as PagePermission[],
    categories: PAGE_CATEGORIES as PagePermissionCategory[],
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>,

    // Role-specific permissions
    operatorPermissions: Object.values(PERMISSIONS),
    adminPermissions: [] as string[],
    staffPermissions: [] as string[],

    selectedRole: 'summary' as string,
    roleOptions: [
      {
        label: 'サマリー',
        value: 'summary'
      },
      {
        label: 'オペレーター',
        value: 'operator'
      },
      {
        label: '管理者',
        value: 'admin'
      },
      {
        label: 'スタッフ',
        value: 'staff'
      }
    ]
  }),

  getters: {
    /**
     * Get page permissions grouped by category
     */
    pagePermissionsByCategory: (state) => {
      const grouped: Record<string, PagePermission[]> = {}

      state.categories.forEach((category) => {
        grouped[category.id] = state.pagePermissions.filter(
          page => page.category === category.id
        )
      })

      return grouped
    },

    /**
     * Get enabled page permissions
     */
    enabledPagePermissions: (state) => {
      return state.pagePermissions.filter(page => page.enabled)
    },

    /**
     * Get enabled page permissions grouped by category
     */
    enabledPagePermissionsByCategory: (state) => {
      const grouped: Record<string, PagePermission[]> = {}

      state.categories.forEach((category) => {
        grouped[category.id] = state.pagePermissions.filter(
          page => page.category === category.id && page.enabled
        )
      })

      return grouped
    }
  },

  actions: {
    /**
     * Initialize page permissions
     */
    initializePagePermissions() {
      this.pagePermissions = [...DEFAULT_PAGE_PERMISSIONS]

      // Initialize role-specific permissions
      this.initializeRolePermissions()
    },

    /**
     * Initialize role-specific permissions
     */
    initializeRolePermissions() {
      // Operator permissions (all permissions)
      this.operatorPermissions = Object.values(PERMISSIONS)

      // Admin permissions
      this.adminPermissions = [
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.VIEW_STATISTICS,
        PERMISSIONS.VIEW_LOGS,
        PERMISSIONS.VIEW_TRAINING_DATA,
        PERMISSIONS.CREATE_TRAINING_DATA,
        PERMISSIONS.EDIT_TRAINING_DATA,
        PERMISSIONS.DELETE_TRAINING_DATA,
        PERMISSIONS.VIEW_KNOWLEDGE,
        PERMISSIONS.EDIT_KNOWLEDGE,
        PERMISSIONS.DELETE_KNOWLEDGE,
        PERMISSIONS.VIEW_BASIC_SETTINGS,
        PERMISSIONS.EDIT_BASIC_SETTINGS,
        PERMISSIONS.VIEW_ERROR_MESSAGES,
        PERMISSIONS.EDIT_ERROR_MESSAGES,
        PERMISSIONS.VIEW_SURVEY_SETTINGS,
        PERMISSIONS.EDIT_SURVEY_SETTINGS,
        PERMISSIONS.EDIT_RAG_SETTINGS,
        PERMISSIONS.VIEW_LABELS,
        PERMISSIONS.CREATE_LABEL,
        PERMISSIONS.EDIT_LABEL,
        PERMISSIONS.DELETE_LABEL,
        PERMISSIONS.VIEW_CATEGORIES,
        PERMISSIONS.CREATE_CATEGORY,
        PERMISSIONS.EDIT_CATEGORY,
        PERMISSIONS.DELETE_CATEGORY,
        PERMISSIONS.VIEW_USERS,
        PERMISSIONS.CREATE_USER,
        PERMISSIONS.EDIT_USER,
        PERMISSIONS.DELETE_USER,
        PERMISSIONS.VIEW_USER_GROUPS,
        PERMISSIONS.CREATE_USER_GROUP,
        PERMISSIONS.EDIT_USER_GROUP,
        PERMISSIONS.DELETE_USER_GROUP,
        PERMISSIONS.VIEW_ADMIN_SETTINGS,
        PERMISSIONS.DEPLOY,
        PERMISSIONS.VIEW_DEPLOY_HISTORY
      ]

      // Staff permissions
      this.staffPermissions = [
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.VIEW_STATISTICS,
        PERMISSIONS.VIEW_LOGS,
        PERMISSIONS.VIEW_TRAINING_DATA,
        PERMISSIONS.VIEW_KNOWLEDGE,
        PERMISSIONS.VIEW_BASIC_SETTINGS,
        PERMISSIONS.VIEW_ERROR_MESSAGES,
        PERMISSIONS.VIEW_SURVEY_SETTINGS,
        PERMISSIONS.VIEW_LABELS,
        PERMISSIONS.VIEW_CATEGORIES
      ]

      // No guest permissions as guest role doesn't exist
    },

    /**
     * Update page permission
     */
    updatePagePermission(id: string, updates: Partial<PagePermission>) {
      const index = this.pagePermissions.findIndex(page => page.id === id)
      if (index !== -1 && this.pagePermissions[index]) {
        // Create a new object with the updates
        const updatedPage: PagePermission = {
          ...this.pagePermissions[index],
          ...updates
        } as PagePermission

        // Update the page
        this.pagePermissions[index] = updatedPage
      }
    },

    /**
     * Toggle page permission enabled state
     */
    togglePagePermission(id: string) {
      const index = this.pagePermissions.findIndex(page => page.id === id)
      if (index !== -1 && this.pagePermissions[index]) {
        const page = this.pagePermissions[index]
        page.enabled = !page.enabled
      }
    },

    /**
     * Enable all page permissions
     */
    enableAllPagePermissions() {
      this.pagePermissions.forEach((page) => {
        page.enabled = true
      })
    },

    /**
     * Disable all page permissions
     */
    disableAllPagePermissions() {
      this.pagePermissions.forEach((page) => {
        page.enabled = false
      })
    },

    /**
     * Enable page permissions by category
     */
    enablePagePermissionsByCategory(categoryId: string) {
      this.pagePermissions.forEach((page) => {
        if (page.category === categoryId) {
          page.enabled = true
        }
      })
    },

    /**
     * Disable page permissions by category
     */
    disablePagePermissionsByCategory(categoryId: string) {
      this.pagePermissions.forEach((page) => {
        if (page.category === categoryId) {
          page.enabled = false
        }
      })
    },

    /**
     * Reset page permissions to default
     */
    resetPagePermissions() {
      this.pagePermissions = [...DEFAULT_PAGE_PERMISSIONS]
    }
  }
})

import { orderBy } from 'lodash'
import { sub } from 'date-fns'
import type { Range } from '~/types'

export const useDeploymentsStore = defineStore('deploymentsStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    deploymentHistories: [] as any[],
    deploymentLogs: [] as any[],
    swapHistories: [] as any[],
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    latestDeployment: {} as any,
    deployLogFilter: {} as Record<string, any>,
    deployLogTotal: 0,
    deploymentHistoryPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    deploymentLogPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    swapHistoryPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    deploymentHistoryPageTotal: 0,
    deploymentHistoryTotalCount: 0,
    swapHistoryPageTotal: 0,
    swapHistoryTotalCount: 0,
    selectedHistoryTab: 0, // 0 for swap history, 1 for sync history
    range: {
      start: sub(new Date(), { days: 14 }),
      end: new Date()
    } as Range
  }),
  getters: {
    from_date(): string {
      return formatDateTimeForAPI(this.range.start)
    },
    to_date(): string {
      return formatDateTimeForAPI(this.range.end)
    }
  },
  actions: {
    async fetchDeploymentHistories(tenant_id: string) {
      try {
        this.loadings.fetchDeploymentHistories = true
        this.errors.fetchDeploymentHistories = null
        this.deploymentHistories = []
        this.deploymentHistoryPageTotal = 0
        this.deploymentHistoryTotalCount = 0
        const params = {
          page: this.deploymentHistoryPagination.page,
          page_size: this.deploymentHistoryPagination.pageCount,
          order: this.deploymentHistoryPagination.asc,
          created_at_from_date: this.from_date,
          created_at_to_date: this.to_date
        } as Record<string, any>
        const response = await useAPI().adminService.get(
          `/v2/deploy/all/tenants/${tenant_id}`,
          {
            params
          }
        )
        this.deploymentHistories = orderBy(response.data?.histories || [], [
          'created_at'
        ])
        this.deploymentHistories = response.data?.histories
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.deploymentHistoryPageTotal
          = pagination?.total_pages * this.deploymentHistoryPagination.pageCount
        this.deploymentHistoryTotalCount = pagination?.total_count
        return response.data?.deployments
      } catch (error: any) {
        this.errors.fetchDeploymentHistories = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchDeploymentHistories = false
      }
    },

    async swapEnv(tenant_id: string, env_id: string) {
      try {
        this.loadings.swapEnv = true
        this.errors.swapEnv = null
        const response = await useAPI().adminService.put(
          `/v2/tenants/${tenant_id}/env/${env_id}/promote`
        )
        return response.data
      } catch (error: any) {
        this.errors.swapEnv = error?.response?.data || error
        return false
      } finally {
        this.loadings.swapEnv = false
      }
    },
    async syncProdEnvToDev(tenant_id: string, env_id: string) {
      try {
        this.loadings.syncProdEnvToDev = true
        this.errors.syncProdEnvToDev = null
        const response = await useAPI().adminService.post(
          `/v2/deploy/tenants/${tenant_id}/env/${env_id}`
        )
        this.latestDeployment = response.data
        return response.data
      } catch (error: any) {
        this.errors.syncProdEnvToDev = error?.response?.data || error
        return false
      } finally {
        this.loadings.syncProdEnvToDev = false
      }
    },
    async cancelSyncProdEnvToDev(tenant_id: string) {
      try {
        this.loadings.cancelSyncProdEnvToDev = true
        this.errors.cancelSyncProdEnvToDev = null
        const response = await useAPI().adminService.delete(
          `/v2/deploy/tenants/${tenant_id}`
        )
        this.latestDeployment = {
          ...this.latestDeployment,
          status: 3
        }
        return response.data
      } catch (error: any) {
        this.errors.cancelSyncProdEnvToDev = error?.response?.data || error
        return false
      } finally {
        this.loadings.cancelSyncProdEnvToDev = false
      }
    },
    async getLatestDeployment(tenant_id: string) {
      try {
        this.loadings.getLatestDeployment = true
        this.errors.getLatestDeployment = null
        const response = await useAPI().adminService.get(
          `/v2/deploy/latest/tenants/${tenant_id}`
        )
        this.latestDeployment = response.data
        return response.data
      } catch (error: any) {
        this.errors.getLatestDeployment = error?.response?.data || error
        return false
      } finally {
        this.loadings.getLatestDeployment = false
      }
    },
    async getDeploymentLogs(tenant_id: string, deployment_id: string) {
      try {
        this.loadings.getDeploymentLogs = true
        this.errors.getDeploymentLogs = null
        this.deploymentLogs = []
        this.deployLogTotal = 0
        const params = {
          page: this.deploymentLogPagination.page,
          page_size: this.deploymentLogPagination.pageCount,
          order: this.deploymentLogPagination.asc,
          message: this.deployLogFilter.message || null,
          error: this.deployLogFilter.error || null,
          level: this.deployLogFilter.level || null
        } as Record<string, any>
        const response = await useAPI().adminService.get(
          `/v2/deploy/${deployment_id}/logs/tenants/${tenant_id}`,
          {
            params
          }
        )
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.deployLogTotal = pagination?.total_count
        this.deploymentLogs = response.data?.logs || []
        return true
      } catch (error: any) {
        this.errors.getDeploymentLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.getDeploymentLogs = false
      }
    },
    async rollbackStagingVersion(tenant_id: string, env_id: string) {
      try {
        this.loadings.rollbackStagingVersion = true
        this.errors.rollbackStagingVersion = null
        const response = await useAPI().adminService.post(
          `/v2/deploy/revoke/tenants/${tenant_id}/env/${env_id}`
        )
        // reload page
        window.location.reload()
        return response.data
      } catch (error: any) {
        this.errors.rollbackStagingVersion = error?.response?.data || error
        return false
      } finally {
        this.loadings.rollbackStagingVersion = false
      }
    },

    async fetchSwapHistories(tenant_id: string) {
      try {
        this.loadings.fetchSwapHistories = true
        this.errors.fetchSwapHistories = null
        this.swapHistories = []
        this.swapHistoryPageTotal = 0
        this.swapHistoryTotalCount = 0
        const params = {
          page: this.swapHistoryPagination.page,
          page_size: this.swapHistoryPagination.pageCount,
          order: this.swapHistoryPagination.asc,
          from_date: this.from_date,
          to_date: this.to_date
        } as Record<string, any>

        // TODO: Replace with actual swap history API endpoint when available
        // For now, using a placeholder endpoint - this should be updated when the backend provides the actual API
        const response = await useAPI().adminService.get(
          `v2/tenants/${tenant_id}/promote/histories/all`,
          {
            params
          }
        )

        this.swapHistories = response.data?.histories || []
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.swapHistoryPageTotal
          = pagination?.total_pages * this.swapHistoryPagination.pageCount
        this.swapHistoryTotalCount = pagination?.total_count
        return response.data?.histories
      } catch (error: any) {
        this.errors.fetchSwapHistories = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchSwapHistories = false
      }
    }
  }
})

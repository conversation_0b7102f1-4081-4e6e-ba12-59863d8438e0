import { sub, format } from 'date-fns'
import { mkConfig, generateCsv } from 'export-to-csv'
import { saveAs } from 'file-saver'

const DEFAULT_LOGS_FILTER = {
  range: { start: sub(new Date(), { days: 3 }), end: new Date() }
}

export const useLogsStore = defineStore('logsStore', {
  state: () => ({
    logs: [] as any[],
    logsFilter: DEFAULT_LOGS_FILTER,
    logsSubFilter: {} as Record<string, any>,
    logsFilterKeyword: '',
    selectedLog: null,
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    logPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    logPaginationTotal: 0,
    originalLogsData: null as any,
    showAdvancedSearch: false
  }),
  getters: {
    logsFilterRange: state => ({
      from_date: format(state.logsFilter.range.start, 'yyyy-MM-dd'),
      to_date: format(state.logsFilter.range.end, 'yyyy-MM-dd')
    }),
    logsFiltered: (state) => {
      return state.logs.filter((log) => {
        return (
          log?.answer?.includes(state.logsFilterKeyword)
          || log?.query?.includes(state.logsFilterKeyword)
          || log?.query_jp?.includes(state.logsFilterKeyword)
          || log?.answer_jp?.includes(state.logsFilterKeyword)
          || !state.logsFilterKeyword
        )
      })
    },
    logsFilteredUniqueBySessionId(): any[] {
      const uniqueLogs = []
      const uniqueSessionIds = new Set()
      for (const log of this.logsFiltered) {
        if (!uniqueSessionIds.has(log.session_id)) {
          uniqueLogs.push(log)
          uniqueSessionIds.add(log.session_id)
        }
      }
      return uniqueLogs
    },
    allRelatedLogs(): any[] {
      return this.logsFiltered
        .filter(log => log.session_id === this.selectedLog?.session_id)
        .sort(
          (a, b) =>
            new Date(a.query_created_at).getTime()
              - new Date(b.query_created_at).getTime()
        )
    },
    isShowTokenInfo(): boolean {
      const authStore = useAuthStore()
      return authStore.isOperator
    },
    searchLogsRequestParams(): any {
      const params = {
        ...this.logsFilterRange,
        page: this.logPagination.page,
        page_size: this.logPagination.pageCount,
        order: this.logPagination.asc,
        query: this.logsSubFilter?.query || null,
        answer: this.logsSubFilter?.answer || null,
        request_id: this.logsSubFilter?.request_id || null,
        session_id: this.logsSubFilter?.session_id || null,
        category_id: this.logsSubFilter?.category_id ?? null,
        context_type: this.logsSubFilter?.context_type ?? null,
        analyzed_action: this.logsSubFilter?.analyzed_action ?? null,
        processed: this.logsSubFilter?.processed ?? null
      } as any
      // Remove null values
      Object.keys(params).forEach((key: string) => {
        if (params[key] === null || params[key] === '') {
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          delete params[key]
        }
      })
      return params
    }
  },
  actions: {
    async searchLogs(tenant_id: string, _environment: number) {
      try {
        this.loadings.searchLogs = true
        this.errors.searchLogs = null
        this.logs = []
        this.logPaginationTotal = 0
        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()

        // Define the endpoints for different roles
        const response = await roleBasedApiCalls.custom(
          {
            operator: '/v2/histories/all/pagination/tenants/{tenantId}', // Operator can see logs from all tenants
            admin: '/v2/histories/all/pagination', // Admin can only see logs from their tenant
            staff: '/v2/histories/all/pagination', // Staff can only see logs from their tenant
            default: '/v2/histories/all/pagination/tenants/{tenantId}',
            params: {
              tenantId: tenant_id
            },
            query: {
              environment: _environment,
              ...this.searchLogsRequestParams
            }
          },
          'get'
        )

        this.logs = response?.histories || []
        const pagination = tryParseJson(response?.pagination)
        this.logPaginationTotal = pagination?.total_count
        return true
      } catch (error: any) {
        this.errors.searchLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.searchLogs = false
      }
    },

    async exportAllLogs(
      tenant_id: string,
      _environment: number,
      categories: any[]
    ) {
      try {
        this.loadings.exportAllLogs = true
        this.errors.exportAllLogs = null

        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()

        // Define the endpoints for different roles with page_size 10000
        const response = await roleBasedApiCalls.custom(
          {
            operator: '/v2/histories/all/pagination/tenants/{tenantId}', // Operator can see logs from all tenants
            admin: '/v2/histories/all/pagination', // Admin can only see logs from their tenant
            staff: '/v2/histories/all/pagination', // Staff can only see logs from their tenant
            default: '/v2/histories/all/pagination/tenants/{tenantId}',
            params: {
              tenantId: tenant_id
            },
            query: {
              ...this.searchLogsRequestParams,
              page: 1, // Always start from page 1 for export
              page_size: 10000, // Large page size to get all logs
              environment: _environment
            }
          },
          'get'
        )

        const allLogs = response?.histories || []

        // Export the logs to CSV
        this.exportLogsToCSV(allLogs, 'all_logs', categories)

        return true
      } catch (error: any) {
        this.errors.exportAllLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.exportAllLogs = false
      }
    },
    exportLogsToCSV(rows: any[], documentName: string, categories?: any[]) {
      rows = rows.map((row) => {
        const category = categories?.find(
          (category: any) => category.value === row.category_id
        )
        return {
          ...row,
          category: category?.label || row.category_id
        }
      })
      const config = mkConfig({ useKeysAsHeaders: true })
      const csvOutput = generateCsv(config)(rows)
      const blob = new Blob([String(csvOutput)], {
        type: 'text/csv;charset=utf-8'
      })
      saveAs(blob, `${documentName || 'logs'}.csv`)
    },

    addLogsDemoData() {
      // Store original data to restore later
      this.originalLogsData = {
        logs: [...this.logs],
        logPaginationTotal: this.logPaginationTotal
      }

      // Add demo logs data
      this.logs = [
        {
          id: 'demo-log-1',
          session_id: 'demo-session-001',
          request_id: 'demo-request-001',
          query: 'Could you please tell me how to use the AI chatbot?',
          query_jp: 'AIチャットボットの使い方を教えてください',
          answer: 'Thank you for using the AI chatbot. The basic usage is as follows...',
          answer_jp: 'AIチャットボットをご利用いただきありがとうございます。基本的な使い方をご説明いたします...',
          query_created_at: '2024-01-15T10:30:00Z',
          answer_created_at: '2024-01-15T10:30:05Z',
          category_id: 'cat-001',
          context_type: 'KNOWLEDGE',
          analyzed_action: 'search',
          processed: true,
          prompt_tokens: 150,
          completion_tokens: 300,
          token_count: 450,
          isDemo: true
        },
        {
          id: 'demo-log-2',
          session_id: 'demo-session-001',
          request_id: 'demo-request-002',
          query: 'パスワードをリセットする方法は？',
          query_jp: 'パスワードをリセットする方法は？',
          answer: 'パスワードのリセット方法についてご案内いたします。ログイン画面の「パスワードを忘れた方」をクリックして...',
          answer_jp: 'パスワードのリセット方法についてご案内いたします。ログイン画面の「パスワードを忘れた方」をクリックして...',
          query_created_at: '2024-01-15T10:32:00Z',
          answer_created_at: '2024-01-15T10:32:03Z',
          category_id: 'cat-002',
          context_type: 'KNOWLEDGE',
          analyzed_action: 'search',
          processed: true,
          prompt_tokens: 120,
          completion_tokens: 250,
          token_count: 370,
          isDemo: true
        },
        {
          id: 'demo-log-3',
          session_id: 'demo-session-002',
          request_id: 'demo-request-003',
          query: '最新の機能について教えて',
          query_jp: '最新の機能について教えて',
          answer: '最新機能についてお答えします。新しく追加された機能には以下があります...',
          answer_jp: '最新機能についてお答えします。新しく追加された機能には以下があります...',
          query_created_at: '2024-01-15T11:00:00Z',
          answer_created_at: '2024-01-15T11:00:04Z',
          category_id: 'cat-003',
          context_type: 'WEBSEARCH',
          analyzed_action: 'web_search',
          processed: true,
          prompt_tokens: 180,
          completion_tokens: 320,
          token_count: 500,
          isDemo: true
        },
        {
          id: 'demo-log-4',
          session_id: 'demo-session-003',
          request_id: 'demo-request-004',
          query: 'エラーが発生しました',
          query_jp: 'エラーが発生しました',
          answer: 'エラーについて詳しく教えてください。どのような操作を行った際にエラーが発生しましたか？',
          answer_jp: 'エラーについて詳しく教えてください。どのような操作を行った際にエラーが発生しましたか？',
          query_created_at: '2024-01-15T11:15:00Z',
          answer_created_at: '2024-01-15T11:15:02Z',
          category_id: 'cat-004',
          context_type: 'NOKNOWLEDGEMATCH',
          analyzed_action: 'fallback',
          processed: false,
          prompt_tokens: 100,
          completion_tokens: 200,
          token_count: 300,
          isDemo: true
        },
        {
          id: 'demo-log-5',
          session_id: 'demo-session-004',
          request_id: 'demo-request-005',
          query: 'サービスの料金プランについて',
          query_jp: 'サービスの料金プランについて',
          answer: '料金プランについてご案内いたします。基本プラン、スタンダードプラン、プレミアムプランをご用意しています...',
          answer_jp: '料金プランについてご案内いたします。基本プラン、スタンダードプラン、プレミアムプランをご用意しています...',
          query_created_at: '2024-01-15T11:30:00Z',
          answer_created_at: '2024-01-15T11:30:06Z',
          category_id: 'cat-005',
          context_type: 'KNOWLEDGE',
          analyzed_action: 'search',
          processed: true,
          prompt_tokens: 160,
          completion_tokens: 280,
          token_count: 440,
          isDemo: true
        }
      ]
      this.logPaginationTotal = 5
    },

    removeLogsDemoData() {
      // Restore original data if it exists
      if (this.originalLogsData) {
        this.logs = this.originalLogsData.logs
        this.logPaginationTotal = this.originalLogsData.logPaginationTotal
        this.originalLogsData = null
      } else {
        // Fallback: filter out demo data
        this.logs = this.logs.filter((item: any) => !item.isDemo)
        this.logPaginationTotal = this.logs.length
      }
    }
  }
})

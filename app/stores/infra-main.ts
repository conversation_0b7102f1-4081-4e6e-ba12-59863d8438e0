import { cloneDeep } from 'lodash'

export const useInfraMainStore = defineStore('infraMainStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    setting: {} as Record<string, any>,
    ogirinalSetting: {} as Record<string, any>,
    settingFields: [
      {
        key: 'endpoint',
        label: 'Endpoint',
        required: true
      },
      {
        key: 'index_name',
        label: 'Index Name',
        required: true
      },
      {
        key: 'key',
        label: 'Key',
        required: true,
        type: 'password'
      },
      {
        key: 'embeddings_main_model_name',
        label: 'Embeddings Main Model Name',
        required: true
      },
      {
        key: 'embeddings_sub_model_name',
        label: 'Embeddings Sub Model Name'
      },
      {
        key: 'llm_main_model_name',
        label: 'LLM Main Model Name',
        required: true
      },
      {
        key: 'llm_sub_model_name',
        label: 'LLM Sub Model Name'
      },
      {
        key: 'tenant_id',
        label: 'テナントID',
        readonly: true
      },
      {
        key: 'env_id',
        label: 'Env ID',
        readonly: true
      },

      {
        key: 'created_username',
        label: '作成者',
        readonly: true
      },
      {
        key: 'created_at',
        label: '作成日時',
        readonly: true
      },
      {
        key: 'updated_username',
        label: '更新者',
        readonly: true
      },
      {
        key: 'updated_at',
        label: '更新日時',
        readonly: true
      }
    ] as Record<string, any>[]
  }),
  getters: {
    editableSettingFields(): any[] {
      return this.settingFields.filter((field: any) => !field.readonly)
    },
    readonlySettingFields(): any[] {
      return this.settingFields.filter((field: any) => field.readonly)
    }
  },
  actions: {
    async createOrUpdateSetting(
      tenant_id: string,
      env_id: string,
      payload: any
    ) {
      try {
        this.loadings.createOrUpdateSetting = true
        this.errors.createOrUpdateSetting = null
        let response: any
        // check if setting is new or update (if setting has created_at, it's update)
        if (this.setting.created_at) {
          response = await useAPI().adminService.put(
            `/v2/infra/main/tenants/${tenant_id}/env/${env_id}`,
            payload
          )
        } else {
          response = await useAPI().adminService.post(
            `/v2/infra/main/tenants/${tenant_id}/env/${env_id}`,
            payload
          )
        }
        this.setting = response.data
        this.ogirinalSetting = cloneDeep(response.data)
        return true
      } catch (error: any) {
        this.errors.createOrUpdateSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.createOrUpdateSetting = false
      }
    },

    async getSetting(tenant_id: string, env_id: string) {
      try {
        this.loadings.getSetting = true
        this.errors.getSetting = null
        const res = await useAPI().adminService.get(
          `/v2/infra/main/tenants/${tenant_id}/env/${env_id}`
        )
        this.setting = res.data
        this.ogirinalSetting = cloneDeep(res.data)
        return res.data
      } catch (error: any) {
        this.errors.getSetting = error?.response?.data || error
        this.setting = {}
        this.ogirinalSetting = {}
        return false
      } finally {
        this.loadings.getSetting = false
      }
    },

    resetSetting() {
      this.setting = cloneDeep(this.ogirinalSetting)
    }
  }
})

import type {
  Environment,
  EnvironmentWithSettings,
  CreateEnvironmentPayload,
  UpdateEnvironmentPayload,
  EnvironmentListResponse
} from '~/types/environment'
import { UserType } from '~/types/index.d'

export const useEnvironmentsStore = defineStore('environmentsStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    environments: {} as Record<string, any>,
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    selectedEnvId: useRoute().params.env as string
  }),
  getters: {
    environmentsDropdownItems: (state) => {
      const { selectedTenantId } = useApp()
      const tenantsStore = useTenantsStore()
      return state.environments[tenantsStore.selectedTenantId]?.map(
        (env: Environment) => ({
          ...env,
          slot: 'env' + env.id,
          label: env.environment === 1 ? '本番' : '検証',
          click: () => {
            state.selectedEnvId = env.id
            // reload the page with new env id
            window.location.href = `/${selectedTenantId.value}/${env.id}`
          }
        }) as EnvironmentWithSettings
      )
    },
    selectedEnv(): EnvironmentWithSettings | undefined {
      const { selectedTenantId } = useApp()
      const settingsStore = useSettingsStore()
      const { customSettings, basicSettings } = storeToRefs(settingsStore)
      const env
        = this.environmentsDropdownItems?.find(
          (env: any) => env.id === this.selectedEnvId
        ) || this.environmentsDropdownItems?.[0]
      return {
        ...env,
        customSettings:
          customSettings.value?.[selectedTenantId.value]?.[env?.id],
        basicSettings: basicSettings.value?.[selectedTenantId.value]?.[env?.id]
      }
    },
    remainingEnv(): EnvironmentWithSettings | undefined {
      const { selectedTenantId } = useApp()
      const settingsStore = useSettingsStore()
      const { customSettings, basicSettings } = storeToRefs(settingsStore)
      const env = this.environmentsDropdownItems?.find(
        (env: any) => env.id !== this.selectedEnvId
      )
      return {
        ...env,
        customSettings:
          customSettings.value?.[selectedTenantId.value]?.[env?.id],
        basicSettings: basicSettings.value?.[selectedTenantId.value]?.[env?.id]
      }
    },
    isSelectedEnvIsDev(): boolean {
      return this.selectedEnv?.environment === 2
    },
    isDevAndProdHasSameVersion(): boolean {
      return this.selectedEnv?.version === this.remainingEnv?.version
    },
    isDevVersionOlderThanProd(): boolean {
      return parseFloat(this.selectedEnv?.version) < parseFloat(this.remainingEnv?.version)
    },
    isSelectedEnvIsProd(): boolean {
      return this.selectedEnv?.environment === 1
    }
  },
  actions: {
    /**
     * Fetch all environments for a tenant based on user role
     * - For operators (PNL_ADMIN): Can fetch environments for any tenant
     * - For non-operators: Can only fetch environments for their assigned tenant
     */
    async fetchAllEnvs(tenant_id: string, force = false) {
      const settingsStore = useSettingsStore()
      // return if already fetched
      if (this.environments[tenant_id] && !force) {
        return true
      }

      try {
        this.loadings.fetchAllEnvs = true
        this.errors.fetchAllEnvs = null

        const authStore = useAuthStore()
        const { userRole } = storeToRefs(authStore)
        const { selectedTenantId } = useApp()

        // For non-operators, verify they're accessing their assigned tenant
        if (userRole.value !== UserType.PNL_ADMIN && tenant_id !== selectedTenantId.value) {
          console.warn('Non-operator users can only access their assigned tenant')
          this.errors.fetchAllEnvs = { message: 'Access denied: You can only access your assigned tenant' }
          return false
        }

        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response = await roleBasedApiCalls.environments.fetch<EnvironmentListResponse>(tenant_id)

        this.environments[tenant_id] = response?.environments || []
        if (!this.selectedEnvId) {
          this.selectedEnvId = this.environments[tenant_id][0]?.id || ''
        }

        // fetch all custom settings and basic settings
        // await Promise.allSettled(
        //   this.environments[tenant_id].map((env: Environment) => [
        //     settingsStore.fetchCustomSettings(tenant_id, env.id),
        //     settingsStore.fetchBasicSettings(tenant_id, env.id)
        //   ])
        // )
        return true
      } catch (error: any) {
        this.errors.fetchAllEnvs = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchAllEnvs = false
      }
    },
    /**
     * Create a new environment for a tenant based on user role
     * - For operators (PNL_ADMIN): Can create environments for any tenant
     * - For admins: Can create environments for their assigned tenant
     * - For staff: Cannot create environments
     */
    async createEnvironment(tenant_id: string, payload: CreateEnvironmentPayload): Promise<Environment | false> {
      try {
        this.loadings.createEnvironment = true
        this.errors.createEnvironment = null

        const authStore = useAuthStore()
        const { userRole } = storeToRefs(authStore)
        const { selectedTenantId } = useApp()

        // For non-operators, verify they're accessing their assigned tenant
        if (userRole.value !== UserType.PNL_ADMIN && tenant_id !== selectedTenantId.value) {
          console.warn('Non-operator users can only access their assigned tenant')
          this.errors.createEnvironment = { message: 'Access denied: You can only access your assigned tenant' }
          return false
        }

        // Staff cannot create environments
        if (userRole.value === UserType.STAFF) {
          console.warn('Staff users cannot create environments')
          this.errors.createEnvironment = { message: 'Access denied: Staff users cannot create environments' }
          return false
        }

        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response: Environment = await roleBasedApiCalls.environments.create<Environment>(tenant_id, payload)

        if (!this.environments[tenant_id]) {
          this.environments[tenant_id] = []
        }
        this.environments[tenant_id].push(response)
        return response
      } catch (error: any) {
        this.errors.createEnvironment = error?.response?.data || error
        return false
      } finally {
        this.loadings.createEnvironment = false
      }
    },
    /**
     * Delete an environment from a tenant based on user role
     * - For operators (PNL_ADMIN): Can delete environments from any tenant
     * - For admins: Can delete environments from their assigned tenant
     * - For staff: Cannot delete environments
     */
    async deleteEnvironment(tenant_id: string, env_id: string): Promise<boolean> {
      try {
        this.loadings.deleteEnvironment = true
        this.errors.deleteEnvironment = null

        const authStore = useAuthStore()
        const { userRole } = storeToRefs(authStore)
        const { selectedTenantId } = useApp()

        // For non-operators, verify they're accessing their assigned tenant
        if (userRole.value !== UserType.PNL_ADMIN && tenant_id !== selectedTenantId.value) {
          console.warn('Non-operator users can only access their assigned tenant')
          this.errors.deleteEnvironment = { message: 'Access denied: You can only access your assigned tenant' }
          return false
        }

        // Staff cannot delete environments
        if (userRole.value === UserType.STAFF) {
          console.warn('Staff users cannot delete environments')
          this.errors.deleteEnvironment = { message: 'Access denied: Staff users cannot delete environments' }
          return false
        }

        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()
        await roleBasedApiCalls.environments.delete(tenant_id, env_id)

        this.environments[tenant_id] = this.environments[tenant_id]?.filter(
          (env: Environment) => env.id !== env_id
        )
        return true
      } catch (error: any) {
        this.errors.deleteEnvironment = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteEnvironment = false
      }
    },

    /**
     * Update an environment in a tenant based on user role
     * - For operators (PNL_ADMIN): Can update environments in any tenant
     * - For admins: Can update environments in their assigned tenant
     * - For staff: Cannot update environments
     */
    async updateEnvironment(tenant_id: string, env_id: string, payload: UpdateEnvironmentPayload): Promise<Environment | false> {
      try {
        this.loadings.updateEnvironment = true
        this.errors.updateEnvironment = null

        const authStore = useAuthStore()
        const { userRole } = storeToRefs(authStore)
        const { selectedTenantId } = useApp()

        // For non-operators, verify they're accessing their assigned tenant
        if (userRole.value !== UserType.PNL_ADMIN && tenant_id !== selectedTenantId.value) {
          console.warn('Non-operator users can only access their assigned tenant')
          this.errors.updateEnvironment = { message: 'Access denied: You can only access your assigned tenant' }
          return false
        }

        // Staff cannot update environments
        if (userRole.value === UserType.STAFF) {
          console.warn('Staff users cannot update environments')
          this.errors.updateEnvironment = { message: 'Access denied: Staff users cannot update environments' }
          return false
        }

        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response: Environment = await roleBasedApiCalls.environments.update<Environment>(tenant_id, env_id, payload)

        // Update the environment in the store
        if (this.environments[tenant_id]) {
          this.environments[tenant_id] = this.environments[tenant_id].map((env: Environment) => {
            if (env.id === env_id) {
              return { ...env, ...response }
            }
            return env
          })
        }

        return response
      } catch (error: any) {
        this.errors.updateEnvironment = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateEnvironment = false
      }
    }
  }
})

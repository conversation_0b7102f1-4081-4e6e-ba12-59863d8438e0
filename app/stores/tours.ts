import { driver } from 'driver.js'

const route = useRoute()

const sleep = (ms: number) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}
export const useToursStore = defineStore('toursStore', {
  persist: [
    {
      pick: ['tourSeens'],
      storage: localStorage
    }
  ],
  state: () => ({
    originalRoute: route.fullPath,
    driverObj: null as ReturnType<typeof driver> | null,
    baseConfig: {
      showProgress: true,
      smoothScroll: true,
      popoverClass: 'custom-driver-popover',
      nextBtnText: '次へ',
      prevBtnText: '戻る',
      doneBtnText: '完了',
      overlayClickBehavior: 'nextStep',
      stagePadding: 4,
      disableActiveInteraction: true,
      onDestroyed: () => {
        const toursStore = useToursStore()
        // replace window location with original route
        if (
          toursStore.originalRoute
          && route.fullPath !== toursStore.originalRoute
        ) {
          window.location.replace(toursStore.originalRoute)
          toursStore.originalRoute = ''
        }
      },
      onDestroyStarted: () => {
        const toursStore = useToursStore()

        if (
          !toursStore.driverObj?.hasNextStep()
          || confirm('ツアーを終了しますか？')
        ) {
          toursStore.driverObj?.destroy()
          toursStore.driverObj = null
        }
      }
    } as const,
    tourSeens: {
      welcome: false
    }
  }),
  getters: {},
  actions: {
    async quickNavigateTo(routePath: string) {
      const { selectedTenantId, selectedEnvId } = useApp()
      await navigateTo(
        `/${selectedTenantId.value}/${selectedEnvId.value}/${routePath}`
      )
    },

    addSurveyDemoData() {
      const reportsStore = useReportsStore()
      // Add demo survey data for tour demonstration
      reportsStore.surveyReportsData = [
        {
          session_id: 'demo-session-001',
          request_id: 'demo-request-001',
          query: 'チャットボットの使い方を教えてください',
          answer:
            'チャットボットの基本的な使い方をご説明します。まず、質問を入力してください...',
          score: 5,
          survey_date: new Date().toISOString(),
          isDemo: true
        },
        {
          session_id: 'demo-session-002',
          request_id: 'demo-request-002',
          query: '営業時間を教えてください',
          answer: '営業時間は平日9:00-18:00です。',
          score: 4,
          survey_date: new Date().toISOString(),
          isDemo: true
        },
        {
          session_id: 'demo-session-003',
          request_id: 'demo-request-003',
          query: '料金について知りたいです',
          answer: '料金プランについてご案内します...',
          score: 3,
          survey_date: new Date().toISOString(),
          isDemo: true
        }
      ]
      reportsStore.surveyReportsDataTotal = 3
    },
    addLabelDemoData() {
      const labelStore = useLabelsStore()
      labelStore.labels = [
        {
          label: '男',
          env_id: 'demo',
          tenant_id: 'demo',
          key: '1',
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: '2020-01-15T13:41:06.060855+09:00',
          updated_at: '2020-08-15T13:41:06.060855+09:00',
          isDemo: true
        },
        {
          label: '女',
          env_id: 'demo',
          tenant_id: 'demo',
          key: '2',
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: '2020-04-15T13:41:25.782122+09:00',
          updated_at: '2020-08-15T13:41:25.782122+09:00',
          isDemo: true
        }
      ]
    },
    addCategoryDemoData() {
      const categoriesStore = useCategoriesStore()
      categoriesStore.categories = [
        {
          category: '地方',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 1,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: '2020-01-15T13:41:06.060855+09:00',
          updated_at: '2020-08-15T13:41:06.060855+09:00',
          isDemo: true
        },
        {
          category: '都会',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 2,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: '2020-04-15T13:41:25.782122+09:00',
          updated_at: '2020-08-15T13:41:25.782122+09:00',
          isDemo: true
        }
      ]
    },
    removeCategoryDemoData() {
      const categoriesStore = useCategoriesStore()
      categoriesStore.categories = categoriesStore.categories.filter(
        (item: any) => !item.isDemo
      )
    },
    removeLabelDemoData() {
      const labelStore = useLabelsStore()
      labelStore.labels = labelStore.labels.filter((item: any) => !item.isDemo)
    },
    removeSurveyDemoData() {
      const reportsStore = useReportsStore()
      reportsStore.surveyReportsData = reportsStore.surveyReportsData.filter(
        (item: any) => !item.isDemo
      )
      reportsStore.surveyReportsDataTotal
        = reportsStore.surveyReportsData.length
    },
    showWelcomeTour() {
      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: 'スマート公共ラボ AIコンシェルジュへようこそ！',
              description:
                'このガイドでは、システムの基本的な機能と使い方をご紹介します。各ステップで重要な機能を確認していきましょう。'
            }
          },
          {
            element: '[data-tour="tenants-dropdown"]',
            popover: {
              title: 'テナント情報',
              description: 'テナント（組織）の情報を表示します。',
              side: 'bottom'
            }
          },
          {
            element: '#env-dropdown>div>button',
            popover: {
              title: '環境切り替え',
              description:
                '本番環境と検証環境を切り替えることができます。設定やデータは環境ごとに独立して管理されます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="search-button"]',
            popover: {
              title: 'データソース検索',
              description:
                'ここからデータソースを素早く検索できます。ファイル名やコンテンツ内容で検索が可能です。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="statistics-nav"] > ul > li:first-child',
            popover: {
              title: '統計・ダッシュボード',
              description:
                'システムの利用状況や統計情報を確認できます。質問数、回答率、アンケート結果などが表示されます。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="statistics-nav"] > ul > li:nth-child(2)',
            popover: {
              title: 'ログ情報',
              description:
                'チャットボットとの会話履歴やシステムログを確認できます。問題の調査や利用状況の分析に活用できます。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="training-data-nav"]',
            popover: {
              title: 'データソース管理',
              description:
                'チャットボットのデータソースを管理します。ファイルアップロード、ウェブサイト連携、Q&Aデータの登録ができます。新しいデータを追加したり、既存のデータを編集したりできます。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="connects-nav"]',
            popover: {
              title: 'コネクト機能',
              description:
                'チャットボットを外部サービスと連携させる機能です。ウェブサイトへの埋め込みやLINE LIFFとの連携が可能です。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="settings-nav"]',
            popover: {
              title: '設定メニュー',
              description:
                'システムの各種設定を行います。ユーザー管理、権限設定、チャットボットの動作設定などが含まれます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="user-dropdown"]',
            popover: {
              title: 'ユーザーメニュー',
              description:
                'ログインユーザーの情報確認、パスワード変更、ログアウトなどができます。ダークモード切り替えも可能です。',
              side: 'top'
            }
          },
          {
            popover: {
              title: 'ツアー完了！',
              description:
                'システムの基本的な機能の紹介は以上です。<br><br><strong>便利なキーボードショートカット：</strong><br>• <kbd>⌘-K</kbd>: 検索ポップアップを開く<br><br>各機能を実際に使ってみて、システムに慣れていってください。ご質問がございましたら、サポートまでお気軽にお問い合わせください。'
            }
          }
        ]
      })

      this.driverObj?.drive()
    },
    async showLoginTour() {
      this.originalRoute = route.fullPath
      await navigateTo('/auth/login?tour=login')
      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            element: '[data-tour="login-form"]',
            popover: {
              title: '認証機能の紹介',
              description: 'このガイドでは、ログインの使い方をご紹介します。'
            }
          },
          {
            element: '[data-tour="login-form"] form',
            popover: {
              title: 'ログインフォーム',
              description:
                'ここでテナントID入力とユーザ名とパスワードを入力してログインします。セキュリティのため、強力なパスワードを使用することをお勧めします。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="login-form"] [name="tenant_id"]',
            popover: {
              title: 'テナントID入力',
              description:
                'テナントIDを入力してください。テナントIDは、組織やプロジェクトごとに一意のIDです。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="login-form"] [name="username"]',
            popover: {
              title: 'ユーザ名入力',
              description:
                '登録済みのユーザ名を入力してください。このユーザ名がユーザーIDとして使用されます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="login-form"] [name="password"]',
            popover: {
              title: 'パスワード入力',
              description:
                'パスワードを入力してください。パスワードは8文字以上で、大文字・小文字・数字・記号を含むことを推奨します。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="forgot-password"]',
            popover: {
              title: 'パスワードを忘れた場合',
              description:
                'パスワードを忘れた場合は、このリンクからパスワードリセットの手続きができます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="login-form"] [type="submit"]',
            popover: {
              title: 'ログインボタン',
              description: '入力後、このボタンをクリックしてログインします。',
              side: 'top'
            }
          },
          {
            popover: {
              title: 'ログインツアー完了！',
              description:
                'ログイン機能の紹介は以上です。<br><br>正しい認証情報を入力してシステムにアクセスしてください。<br><br>セキュリティのため、他の人と認証情報を共有しないでください。'
            }
          }
        ]
      })

      setTimeout(() => {
        this.driverObj?.drive()
      }, 1000) // Delay to ensure the login form is fully rendered
    },
    async showFirstLoginTour() {
      this.originalRoute = route.fullPath
      await navigateTo('/auth/login?tour=login')
      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            element: '[data-tour="login-form"]',
            popover: {
              title: '初期のログイン',
              description:
                'このガイドでは、初回ログイン時の使い方をご紹介します。<br>ログイン画面でテナントID、ユーザ名、パスワードを入力してログインします。',
              onNextClick: async () => {
                await navigateTo('/auth/new-password?tour=first-login')
                // Wait for the new password form to load
                this.driverObj?.moveNext()
              }
            }
          },
          {
            element: '[data-tour="new-password-form"]',
            popover: {
              title: 'パスワードの設定',
              description:
                '初回ログイン時は、セキュリティのため新しいパスワードを設定する必要があります。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="new-password-form"] [name="newPassword"]',
            popover: {
              title: 'パスワード',
              description:
                '8文字以上の強力なパスワードを設定してください。英数字と記号を組み合わせることをお勧めします。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="password-requirements"]',
            popover: {
              title: 'パスワード要件',
              description:
                'パスワードの要件が表示されます。すべての条件を満たしているか確認してください。',
              side: 'left'
            }
          },
          {
            element: '[data-tour="new-password-form"] [name="confirmPassword"]',
            popover: {
              title: 'パスワードの確認',
              description: '新しいパスワードを再度入力して確認してください。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="new-password-form"] [type="submit"]',
            popover: {
              title: 'パスワードを設定',
              description:
                'すべての入力が完了したら、このボタンをクリックしてパスワードを更新します。',
              side: 'top'
            }
          },
          {
            popover: {
              title: 'パスワード設定完了！',
              description:
                'パスワードの設定が完了しました。次回ログイン時は、新しいパスワードでログインしてください。セキュリティのため、パスワードは定期的に変更することをお勧めします。'
            }
          }
        ]
      })

      setTimeout(() => {
        this.driverObj?.drive()
      }, 1000) // Delay to ensure the login form is fully rendered
    },
    async showForgotPasswordTour() {
      this.originalRoute = route.fullPath
      await navigateTo('/auth/login?tour=forgot-password')
      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            element: '[data-tour="login-form"]',
            popover: {
              title: 'パスワードリセットの紹介',
              description:
                'このガイドでは、パスワードを忘れた場合のリセット方法をご紹介します。<br>ログイン画面からパスワードリセットの手続きを行います。'
            }
          },
          {
            element: '[data-tour="forgot-password"]',
            popover: {
              title: 'パスワードを忘れた場合',
              description:
                'パスワードを忘れた場合は、このリンクをクリックしてください。',
              side: 'top',
              onNextClick: async () => {
                await navigateTo('/auth/forgot-password?tour=forgot-password')
                // Wait for the new password form to load
                this.driverObj?.moveNext()
              }
            }
          },
          {
            element: '[data-tour="forgot-password-form"] [name="username"]',
            popover: {
              title: 'パスワードリセットフォーム',
              description:
                'ここでユーザ名を入力して、パスワードリセットのためのコードをメールに送信します。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="forgot-password-form"] [type="submit"]',
            popover: {
              title: 'コード送信ボタン',
              description:
                'ユーザ名を入力後、このボタンをクリックしてパスワードリセットのためのコードを送信します。',
              side: 'top',
              onNextClick: async () => {
                await navigateTo('/auth/reset-password?tour=reset-password')
                // Wait for the reset password form to load
                this.driverObj?.moveNext()
              }
            }
          },
          {
            element: '[data-tour="reset-password-form"]',
            popover: {
              title: 'パスワードリセットフォーム',
              description:
                'ここで、送信されたコードを入力して新しいパスワードを設定します。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="reset-password-form"] [name="code"]',
            popover: {
              title: 'コード入力',
              description:
                '受け取ったコードを入力してください。このコードは一度限り有効です。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="reset-password-form"] [name="newPassword"]',
            popover: {
              title: '新しいパスワード',
              description:
                '新しいパスワードを入力してください。8文字以上で、英数字と記号を含むことをお勧めします。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="password-requirements"]',
            popover: {
              title: 'パスワード要件',
              description:
                'パスワードの要件が表示されます。すべての条件を満たしているか確認してください。',
              side: 'left'
            }
          },
          {
            element:
              '[data-tour="reset-password-form"] [name="confirmPassword"]',
            popover: {
              title: 'パスワードの確認',
              description: '新しいパスワードを再度入力して確認してください。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="reset-password-form"] [type="submit"]',
            popover: {
              title: 'パスワードリセットボタン',
              description:
                'すべての入力が完了したら、このボタンをクリックしてパスワードをリセットします。',
              side: 'top'
            }
          },
          {
            popover: {
              title: 'パスワードリセット完了！',
              description:
                'パスワードのリセットが完了しました。新しいパスワードでログインしてください。セキュリティのため、定期的にパスワードを変更することをお勧めします。'
            }
          }
        ]
      })

      setTimeout(() => {
        this.driverObj?.drive()
      }, 1000) // Delay to ensure the login form is fully rendered
    },
    showChangePasswordTour() {
      const authStore = useAuthStore()
      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: 'パスワード変更の紹介',
              description:
                'このガイドでは、ログイン後のパスワード変更方法をご紹介します。<br>ユーザーメニューからパスワード変更画面にアクセスできます。'
            }
          },
          {
            element: '[data-tour="user-dropdown"]',
            popover: {
              title: 'ユーザーメニュー',
              description: '画面下左のユーザーメニューをクリックします。',
              side: 'top',
              onNextClick: async () => {
                authStore.openUserMenu = true
                nextTick(async () => {
                  await sleep(200)
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[data-tour="user-dropdown"] .change-password',
            popover: {
              title: 'パスワード変更メニュー',
              description:
                'ドロップダウンメニューから「パスワード変更」を選択します。',
              side: 'right',
              onNextClick: async () => {
                authStore.isChangePasswordModalOpen = true
                nextTick(async () => {
                  await sleep(200)
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element:
              '[data-tour="change-password-modal"] [data-headlessui-state="open"]',
            popover: {
              title: 'パスワード変更フォーム',
              description:
                'ここで現在のパスワードと新しいパスワードを入力します。',
              side: 'right'
            }
          },
          {
            element:
              '[data-tour="change-password-modal"] [name="currentPassword"]',
            popover: {
              title: '現在のパスワード入力',
              description:
                '現在のパスワードを入力してください。セキュリティのため、現在のパスワードが必要です。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="change-password-modal"] [name="newPassword"]',
            popover: {
              title: '新しいパスワード入力',
              description: '新しいパスワードを入力してください。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="password-requirements"]',
            popover: {
              title: 'パスワード要件',
              description:
                'パスワードの要件が表示されます。すべての条件を満たしているか確認してください。',
              side: 'left'
            }
          },
          {
            element:
              '[data-tour="change-password-modal"] [name="confirmPassword"]',
            popover: {
              title: 'パスワードの確認',
              description: '新しいパスワードを再度入力して確認してください。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="change-password-modal"] [type="submit"]',
            popover: {
              title: 'パスワード変更ボタン',
              description:
                'すべての入力が完了したら、このボタンをクリックしてパスワードを変更します。',
              side: 'top'
            }
          },
          {
            popover: {
              title: 'パスワード変更完了！',
              description:
                'パスワードの変更が完了しました。次回ログイン時は、新しいパスワードを使用してください。<br><br>セキュリティのため、定期的にパスワードを変更することをお勧めします。'
            }
          }
        ],
        onDestroyed: () => {
          authStore.isChangePasswordModalOpen = false
        }
      })

      setTimeout(() => {
        this.driverObj?.drive()
      }, 100) // Delay to ensure the login form is fully rendered
    },
    showDashboardTour() {
      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: 'ダッシュボードの紹介',
              description:
                'このガイドでは、ダッシュボードの基本的な機能と使い方をご紹介します。<br>ダッシュボードはシステムの中心となる画面で、各種情報や機能にアクセスできます。'
            }
          },
          {
            element:
              '[data-tour="statistics-nav"] [type="tenantId-env-statistics-dashboard"]',
            popover: {
              title: 'ダッシュボード',
              description: 'ここからダッシュボードにアクセスできます。',
              side: 'right',
              onNextClick: async () => {
                await this.quickNavigateTo('')
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[data-tour="dashboard-toolbar"]',
            popover: {
              title: 'ダッシュボード検索',
              description:
                'ダッシュボード上部のツールバーから、期間や集計方式やカテゴリを選択して、統計情報を表示できます。<br>特定の期間や条件でデータを絞り込むことができます。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="dashboard-statistics-card-total-questions"]',
            popover: {
              title: '全質問数',
              description:
                'ダッシュボードの左上に表示される全質問数です。<br>選択している期間内のチャットボットへの全ての質問の合計数を示します。<br/>これをクリックすると、ログ情報画面に遷移します。（同じ期間で絞り込まれます）',
              side: 'right'
            }
          },
          {
            element:
              '[data-tour="dashboard-statistics-card-unanswered-questions"]',
            popover: {
              title: '未回答数',
              description:
                'ダッシュボードの左上に表示される未回答数です。<br>選択している期間内で、チャットボットが回答できなかった質問の数を示します。<br/>これをクリックすると、未回答の質問一覧画面に遷移します。（同じ期間で絞り込まれます）',
              side: 'right'
            }
          },
          {
            element: '[data-tour="dashboard-statistics-unanswered-reasons"]',
            popover: {
              title: '未回答の原因',
              description:
                '未回答の質問の原因を分析するための情報です。<br>これにより、チャットボットの改善点を特定できます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="dashboard-statistics-daily-response-rate"]',
            popover: {
              title: '全体の割合',
              description:
                'このグラフは、選択した期間内の全体の回答率を示します。<br>未回答数と全質問数の割合を視覚化しています。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="dashboard-statistics-survey-reports"]',
            popover: {
              title: 'アンケートの割合',
              description:
                'このグラフは、選択した期間内のアンケート選択肢の回答率を示します。<br>アンケートの結果を視覚化し、ユーザーのフィードバックを分析できます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="dashboard-sessions-chart"]',
            popover: {
              title: '会話セッション数',
              // bar chart
              // This chart shows the number of conversation sessions over time
              description:
                'このグラフは、選択した期間内の会話セッション数を示します。<br>会話セッションは、ユーザーがチャットボットとやり取りした回数を表します。<br>これにより、ユーザーの利用状況を把握できます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="dashboard-response-rate-chart"]',
            popover: {
              title: '回答率 (回答数 / 全質問数)',
              // bar chart
              // This chart shows the response rate over time
              description:
                'このグラフは、選択した期間内の回答率を示します。<br>回答率は、チャットボットが質問に対してどれだけ回答できたかを示す指標です。<br>高い回答率は、チャットボットの性能が良好であることを示します。',
              side: 'top'
            }
          },
          {
            popover: {
              title: 'ダッシュボードのツアー完了！',
              description:
                'ダッシュボードの基本的な機能の紹介は以上です。<br><br>ダッシュボードを活用して、システムの利用状況や統計情報を把握し、チャットボットの改善に役立ててください。<br><br>各カードをクリックすると、詳細な情報にアクセスできます。'
            }
          }
        ]
      })

      this.driverObj?.drive()
    },
    async showSurveysTour() {
      this.originalRoute = route.fullPath

      // Add demo data first
      const reportsStore = useReportsStore()

      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: '回答後のアンケートの紹介',
              description:
                'このガイドでは、回答後のアンケート画面の基本的な機能と使い方をご紹介します。<br>ユーザーがチャットボットの回答に対して行ったアンケート結果を確認できます。'
            }
          },
          {
            element:
              '[data-tour="statistics-nav"] [type="tenantId-env-statistics-surveys"]',
            popover: {
              title: '回答後のアンケート',
              description: 'ここから回答後のアンケート画面にアクセスできます。',
              side: 'right',
              onNextClick: async () => {
                await this.quickNavigateTo('statistics/surveys?tour=surveys')
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },

          {
            element: '[data-tour="surveys-navbar"]',
            popover: {
              title: 'アンケート結果の概要',
              description:
                'ページ上部にアンケート結果の総数が表示されます。<br>選択した期間内のアンケート回答数を確認できます。',
              side: 'bottom',
              onPopoverRender() {
                reportsStore.addSurveyDemoData()
              }
            }
          },
          {
            element: '[data-tour="surveys-date-range-picker"]',
            popover: {
              title: '期間選択',
              description:
                'アンケート結果を表示する期間を選択できます。<br>特定の期間のアンケート結果に絞り込んで分析することができます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="surveys-score-filter"]',
            popover: {
              title: '評価フィルター',
              description:
                'アンケートの評価（スコア）でフィルタリングできます。<br>特定の評価を選択したユーザーの回答のみを表示することができます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="surveys-refresh-button"]',
            popover: {
              title: 'データ更新',
              description:
                'このボタンをクリックして、最新のアンケート結果を取得できます。<br>リアルタイムでデータを更新したい場合に使用します。',
              side: 'left'
            }
          },
          {
            element: '[data-tour="surveys-table"]',
            popover: {
              title: 'アンケート結果一覧',
              description:
                'アンケート結果の詳細情報が表形式で表示されます。<br>セッションID、質問、回答、選択肢、回答日時などの情報を確認できます。<br><br>各行の左側にあるチェックボックスで複数の項目を選択できます。',
              side: 'top'
            }
          },
          {
            element:
              '[data-tour="surveys-table"] tbody tr:first-child td:first-child input[type="checkbox"]',
            popover: {
              title: 'チェックボックス選択',
              description:
                '各行のチェックボックスをクリックして、複数のアンケート結果を選択できます。<br>選択した項目に対して一括操作を実行できます。<br><br>実際にクリックしてみてください。',
              side: 'right',
              onNextClick: () => {
                // Simulate checkbox click
                const checkbox = document.querySelector(
                  '[data-tour="surveys-table"] tbody tr:first-child td:first-child input[type="checkbox"]'
                ) as HTMLInputElement
                if (checkbox) {
                  checkbox.click()
                }
                this.driverObj?.moveNext()
              }
            }
          },
          {
            element:
              '[data-tour="surveys-table"] tbody tr:nth-child(2) td:first-child input[type="checkbox"]',
            popover: {
              title: '複数選択のデモ',
              description:
                '複数の項目を選択することで、一括操作が可能になります。<br>もう一つの項目も選択してみましょう。',
              side: 'right',
              onNextClick: () => {
                // Simulate second checkbox click
                const checkbox = document.querySelector(
                  '[data-tour="surveys-table"] tbody tr:nth-child(2) td:first-child input[type="checkbox"]'
                ) as HTMLInputElement
                if (checkbox) {
                  checkbox.click()
                }
                this.driverObj?.moveNext()
              }
            }
          },
          {
            element: '[data-tour="surveys-bulk-actions"]',
            popover: {
              title: '一括操作メニュー',
              description:
                '複数の項目を選択すると、この一括操作メニューが表示されます。<br>選択した項目をまとめてCSV出力することができます。<br><br>実際にクリックしてCSV出力を試してみましょう。',
              side: 'left',
              onNextClick: () => {
                // Wait a moment for the bulk actions to appear
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 500)
              }
            }
          },
          {
            element:
              '[data-tour="surveys-table"] tbody tr:first-child td:last-child button',
            popover: {
              title: '個別操作メニュー',
              description:
                '各行の右端にある操作メニューから、個別の項目に対してCSV出力などの操作を実行できます。<br>特定の1件だけを出力したい場合に便利です。',
              side: 'left',
              onPopoverRender() {
                // Simulate row hover to show the menu
                const row = document.querySelector(
                  '[data-tour="surveys-table"] tbody tr:first-child'
                ) as HTMLTableRowElement
                if (row) {
                  row.dispatchEvent(new MouseEvent('mouseenter'))
                }
              }
            }
          },
          {
            popover: {
              title: '回答後のアンケートツアー完了！',
              description:
                '回答後のアンケート画面の基本的な機能の紹介は以上です。<br><br>この画面を活用して、ユーザーのフィードバックを分析し、チャットボットの改善に役立ててください。<br><br>・複数選択によるCSV一括出力<br>・個別行からのCSV出力<br>・期間やスコアでのフィルタリング<br><br>これらの機能を活用してアンケート結果を効率的に分析できます。'
            }
          }
        ]
      })

      setTimeout(() => {
        this.driverObj?.drive()
      }, 100)
    },
    async showUnansweredTour() {
      this.originalRoute = route.fullPath

      // Add demo data first
      const reportsStore = useReportsStore()

      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: '未回答の質問の紹介',
              description:
                'このガイドでは、未回答の質問画面の基本的な機能と使い方をご紹介します。<br>AIが回答できなかった質問を確認し、分析することができます。'
            }
          },
          {
            element:
              '[data-tour="statistics-nav"] [type="tenantId-env-statistics-unanswered"]',
            popover: {
              title: '未回答の質問',
              description: 'ここから未回答の質問画面にアクセスできます。',
              side: 'right',
              onNextClick: async () => {
                await this.quickNavigateTo(
                  'statistics/unanswered-questions?tour=unanswered'
                )
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },

          {
            element: '[data-tour="unanswered-navbar"]',
            popover: {
              title: '未回答質問の概要',
              description:
                'ページ上部に未回答質問の総数が表示されます。<br>選択した期間内の未回答質問数を確認できます。',
              side: 'bottom',
              onPopoverRender() {
                reportsStore.addUnansweredDemoData()
              }
            }
          },
          {
            element: '[data-tour="unanswered-date-range"]',
            popover: {
              title: '期間選択',
              description:
                '表示する期間を指定できます。<br>デフォルトでは過去7日間のデータが表示されます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="unanswered-advanced-search"]',
            popover: {
              title: '高度な検索',
              description:
                '詳細な検索条件を指定できます。<br>クリックして検索モーダルを開いてみましょう。',
              side: 'bottom',
              onNextClick: () => {
                reportsStore.showAdvancedSearch = true
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 300)
              }
            }
          },
          {
            element:
              '[data-tour="unanswered-advanced-search-modal"] [data-headlessui-state="open"]',
            popover: {
              title: '高度な検索モーダル',
              description:
                '詳細な検索条件を設定できるモーダルです。<br>各フィールドを確認してみましょう。',
              side: 'left'
            }
          },
          {
            element: '[data-tour="advanced-search-date-range"]',
            popover: {
              title: '日付範囲',
              description: '検索対象の日付範囲を指定できます。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="advanced-search-session-id"]',
            popover: {
              title: 'セッションID',
              description:
                '特定のセッションIDで検索できます。<br>UUID形式で入力してください。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="advanced-search-chat-id"]',
            popover: {
              title: 'チャットID',
              description:
                '特定のチャットIDで検索できます。<br>UUID形式で入力してください。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="advanced-search-context-type"]',
            popover: {
              title: 'ナレッジの種類',
              description:
                'ナレッジの種類（ナレッジ、ウェブ検索、以前の記録不明、対応ナレッジなし等）で絞り込めます。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="advanced-search-analyzed-action"]',
            popover: {
              title: 'RAGパターン',
              description:
                'RAGパターン（通常、RAG、まとめ、天気、キャッシュ等）で絞り込めます。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="advanced-search-processed"]',
            popover: {
              title: '正常処理済み',
              description: '処理状態（正常、失敗）で絞り込めます。',
              side: 'right'
            }
          },
          {
            element: '[data-tour="advanced-search-clear-button"]',
            popover: {
              title: '条件クリア',
              description: '設定した検索条件をクリアできます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="advanced-search-search-button"]',
            popover: {
              title: '検索実行',
              description:
                '設定した条件で検索を実行します。<br>モーダルを閉じて結果を確認しましょう。',
              side: 'top',
              onNextClick: () => {
                reportsStore.showAdvancedSearch = false
                reportsStore.unansweredReportFilter.session_id
                  = 'demo-session-id'
                this.driverObj?.moveNext()
              }
            }
          },
          {
            element: '[data-tour="unanswered-table"]',
            popover: {
              title: '未回答質問一覧',
              description:
                'AIが回答できなかった質問の詳細情報が表示されます。<br>各行をチェックして一括操作も可能です。',
              side: 'top'
            }
          },
          {
            element:
              '[data-tour="unanswered-table"] [data-tour="unanswered-ids"]',
            popover: {
              title: 'セッションID・チャットID',
              description:
                'セッションIDとチャットIDが表示されます。<br>ホバーするとコピーボタンが表示され、クリックでクリップボードにコピーできます。',
              side: 'top'
            }
          },
          {
            element:
              '[data-tour="unanswered-table"] tbody tr:first-child td:first-child input[type="checkbox"]',
            popover: {
              title: 'チェックボックス選択',
              description:
                '各行のチェックボックスをクリックして、複数の未回答質問を選択できます。<br>選択した項目に対して一括操作を実行できます。<br><br>実際にクリックしてみてください。',
              side: 'right',
              onNextClick: () => {
                // Simulate checkbox click
                const checkbox = document.querySelector(
                  '[data-tour="unanswered-table"] tbody tr:first-child td:first-child input[type="checkbox"]'
                ) as HTMLInputElement
                if (checkbox) {
                  checkbox.click()
                }
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 300)
              }
            }
          },
          {
            element:
              '[data-tour="unanswered-table"] tbody tr:nth-child(2) td:first-child input[type="checkbox"]',
            popover: {
              title: '複数選択のデモ',
              description:
                '複数の項目を選択することで、一括操作が可能になります。<br>もう一つの項目も選択してみましょう。',
              side: 'right',
              onNextClick: () => {
                // Simulate second checkbox click
                const checkbox = document.querySelector(
                  '[data-tour="unanswered-table"] tbody tr:nth-child(2) td:first-child input[type="checkbox"]'
                ) as HTMLInputElement
                if (checkbox) {
                  checkbox.click()
                }
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 300)
              }
            }
          },
          {
            element: '[data-tour="unanswered-bulk-actions"]',
            popover: {
              title: '一括操作メニュー',
              description:
                '複数の項目を選択すると、この一括操作メニューが表示されます。<br>選択した項目をまとめてCSV出力することができます。<br><br>実際にクリックしてCSV出力を試してみましょう。',
              side: 'left',
              onNextClick: () => {
                // Wait a moment for the bulk actions to appear
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 300)
              }
            }
          },
          {
            element: '[data-tour="unanswered-clear-search"]',
            popover: {
              title: '検索条件クリア',
              description:
                '設定した検索条件をクリアして、全てのデータを表示できます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="unanswered-refresh"]',
            popover: {
              title: 'データ更新',
              description: '最新のデータを取得して画面を更新できます。',
              side: 'bottom'
            }
          },
          {
            popover: {
              title: '未回答の質問ツアー完了！',
              description:
                '未回答の質問画面の基本的な機能の紹介は以上です。<br><br>この画面を活用して、AIが回答できなかった質問を分析し、チャットボットの改善に役立ててください。<br><br>・高度な検索による絞り込み<br>・一括操作によるCSV出力<br>・個別行からのCSV出力<br><br>これらの機能を活用して未回答の質問を効率的に分析できます。'
            }
          }
        ]
      })

      this.driverObj?.drive()
    },
    showLogsTour() {
      const logsStore = useLogsStore()
      const router = useRouter()
      const { selectedTenantId, selectedEnvId } = useApp()

      // Add demo data
      logsStore.addLogsDemoData()

      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          // Navigation to logs page
          {
            popover: {
              title: 'ログ情報ツアー',
              description:
                'ログ情報画面の使い方をご案内します。<br>まずはログ情報メニューに移動しましょう。',
              side: 'bottom',
              onNextClick: async () => {
                await router.push(
                  `/${selectedTenantId.value}/${selectedEnvId.value}/logs?tour=true`
                )
                await nextTick()
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 500)
              }
            }
          },
          {
            element: '[data-tour="statistics-nav"] [type="tenantId-env-logs"]',
            popover: {
              title: 'ログ情報',
              description: 'ここからログ情報の質問画面にアクセスできます。',
              side: 'right',
              onNextClick: async () => {
                await this.quickNavigateTo('logs?tour=logs')
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          // Page overview
          {
            element: '[data-tour="logs-navbar"]',
            popover: {
              title: 'ログ情報画面',
              description:
                'ここではAIチャットボットとユーザーの会話ログを確認できます。<br>タイトル横の数字は現在表示されているログの総数を示しています。',
              side: 'bottom'
            }
          },
          // Mode tabs explanation
          {
            element: '[data-tour="logs-mode-tabs"]',
            popover: {
              title: '表示モード切り替え',
              description:
                '2つの表示モードがあります：<br><br><strong>メッセージ単位</strong>：個別のメッセージごとに表示<br><strong>セッション単位</strong>：会話セッションごとにまとめて表示<br><br>用途に応じて切り替えてください。',
              side: 'bottom'
            }
          },
          // Date range filter
          {
            element: '[data-tour="logs-date-range"]',
            popover: {
              title: '期間フィルタ',
              description:
                '表示するログの期間を指定できます。<br>デフォルトでは過去3日間のログが表示されます。<br>期間を変更すると自動的にログが再読み込みされます。',
              side: 'bottom'
            }
          },
          // Keyword filter
          {
            element: '[data-tour="logs-keyword-filter"]',
            popover: {
              title: 'キーワードフィルタ',
              description:
                'ログの内容をキーワードで絞り込むことができます。<br>質問文、回答文の両方から検索されます。<br>リアルタイムで結果が更新されます。',
              side: 'top'
            }
          },
          // Advanced search button
          {
            element: '[data-tour="logs-advanced-search"]',
            popover: {
              title: '高度な検索',
              description:
                'より詳細な条件でログを検索できます。<br>セッションID、リクエストID、カテゴリなど様々な条件で絞り込み可能です。<br><br>実際に開いてみましょう。',
              side: 'left',
              onNextClick: () => {
                // Open advanced search modal
                logsStore.showAdvancedSearch = true
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 300)
              }
            }
          },
          // Advanced search modal overview
          {
            element:
              '[data-tour="logs-advanced-search-modal"] [data-headlessui-state="open"]',
            popover: {
              title: '高度な検索モーダル',
              description:
                'ここでは詳細な検索条件を設定できます。<br>複数の条件を組み合わせて、より精密なログ検索が可能です。<br><br>各項目について順番に説明していきます。',
              side: 'bottom'
            }
          },
          // Advanced search modal - Date Range
          {
            element: '[data-tour="advanced-search-date-range"]',
            popover: {
              title: '日付範囲検索',
              description:
                'ログを検索する日付範囲を指定できます。<br>デフォルトでは過去3日間が設定されています。<br>カレンダーから開始日と終了日を選択してください。',
              side: 'right'
            }
          },
          // Advanced search modal - Session ID
          {
            element: '[data-tour="advanced-search-session-id"]',
            popover: {
              title: 'セッションID検索',
              description:
                '特定のセッションIDでログを検索できます。<br>UUID形式での入力が必要です。<br>セッション全体の会話履歴を確認したい場合に便利です。',
              side: 'right'
            }
          },
          // Advanced search modal - Request ID
          {
            element: '[data-tour="advanced-search-request-id"]',
            popover: {
              title: 'リクエストID検索',
              description:
                '特定のリクエストIDでログを検索できます。<br>個別のメッセージを特定したい場合に使用します。',
              side: 'right'
            }
          },
          // Advanced search modal - Category
          {
            element: '[data-tour="advanced-search-category"]',
            popover: {
              title: 'カテゴリ検索',
              description:
                'ログをカテゴリ別に絞り込むことができます。<br>設定されているカテゴリから選択してください。',
              side: 'right'
            }
          },
          // Advanced search modal - Query
          {
            element: '[data-tour="advanced-search-query"]',
            popover: {
              title: '質問文検索',
              description:
                'ユーザーの質問文に含まれる特定の文字列で検索できます。<br>部分一致で検索されます。',
              side: 'right'
            }
          },
          // Advanced search modal - Answer
          {
            element: '[data-tour="advanced-search-answer"]',
            popover: {
              title: '回答文検索',
              description:
                'AIの回答文に含まれる特定の文字列で検索できます。<br>回答内容の確認や分析に便利です。',
              side: 'right'
            }
          },
          // Advanced search modal - Context Type
          {
            element: '[data-tour="advanced-search-context-type"]',
            popover: {
              title: 'コンテキスト',
              description:
                '回答生成時に使用されたコンテキストの種類で絞り込めます：<br><br><strong>ナレッジ</strong>：ナレッジベースから回答<br><strong>ウエブ検索</strong>：ウェブ検索から回答<br><strong>見つからない</strong>：該当なし<br><strong>不明</strong>：確定できない',
              side: 'right'
            }
          },
          // Advanced search modal - Analyzed Action
          {
            element: '[data-tour="advanced-search-analyzed-action"]',
            popover: {
              title: '分析アクション',
              description:
                'システムが実行した分析アクションの種類で絞り込めます。',
              side: 'right'
            }
          },
          // Advanced search modal - Processed
          {
            element: '[data-tour="advanced-search-processed"]',
            popover: {
              title: '処理状態',
              description:
                'ログの処理状態で絞り込むことができます：<br><br><strong>正常</strong>：正常に処理されたログ<br><strong>失敗</strong>：エラーが発生したログ',
              side: 'right'
            }
          },
          // Close advanced search modal
          {
            popover: {
              title: '高度な検索の完了',
              description:
                '高度な検索の各項目について説明しました。<br>検索条件を設定したら「検索」ボタンで実行できます。<br><br>モーダルを閉じて次に進みましょう。',
              side: 'bottom',
              onNextClick: () => {
                // Close modal by clicking outside or close button
                logsStore.showAdvancedSearch = false
                logsStore.logsSubFilter.session_id = 'demo-session-id'
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 300)
              }
            }
          },
          // Select all checkbox
          {
            element: '[data-tour="logs-select-all"]',
            popover: {
              title: '全選択チェックボックス',
              description:
                '現在のページの全てのログを一括で選択/選択解除できます。<br>大量のログを処理する際に便利です。<br><br>実際にクリックしてみましょう。',
              side: 'right',
              onNextClick: () => {
                // Click select all checkbox
                const checkbox = document.querySelector(
                  '[data-tour="logs-select-all"] input[type="checkbox"]'
                ) as HTMLInputElement
                if (checkbox) {
                  checkbox.click()
                }
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 300)
              }
            }
          },
          // Logs list
          {
            element: '[data-tour="logs-list"]',
            popover: {
              title: 'ログ一覧',
              description:
                'ログがカード形式で表示されます。<br>各カードにはセッションID、質問文、回答文、トークン情報などが含まれます。<br>カードをクリックすると詳細が右パネルに表示されます。',
              side: 'right'
            }
          },
          // Individual log selection
          {
            element:
              '[data-tour="logs-list"] .log-card:first-child .log-checkbox',
            popover: {
              title: '個別ログ選択',
              description:
                '各ログカードにマウスを乗せると選択チェックボックスが表示されます。<br>個別にログを選択して一括操作を実行できます。<br><br>チェックボックスをクリックしてみましょう。',
              side: 'left',
              onNextClick: () => {
                // Simulate hover and click on first log checkbox
                const firstCard = document.querySelector(
                  '[data-tour="logs-list"] .log-card:first-child'
                )
                if (firstCard) {
                  // Trigger hover effect
                  firstCard.dispatchEvent(
                    new MouseEvent('mouseenter', { bubbles: true })
                  )
                  setTimeout(() => {
                    const checkbox = firstCard.querySelector(
                      '.log-checkbox input[type="checkbox"]'
                    ) as HTMLInputElement
                    if (checkbox) {
                      checkbox.click()
                    }
                    setTimeout(() => {
                      this.driverObj?.moveNext()
                    }, 300)
                  }, 200)
                }
              }
            }
          },
          // Export dropdown
          {
            element: '[data-tour="logs-export-dropdown"]',
            popover: {
              title: 'エクスポート機能',
              description:
                'ログをCSV形式でエクスポートできます。<br>3つのモードがあります：<br><br><strong>全てのログ</strong>：検索条件に一致する全ログ<br><strong>ページ内のログ</strong>：現在表示中のログのみ<br><strong>選択中のログ</strong>：チェックした特定のログのみ',
              side: 'left'
            }
          },
          // Clear search
          {
            element: '[data-tour="logs-clear-search"]',
            popover: {
              title: '検索条件クリア',
              description:
                '設定した高度な検索条件をすべてクリアできます。<br>初期状態に戻したい場合に使用してください。',
              side: 'bottom'
            }
          },
          // Log detail click
          {
            element: '[data-tour="logs-list"] .log-card:first-child',
            popover: {
              title: 'ログ詳細表示',
              description:
                'ログカードをクリックすると、右パネルに詳細情報が表示されます。<br>会話の全体像などを確認できます。<br><br>実際にクリックしてみましょう。',
              side: 'left',
              onNextClick: () => {
                // Click on first log card
                const firstCard = document.querySelector(
                  '[data-tour="logs-list"] .log-card:first-child'
                ) as HTMLElement
                if (firstCard) {
                  firstCard.click()
                }
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 500)
              }
            }
          },
          // Log detail panel introduction
          {
            element: '[data-tour="logs-detail-panel"]',
            popover: {
              title: 'ログ詳細パネル',
              description:
                '右パネルにログの詳細情報が表示されています。<br><br>• ユーザーの質問と回答が会話形式で表示<br>• 分析アクションやコンテキストタイプ<br>• セッションモードでは関連する全ての会話<br><br>ログの詳細分析に活用してください。',
              side: 'left'
            }
          },

          // Tour completion
          {
            popover: {
              title: 'ログ情報ツアー完了！',
              description:
                'ログ情報画面の使い方について説明しました。<br><br>主な機能：<br>• 期間・キーワードでの絞り込み<br>• 高度な検索条件での詳細検索<br>• メッセージ単位・セッション単位の表示切り替え<br>• ログの一括選択とCSVエクスポート<br>• 詳細情報の確認<br><br>これらの機能を活用してログ分析を行ってください！',
              side: 'bottom',
              onNextClick: () => {
                // Clean up demo data
                logsStore.removeLogsDemoData()
                this.driverObj?.destroy()
              }
            }
          }
        ]
      })

      this.driverObj?.drive()
    },
    async showDatasourceListTour() {
      this.originalRoute = route.fullPath

      // Add demo data first
      const trainingDatasStore = useTrainingDatasStore()

      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: 'データソース管理の紹介',
              description:
                'このガイドでは、データソース管理画面の基本的な機能と使い方をご紹介します。<br>チャットボットのナレッジベースとなるデータソースを管理できます。'
            }
          },
          {
            element:
              '[data-tour="training-data-nav"] [type="tenantId-env-training-data"]',
            popover: {
              title: 'データソース',
              description: 'ここからデータソース管理画面にアクセスできます。',
              side: 'right',
              onNextClick: async () => {
                await this.quickNavigateTo('training-data?tour=datasource')
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[data-tour="datasource-navbar"]',
            popover: {
              title: 'データソース一覧',
              description:
                'ページ上部にデータソースの総数が表示されます。<br>現在登録されているデータソースの数を確認できます。',
              side: 'bottom',
              onPopoverRender() {
                trainingDatasStore.addDatasourceDemoData()
              }
            }
          },
          {
            element: '[data-tour="datasource-search"]',
            popover: {
              title: 'データソース検索',
              description:
                'データソース名で検索できます。<br>大量のデータソースから特定のものを素早く見つけることができます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="datasource-register-button"]',
            popover: {
              title: 'データソース登録',
              description:
                '新しいデータソースを登録できます。<br>ファイルアップロードやウェブサイト連携でナレッジを追加できます。',
              side: 'left'
            }
          },
          {
            element: '[data-tour="datasource-status-filter"]',
            popover: {
              title: 'ステータスフィルター',
              description:
                'データソースのステータス（有効/無効）で絞り込みができます。<br>特定の状態のデータソースのみを表示できます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="datasource-type-filter"]',
            popover: {
              title: 'ファイル種類フィルター',
              description:
                'ファイルの種類（PDF、Word、Excel、ウェブページなど）で絞り込みができます。<br>特定の形式のデータソースのみを表示できます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="datasource-web-search"]',
            popover: {
              title: 'ウェブ検索フィルター',
              description:
                'ウェブサイトのURLで検索できます。<br>特定のウェブサイトから取得したデータソースを見つけることができます。',
              side: 'bottom'
            }
          },
          {
            element: '[data-tour="datasource-refresh"]',
            popover: {
              title: 'データ更新',
              description:
                '最新のデータソース情報を取得して画面を更新できます。<br>他のユーザーが追加・変更したデータソースを確認したい場合に使用します。',
              side: 'left'
            }
          },
          {
            element: '[data-tour="datasource-table"]',
            popover: {
              title: 'データソース一覧テーブル',
              description:
                'データソースの詳細情報が表形式で表示されます。<br>データソース名、ファイル名/URL、登録日時、更新日時、ステータスなどを確認できます。',
              side: 'top'
            }
          },
          {
            element:
              '[data-tour="datasource-table"] tbody tr:first-child td:first-child',
            popover: {
              title: 'データソース名クリック',
              description:
                'データソース名をクリックすると、そのデータソースのナレッジ一覧画面に移動します。<br>データソース内の詳細なコンテンツを確認・編集できます。',
              side: 'right'
            }
          },
          {
            element:
              '[data-tour="datasource-table"] tbody tr:first-child [data-tour="datasource-status-toggle"]',
            popover: {
              title: 'ステータス切り替え',
              description:
                'ステータス列のトグルスイッチをクリックすると、データソースの有効/無効を素早く切り替えできます。<br>無効にするとチャットボットの回答に使用されなくなります。',
              side: 'left'
            }
          },
          {
            element:
              '[data-tour="datasource-table"] tbody tr:first-child [data-tour="datasource-menu"]',
            popover: {
              title: '追加メニュー',
              description:
                '各行の右端にある3点メニューから、以下の操作を実行できます：<br><br>'
                + '• <strong>ナレッジ編集</strong>：データソース内のナレッジコンテンツを編集<br>'
                + '• <strong>無効化/有効化</strong>：データソースの有効/無効状態を切り替え<br>'
                + '• <strong>再アップロード</strong>：ファイルの再アップロードやウェブサイトの再インポート<br>'
                + '• <strong>削除</strong>：データソースを完全に削除（復元不可）<br><br>'
                + 'メニューを開いて各機能を確認してみましょう。',
              side: 'left',
              onNextClick: () => {
                const router = useRouter()
                router.replace({
                  query: {
                    tour: 'datasource',
                    openMenu: 'false'
                  }
                })
                setTimeout(() => {
                  this.driverObj?.moveNext()
                }, 300)
              }
            }
          },
          {
            popover: {
              title: 'データソース管理ツアー完了！',
              description:
                'データソース管理画面の基本的な機能の紹介は以上です。<br><br>主な機能：<br>• データソースの検索・フィルタリング<br>• 新規データソース登録<br>• ステータスの切り替え<br>• ナレッジ編集・再アップロード・削除<br>• データソース詳細の確認<br><br>これらの機能を活用してチャットボットのナレッジベースを効率的に管理してください！',
              side: 'bottom',
              onNextClick: () => {
                // Clean up demo data
                trainingDatasStore.removeDatasourceDemoData()
                this.driverObj?.destroy()
              }
            }
          }
        ]
      })

      setTimeout(() => {
        this.driverObj?.drive()
      }, 100)
    },
    showDatasourceRegisterTour() {
      console.log('showDatasourceRegisterTour called')
    },
    showKnowledgeListTour() {
      console.log('showKnowledgeListTour called')
    },
    showKnowledgeDetailTour() {
      console.log('showKnowledgeDetailTour called')
    },
    showIndexHistoryTour() {
      console.log('showIndexHistoryTour called')
    },
    showConnectTour() {
      console.log('showConnectTour called')
    },
    async showChatbotSettingsTour() {
      this.originalRoute = route.fullPath

      await this.quickNavigateTo('settings/')

      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: 'チャットボット設定の紹介',
              description:
                'このガイドでは、チャットボットの設定方法をご紹介します。<br>チャットボットの基本設定や動作設定を行います。'
            }
          },
          {
            element: '#settings-chatbot-name',
            popover: {
              title: 'チャットボット名',
              description:
                'チャットボットの名前を設定します。ユーザーに親しみやすい名前を付けることができます。',
              side: 'top'
            }
          },
          {
            element: '#settings-chatbot-avatar',
            popover: {
              title: 'チャットボットアバター',
              description:
                'チャットボットのアバター画像のリンクを設定します。<br>ユーザーに視覚的なアイコンを提供することで、親しみやすさを向上させます。',
              side: 'top'
            }
          },
          {
            element: '#settings-chatbot-color',
            popover: {
              title: '一次色',
              description:
                'チャットボットの一次色を設定します。<br>この色は、チャットボットのUIに反映され、ブランドイメージを統一するのに役立ちます。',
              side: 'top'
            }
          },
          {
            element: '#settings-chatbot-supported-languages',
            popover: {
              title: '対応言語',
              description:
                'チャットボットが対応する言語を設定します。<br>複数の言語をサポートすることで、より多くのユーザーに利用してもらいやすくなります。',
              side: 'left'
            }
          },
          {
            // lastchild of li
            element: '#settings-chatbot-supported-languages ul li:last-child',
            popover: {
              title: '言語追加',
              description:
                '新しい言語を追加するには、ここをクリックします。<br>言語を追加することで、チャットボットの対応範囲を広げることができます。',
              side: 'left',
              onNextClick: async () => {
                const settingsStore = useSettingsStore()
                settingsStore.addSupportLanguageDemo()
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            // lastchild-1 of li
            element:
              '#settings-chatbot-supported-languages ul li:nth-last-child(2) button',
            popover: {
              title: '言語削除',
              description:
                '追加した言語を削除するには、ここをクリックします。<br>不要な言語を削除することで、チャットボットの設定を整理できます。',
              side: 'top',
              onNextClick: async () => {
                const settingsStore = useSettingsStore()
                settingsStore.removeSupportLanguageDemo()
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '#settings-chatbot-welcome-message',
            popover: {
              title: '開始時メッセージ',
              description:
                'チャットボットが起動時に表示するメッセージを設定します。<br>ユーザーに対して、チャットボットの機能や使い方を案内することができます。',
              side: 'left'
            }
          },
          {
            element: '#welcome-message-editor',
            popover: {
              title: '開始時メッセージエディタ',
              description:
                '開始時メッセージを編集するためのエディタです。<br>ここでメッセージの内容を自由にカスタマイズできます。',
              side: 'left'
            }
          },
          {
            element: '#welcome-message-editor ul li:last-child',
            popover: {
              title: '開始時メッセージのプレビュー',
              description:
                '開始時メッセージのプレビューを表示します。<br>実際にユーザーがどのようにメッセージを受け取るかを確認できます。',
              side: 'left',
              onNextClick: () => {
                const previewButton = document.querySelector(
                  '#welcome-message-editor ul li:last-child button'
                ) as HTMLButtonElement
                if (previewButton) {
                  previewButton.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '#welcome-message-editor',
            popover: {
              title: '開始時メッセージプレビュー',
              description:
                '開始時メッセージのプレビューを確認できます。<br>ここで実際の表示を確認し、必要に応じてメッセージを調整してください。',
              side: 'left'
            }
          },
          {
            popover: {
              title: 'チャットボットの設定のツアー完了！',
              description:
                'チャットボットの設定方法の紹介は以上です。<br><br>チャットボットの設定を行うことで、ユーザーに対してより良い体験を提供できます。<br><br>各項目を実際に設定して、チャットボットをカスタマイズしてみてください。'
            }
          }
        ]
      })

      setTimeout(() => {
        this.driverObj?.drive()
      }, 1000) // Delay to ensure the login form is fully rendered
    },
    async showErrorMessageSettingsTour() {
      this.originalRoute = route.fullPath
      const settingsStore = useSettingsStore()
      await this.quickNavigateTo('settings/error-messages')
      const settings_error_ui = await getElementBySelector(
        '[data-tour="error-message-settings"]'
      )
      if (settings_error_ui === null) {
        this.driverObj = driver({
          ...this.baseConfig,
          steps: [
            {
              popover: {
                title: 'エラーが発生しました',
                description: '恐れ入りますが、UIロードに失敗しました。'
              }
            }
          ]
        })
      } else {
        this.driverObj = driver({
          ...this.baseConfig,
          steps: [
            {
              popover: {
                title: 'エラーメッセージ設定の紹介',
                description:
                  'このガイドでは、エラーメッセージの設定方法をご紹介します。<br>エラーメッセージの基本設定や動作設定を行います。'
              }
            },
            {
              element: '[data-tour="error-message-settings"]',
              popover: {
                title: 'エラーメッセージ設定',
                description:
                  'ここエラーメッセージの新規作成、リセット、削除、編集などできます。',
                side: 'bottom'
              }
            },
            {
              element: '[data-tour="error_message_create"]',
              popover: {
                title: '新規エラーコード追加',
                description: 'ここにクリックして新規作成画面を表示します。',
                side: 'bottom',
                onNextClick: () => {
                  settingsStore.isNewErrorModalOpen = true
                  nextTick(async () => {
                    this.driverObj?.moveNext()
                  })
                }
              }
            },
            {
              element:
                '[data-tour="error-message-create-modal"] [data-headlessui-state="open"]',
              popover: {
                title: '新規エラーコード追加',
                description: 'この画面で新しくエラーメッセージを作成します。',
                side: 'left',
                onPrevClick: () => {
                  settingsStore.isNewErrorModalOpen = false
                  nextTick(async () => {
                    this.driverObj?.movePrevious()
                  })
                }
              }
            },
            {
              element: '[data-tour="error_code_list"] ',
              popover: {
                title: '新規エラーコードリスト',
                description: 'エラーコード一覧から使いたいコードを選択します。',
                side: 'left',
                onNextClick: () => {
                  const nextButton = document.querySelector(
                    '[data-tour="error-message-input"] ul li:first-child button'
                  ) as HTMLButtonElement
                  if (nextButton) {
                    nextButton.click()
                  }
                  nextTick(async () => {
                    this.driverObj?.moveNext()
                  })
                }
              }
            },
            {
              element: '[data-tour="error-message-input"]',
              popover: {
                title: 'エラーメッセージ内容入力',
                description:
                  '上のエラーコードが発生した時に、<br>ユーザーに表示するためのメッセージ内容をここで記入します。',
                side: 'left',
                onNextClick: () => {
                  const nextButton = document.querySelector(
                    '[data-tour="error-message-input"] ul li:last-child button'
                  ) as HTMLButtonElement
                  if (nextButton) {
                    nextButton.click()
                  }
                  nextTick(async () => {
                    this.driverObj?.moveNext()
                  })
                }
              }
            },
            {
              element: '[data-tour="error-message-input"]',
              popover: {
                title: 'エラーメッセージ内容プレビュー',
                description:
                  'ここに実際にユーザーが見る表示になるので、<br>入力したメッセージをここで再度確認することをお勧めします。',
                side: 'left',
                onPrevClick: () => {
                  const previewButton = document.querySelector(
                    '[data-tour="error-message-input"] ul li:first-child button'
                  ) as HTMLButtonElement
                  if (previewButton) {
                    previewButton.click()
                  }
                  nextTick(async () => {
                    this.driverObj?.movePrevious()
                  })
                }
              }
            },
            {
              element: '[data-tour="error-message-create"]',
              popover: {
                title: '新規エラーメッセージ保存',
                description:
                  '内容に誤りがないよう、確認終わってここをクリックすると、<br>。新しいエラーメッセージが追加されます。',
                side: 'left',
                onPrevClick: () => {
                  const previewButton = document.querySelector(
                    '[data-tour="error-message-input"] ul li:last-child button'
                  ) as HTMLButtonElement
                  if (previewButton) {
                    previewButton.click()
                  }
                  nextTick(async () => {
                    this.driverObj?.movePrevious()
                  })
                },
                onNextClick: () => {
                  settingsStore.isNewErrorModalOpen = false
                  nextTick(async () => {
                    this.driverObj?.moveNext()
                  })
                }
              }
            },
            {
              element: '[data-tour="settings-error-messages"]',
              popover: {
                title: '作成されたエラーメッセージ',
                description: 'ここに作成されたエラーメッセージが表示されます。',
                side: 'left',
                onPrevClick: () => {
                  settingsStore.isNewErrorModalOpen = true
                  nextTick(async () => {
                    this.driverObj?.movePrevious()
                  })
                }
              }
            },
            {
              element: '[data-tour="settings-error-messages"] div label',
              popover: {
                title: 'エラーコード',
                description:
                  'サーバーや何らかの不具合でエラーが起こる時があり、<br>不具合によってコードが異なります。',
                side: 'right'
              }
            },
            {
              element: '[data-tour="settings-error-messages"] div p',
              popover: {
                title: 'エラーコード',
                description: 'ここにそのエラーコードの説明が見えます。',
                side: 'right'
              }
            },
            {
              element: '[data-tour="error-message-editor"]',
              popover: {
                title: 'エラーメッセージ編集',
                description:
                  '編集したい文章があれば直接書き換えます。<br>上書きするだけで自動的に保存されますのでご注意ください。</br>',
                side: 'left',
                onNextClick: () => {
                  const nextButton = document.querySelector(
                    '[data-tour="error-message-editor"] ul li:last-child button'
                  ) as HTMLButtonElement
                  if (nextButton) {
                    nextButton.click()
                  }
                  nextTick(async () => {
                    this.driverObj?.moveNext()
                  })
                }
              }
            },
            {
              element: '[data-tour="error-message-editor"]',
              popover: {
                title: 'エラーメッセージのプレビュー',
                description:
                  '実際にユーザーがどのようにメッセージを受け取るかを確認できます。',
                side: 'top',
                onPrevClick: () => {
                  const previewButton = document.querySelector(
                    '[data-tour="error-message-editor"] ul li:first-child button'
                  ) as HTMLButtonElement
                  if (previewButton) {
                    previewButton.click()
                  }
                  nextTick(async () => {
                    this.driverObj?.movePrevious()
                  })
                }
              }
            },
            {
              element: '[data-tour="delete-error-message"]',
              popover: {
                title: 'エラーメッセージ削除',
                description: '削除する時はクリックします。',
                side: 'left'
              }
            },
            {
              element: '[data-tour="error_message_reset"]',
              popover: {
                title: 'エラーメッセージのリセット',
                description: '既存のエラーメッセージを初期化します。',
                side: 'bottom'
              }
            },
            {
              popover: {
                title: 'ツアー完了！',
                description:
                  'エラーメッセージの設定方法の紹介は以上です。<br>エラーメッセージを設定すると、ユーザーにとって親切でわかりやすい表示になります。'
              }
            }
          ]
        })
      }

      setTimeout(() => {
        this.driverObj?.drive()
      }, 1000) // Delay to ensure the login form is fully rendered
    },
    async showSurveySettingsTour() {
      this.originalRoute = route.fullPath

      await this.quickNavigateTo('settings/survey')
      const settings_survey_ui = await getElementBySelector(
        '[data-tour="survey_cast_number"]'
      )
      if (settings_survey_ui === null) {
        this.driverObj = driver({
          ...this.baseConfig,
          steps: [
            {
              popover: {
                title: 'エラーが発生しました',
                description: '恐れ入りますが、UIロードに失敗しました。'
              }
            }
          ]
        })
      } else {
        this.driverObj = driver({
          ...this.baseConfig,
          steps: [
            {
              element: '[data-tour="survey_cast_number"]',
              popover: {
                title: 'アンケート選択肢数',
                description:
                  'ユーザーに表示される評価アンケートの選択肢数を設定します。<br>選択した数に応じて、<br>ユーザーに表示されるアンケート評価の数が決まります。<br>たとえば「2」を選択した場合、<br>2つの評価項目がユーザーに提示されます。',
                side: 'bottom'
              }
            },
            {
              element: '[data-tour="survey_option_count"]',
              popover: {
                title: '各アンケート',
                description:
                  '編集したいアンケートをこの画面から選択してください。<br>なお、「アンケート選択肢数」で設定した数を超えてアンケートを作成することはできません。<br>たとえば選択肢数が「2」の場合、編集可能なのは2件まで（例：0番と1番）となりますので、ご注意ください。',
                side: 'bottom'
              }
            },
            {
              element: '[data-tour="survey_editor"]',
              popover: {
                title: 'アンケート編集',
                description:
                  'ここでは、選択したアンケートの内容を編集することができます。',
                side: 'top'
              }
            },
            {
              element: '[data-tour="survey_label"]',
              popover: {
                title: 'アンケートラベル',
                description:
                  'アンケートの名称やタイトルとして表示されるラベルを設定します。<br>このラベルはユーザーが実際に目にするため、内容をわかりやすく、目的が伝わるように設定しましょう。',
                side: 'top'
              }
            },
            {
              element: '[data-tour="survey_reply_message"]',
              popover: {
                title: '送信メッセージ',
                description:
                  'ここでは、<br>ユーザーが評価を選択した際に表示される返信メッセージを設定します。',
                side: 'top'
              }
            },
            {
              element: '[data-tour="survey_reply_message_editor"]',
              popover: {
                title: 'アンケートエデｲタ',
                description:
                  'ユーザーに直接届くメッセージとなるため、<br>内容や表現に誤りがないよう十分にご注意ください。',
                side: 'top',
                onNextClick: () => {
                  const previewButton = document.querySelector(
                    '[data-tour="survey_reply_message_editor"] div ul li:last-child button'
                  ) as HTMLButtonElement
                  if (previewButton) {
                    previewButton.click()
                  }
                  nextTick(async () => {
                    this.driverObj?.moveNext()
                  })
                }
              }
            },
            {
              element: '[data-tour="survey_reply_message_editor"]',
              popover: {
                title: 'アンケートプレビュー',
                description:
                  'この画面では、エディタで作成したメッセージがユーザーにどのように表示されるかをリアルタイムで確認できます。<br>実際の表示イメージを見ながら、内容や表現を調整しましょう。',
                side: 'top',
                onPrevClick: () => {
                  const previewButton = document.querySelector(
                    '[data-tour="survey_reply_message_editor"] div ul li:first-child button'
                  ) as HTMLButtonElement
                  if (previewButton) {
                    previewButton.click()
                  }
                  nextTick(async () => {
                    this.driverObj?.movePrevious()
                  })
                }
              }
            },
            {
              element: '[data-tour="survey_update"] button:nth-of-type(2)',
              popover: {
                title: 'アンケート更新',
                description:
                  '編集が完了したら、<br>こちらのボタンをクリックして変更内容を保存してください。',
                side: 'left'
              }
            },
            {
              element: '[data-tour="survey_update"] button:nth-of-type(1)',
              popover: {
                title: 'アンケート設定のリセット',
                description:
                  'ここを押すと、現在選択中のアンケート内容が初期状態に戻ります。<br>編集内容をすべてクリアして、<br>最初からやり直したい場合にご利用ください。',
                side: 'left'
              }
            },
            {
              element: '[data-tour="survey_delete"]',
              popover: {
                title: 'アンケート削除',
                description:
                  'このボタンをクリックすると、選択した項目を削除します。<br>操作を取り消すことはできませんので、ご注意ください。',
                side: 'left'
              }
            },
            {
              popover: {
                title: 'ご清聴ありがとうございます',
                description:
                  'このアンケートは、チャットボットのユーザーインターフェース（UI）に反映され、ブランドイメージの統一や一貫したユーザー体験の提供に役立ちます。<br>ご協力いただくことで、より魅力的で信頼感のあるサービス設計に繋がります。',
                side: 'left'
              }
            }
          ]
        })
      }

      setTimeout(() => {
        this.driverObj?.drive()
      }, 1000)
    },
    async showLabelManagementTour() {
      this.originalRoute = route.fullPath
      setTimeout(() => {
        this.addLabelDemoData()
      }, 1000)
      await this.quickNavigateTo('settings/labels')
      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: 'ラベルの紹介',
              description:
                'このガイドでは、ラベルの設定方法をご紹介します。<br>ラベルの基本設定や動作設定を行います。'
            }
          },
          {
            element: '[data-tour="label-table"]',
            popover: {
              title: 'ラベル一覧',
              description: 'ここで各ラベルの詳細情報が閲覧できます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="table"]',
            popover: {
              title: 'ラベル一覧',
              description:
                '各ラベルの詳細情報が表示されています。<br>編集や削除を行う場合は、左側の操作ボタンをクリックしてください。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="label-pagecount"]',
            popover: {
              title: 'データ表示件数',
              description: 'ここで、一覧に表示するデータの数を選べます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="label-pagination"]',
            popover: {
              title: 'ペジネーション',
              description:
                '表示されたページを選択すると、該当のページへ移動します。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="label-search"] input',
            popover: {
              title: '検索機能',
              description: 'ラベル名で検索する時に使用します。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="label-search"] button',
            popover: {
              title: 'リフレッシュボタン',
              description:
                'データをリロードする場合は、こちらをクリックしてください。',
              side: 'top',
              onNextClick: () => {
                const ab = document.querySelectorAll('[data-tour="dropdown"]')
                ab.forEach((button) => {
                  (button as HTMLElement).style.display = 'block'
                })
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[data-tour="label-create"]',
            popover: {
              title: 'ラベル追加',
              description: '新しく作成するには、こちらのボタンを押します',
              side: 'top',
              onNextClick: () => {
                const labelStore = useLabelsStore()
                labelStore.isFormOpen = true
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element:
              '[data-tour="label-modify-dialog"] [data-headlessui-state="open"]',
            popover: {
              title: 'ラベル追加',
              description: 'この画面で、ラベル作成に必要な情報を入力します。',
              side: 'left',
              onPrevClick: () => {
                const labelStore = useLabelsStore()
                labelStore.isFormOpen = false
                nextTick(async () => {
                  this.driverObj?.movePrevious()
                })
              }
            }
          },
          {
            element:
              '[data-tour="label-modify-dialog"] [data-headlessui-state="open"] input',
            popover: {
              title: 'ラベル名',
              description:
                'ラベルの名前をここに入力します。<br>後から見ても分かりやすいように、明確な名前を付けましょう。',
              side: 'left'
            }
          },
          {
            element:
              '[data-tour="label-modify-dialog"] [data-headlessui-state="open"] form button[type="button"]',
            popover: {
              title: 'キャンセル',
              description:
                'ここをクリック、または画面の外押したら新規追加はキャンセルされます。',
              side: 'bottom'
            }
          },
          {
            element:
              '[data-tour="label-modify-dialog"] [data-headlessui-state="open"] form button:not([type="button"])',
            popover: {
              title: '確定',
              description:
                'ラベル名が入力終わったらここをクリックして完了です。',
              side: 'bottom',
              onNextClick: () => {
                const labelStore = useLabelsStore()
                labelStore.isFormOpen = false
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[data-tour="label-table"] tbody tr:first-child',
            popover: {
              title: 'データ詳細',
              description: '新規追加されたデータは一番上の表示されます。',
              side: 'bottom',
              onPrevClick: () => {
                const labelStore = useLabelsStore()
                labelStore.isFormOpen = true
                nextTick(async () => {
                  this.driverObj?.movePrevious()
                })
              }
            }
          },
          {
            element: '[data-tour="label-table"] tbody tr:first-child .row-menu',
            popover: {
              title: 'ラベル編集・削除',
              description:
                '作成したラベルの編集・削除はこのボタンから行います。<br>通常はマウスを近づけると表示されますが、<br>わかりやすく常時表示しています。',
              side: 'top',
              onNextClick: () => {
                const ab = document.querySelector(
                  '[data-tour="label-table"] tbody tr:first-child .row-menu'
                ) as HTMLButtonElement
                if (ab) {
                  ab.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[role="menu"][data-headlessui-state="open"]',
            popover: {
              title: 'ラベル編集',
              description: '上にある「ラベル編集」を選択します。',
              side: 'top',
              onNextClick: () => {
                const ab = document.querySelector(
                  '[role="menu"][data-headlessui-state="open"] button'
                ) as HTMLButtonElement
                if (ab) {
                  ab.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element:
              '[data-tour="label-modify-dialog"] [data-headlessui-state="open"]',
            popover: {
              title: 'ラベル編集',
              description: 'ここでラベルを更新できます。',
              side: 'left',
              onPrevClick: () => {
                const labelStore = useLabelsStore()
                labelStore.isFormOpen = false
                nextTick(async () => {
                  this.driverObj?.movePrevious()
                })
              }
            }
          },
          {
            element:
              '[data-tour="label-modify-dialog"] [data-headlessui-state="open"] input',
            popover: {
              title: 'ラベル入力',
              description: 'ここにラベル名を入力してください。',
              side: 'left'
            }
          },
          {
            element:
              '[data-tour="label-modify-dialog"] [data-headlessui-state="open"] form button[type="button"]',
            popover: {
              title: 'キャンセル',
              description:
                'ここをクリック、<br>または画面の外押したら新規追加はキャンセルされます。',
              side: 'bottom'
            }
          },
          {
            element:
              '[data-tour="label-modify-dialog"] [data-headlessui-state="open"] form button:not([type="button"])',
            popover: {
              title: '確定',
              description: 'ここをクリックするとデータが更新されます。',
              side: 'bottom',
              onNextClick: () => {
                const labelStore = useLabelsStore()
                labelStore.isFormOpen = false
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[data-tour="label-table"] tbody tr:first-child .row-menu',
            popover: {
              title: 'ラベル編集・削除',
              description: '次は、データを削除してみましょう。',
              side: 'top',
              onNextClick: () => {
                const ab = document.querySelector(
                  '[data-tour="label-table"] tbody tr:first-child .row-menu'
                ) as HTMLButtonElement
                if (ab) {
                  ab.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[role="menu"][data-headlessui-state="open"]',
            popover: {
              title: 'ラベル削除',
              description: '下の「ラベル削除」を選択します。',
              side: 'top',
              onNextClick: () => {
                const ab = document.querySelectorAll(
                  '[role="menu"][data-headlessui-state="open"] button'
                )[1] as HTMLButtonElement
                if (ab) {
                  ab.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[id^="headlessui-dialog-panel-v-"]',
            popover: {
              title: 'ラベル削除',
              description:
                'データ削除前に、誤っていないか確認するための画面が表示されます。',
              side: 'top'
            }
          },
          {
            element: '[id^="headlessui-dialog-panel-v-"] button:nth-child(1)',
            popover: {
              title: 'ラベル削除',
              description: 'ここをクリックすると削除されます。',
              side: 'bottom',
              onNextClick: () => {
                const ab = document.querySelectorAll(
                  '[id^="headlessui-dialog-panel-v-"] button'
                )[2] as HTMLButtonElement
                if (ab) {
                  ab.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            popover: {
              title: 'ラベル紹介のツアー終了',
              description:
                'ツアーはこれで終わりです。<br>ご参加いただきありがとうございました！',
              onNextClick: () => {
                this.removeLabelDemoData()
              }
            }
          }
        ]
      })
      setTimeout(() => {
        this.driverObj?.drive()
      }, 1000)
    },
    async showCategoryManagementTour() {
      this.originalRoute = route.fullPath
      setTimeout(() => {
        this.addCategoryDemoData()
      }, 1000)
      await this.quickNavigateTo('settings/categories')
      this.driverObj = driver({
        ...this.baseConfig,
        steps: [
          {
            popover: {
              title: 'カテゴリの紹介',
              description:
                'このガイドでは、カテゴリの設定方法をご紹介します。<br>カテゴリの基本設定や動作設定を行います。'
            }
          },
          {
            element: '[data-tour="categories"]',
            popover: {
              title: 'カテゴリ画面',
              description: 'ここでカテゴリの情報と機能があります。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="categories"] table',
            popover: {
              title: 'カテゴリ一覧',
              description:
                '各カテゴリの詳細情報が表示されています。<br>編集や削除を行う場合は、左側の操作ボタンをクリックしてください。',
              side: 'left'
            }
          },
          {
            element: '[data-tour="categories-pagecount"]',
            popover: {
              title: 'データ表示件数',
              description: 'ここで、一覧に表示するデータの数を選べます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="categories-pagination"]',
            popover: {
              title: 'ペジネーション',
              description:
                '表示されたページを選択すると、該当のページへ移動します。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="categories"] input',
            popover: {
              title: '検索機能',
              description: 'カテゴリ名で検索できます。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="category-refresh"]',
            popover: {
              title: 'リフレッシュボタン',
              description:
                'データをリロードする場合は、こちらをクリックしてください。',
              side: 'top'
            }
          },
          {
            element: '[data-tour="category-create"]',
            popover: {
              title: 'カテゴリ新規作成',
              description: 'カテゴリ新規作成する時にここを押します。',
              side: 'top',
              onNextClick: () => {
                const categoriesStore = useCategoriesStore()
                categoriesStore.categoryForm = true
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element:
              '[data-tour="category-modify-dialog"] [data-headlessui-state="open"]',
            popover: {
              title: 'カテゴリ新規作成',
              description: 'この画面で新規作成は実施します。',
              side: 'top',
              onPrevClick: () => {
                const categoriesStore = useCategoriesStore()
                categoriesStore.categoryForm = false
                nextTick(async () => {
                  this.driverObj?.movePrevious()
                })
              },
              onNextClick: () => {
                const ab = document.querySelectorAll('[data-tour="dropdown"]')
                if (ab) {
                  ab.forEach((button) => {
                    (button as HTMLButtonElement).style.display = 'block'
                  })
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element:
              '[data-tour="category-modify-dialog"] [data-headlessui-state="open"] input',
            popover: {
              title: 'カテゴリ名',
              description: '作成したいカテゴリの名前をここで入力します。',
              side: 'left'
            }
          },
          {
            element:
              '[data-tour="category-modify-dialog"] [data-headlessui-state="open"] form button[type="button"',
            popover: {
              title: 'キャンセル',
              description:
                'ここを押すかこの画面を閉じたら、キャンセルされます。',
              side: 'bottom',
              onNextClick: () => {
                const ab = document.querySelectorAll('[data-tour="dropdown"]')
                ab.forEach((button) => {
                  (button as HTMLElement).style.display = 'block'
                })
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element:
              '[data-tour="category-modify-dialog"] [data-headlessui-state="open"] form button[type="submit"',
            popover: {
              title: '確定ボタン',
              description: 'ここを押すとカテゴリが作成されます。',
              side: 'bottom',
              onNextClick: () => {
                const categoriesStore = useCategoriesStore()
                categoriesStore.categoryForm = false
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[data-tour="categories"] tbody tr:nth-child(2) .row-menu',
            popover: {
              title: 'カテゴリ編集',
              description:
                '作成したカテゴリの編集・削除はこのボタンから行います。<br>通常はマウスを近づけると表示されますが、<br>わかりやすく常時表示しています。',
              side: 'top',
              onNextClick: () => {
                const a = document.querySelector(
                  '[data-tour="categories"] tbody tr:nth-child(2) .row-menu'
                ) as HTMLButtonElement
                if (a) {
                  a.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[role="menu"][data-headlessui-state="open"]',
            popover: {
              title: 'カテゴリ編集',
              description: '上にある「カテゴリ編集」を選択します。',
              side: 'left',
              onNextClick: () => {
                const a = document.querySelectorAll(
                  '[role="menu"][data-headlessui-state="open"] button'
                )[0] as HTMLButtonElement
                if (a) {
                  a.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element:
              '[data-tour="category-modify-dialog"] [data-headlessui-state="open"]',
            popover: {
              title: 'カテゴリ編集',
              description: 'ここで選択されたカテゴリを編集します。',
              side: 'left'
            }
          },
          {
            element:
              '[data-tour="category-modify-dialog"] [data-headlessui-state="open"] input',
            popover: {
              title: 'カテゴリ名',
              description: 'カテゴリ名を自由に変更してください。',
              side: 'left',
              onNextClick: () => {
                const categoriesStore = useCategoriesStore()
                categoriesStore.categoryForm = false
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[data-tour="categories"] tbody tr:nth-child(2) .row-menu',
            popover: {
              title: 'カテゴリ削除',
              description: 'カテゴリの右側のボタン押します。',
              side: 'top',
              onNextClick: () => {
                const a = document.querySelector(
                  '[data-tour="categories"] tbody tr:nth-child(2) .row-menu'
                ) as HTMLButtonElement
                if (a) {
                  a.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[role="menu"][data-headlessui-state="open"]',
            popover: {
              title: 'カテゴリ編集',
              description: '下にある「カテゴリ削除」を選択します。',
              side: 'left',
              onNextClick: () => {
                const a = document.querySelectorAll(
                  '[role="menu"][data-headlessui-state="open"] button'
                )[1] as HTMLButtonElement
                if (a) {
                  a.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            element: '[id^="headlessui-dialog-panel"]',
            popover: {
              title: 'カテゴリ削除',
              description:
                '削除確認のための画面が表示されます。<br>間違えなければ「削除」ボタン押して削除されます。',
              side: 'top',
              onNextClick: () => {
                const a = document.querySelector(
                  '[id^="headlessui-dialog-panel"] button:nth-child(2)'
                ) as HTMLButtonElement
                if (a) {
                  a.click()
                }
                nextTick(async () => {
                  this.driverObj?.moveNext()
                })
              }
            }
          },
          {
            popover: {
              title: 'カテゴリ紹介のツアー終了',
              description:
                'ツアーはこれで終わりです。<br>ご参加いただきありがとうございました！',
              onNextClick: () => {
                this.removeCategoryDemoData()
              }
            }
          }
        ]
      })
      setTimeout(() => {
        this.driverObj?.drive()
      }, 1000)
    },
    showUserManagementTour() {
      console.log('showUserManagementTour called')
    },
    showUserGroupManagementTour() {
      console.log('showUserGroupManagementTour called')
    },
    showPagePermissionManagementTour() {
      console.log('showPagePermissionManagementTour called')
    },
    showIpControlTour() {
      console.log('showIpControlTour called')
    },
    showDeployTour() {
      console.log('showDeployTour called')
    },
    showDeployHistoryTour() {
      console.log('showDeployHistoryTour called')
    }
  }
})

import { orderBy } from 'lodash'

export const useLabelsStore = defineStore('labelsStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    labels: [] as any[],
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    showLabelssPalette: false,
    labelsTableSort: {
      key: 'updated_at',
      direction: 'desc'
    }
  }),
  getters: {},
  actions: {
    async fetchLabels(tenant_id: string, env_id: string, force = false) {
      // check if labels are already fetched and tenant_id and env_id are same
      if (this.labels.length > 0 && !force) {
        if (this.labels.every(label => label.tenant_id === tenant_id && label.env_id === env_id)) {
          return this.labels
        }
      }
      try {
        this.loadings.fetchLabels = true
        this.errors.fetchLabels = null
        this.labels = []
        const response = await useAPI().adminService.get(
          `/v2/label/all/tenants/${tenant_id}/env/${env_id}`
        )
        this.labels = orderBy(response.data?.labels || [], ['updated_at'], ['desc'])
        return response.data?.labels
      } catch (error: any) {
        this.errors.fetchLabels = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchLabels = false
      }
    },
    async createLabel(tenant_id: string, env_id: string, payload: any) {
      try {
        this.loadings.createLabel = true
        this.errors = {}
        const response = await useAPI().adminService.post(
          `/v2/label/tenants/${tenant_id}/env/${env_id}`,
          payload
        )
        this.labels.unshift(response.data)
        return response.data
      } catch (error: any) {
        this.errors.createLabel = error?.response?.data || error
        return false
      } finally {
        this.loadings.createLabel = false
      }
    },
    async updateLabel(tenant_id: string, env_id: string, payload: any) {
      try {
        this.loadings.updateLabel = true
        this.errors = {}
        const response = await useAPI().adminService.put(
          `/v2/label/${payload?.key}/tenants/${tenant_id}/env/${env_id}`,
          {
            label: payload.label
          }
        )
        this.labels = this.labels.map((label) => {
          if (label.key === response.data.key) {
            return response.data
          }
          return label
        })
        return true
      } catch (error: any) {
        this.errors.updateLabel = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateLabel = false
      }
    },
    async deleteLabel(tenant_id: string, env_id: string, key: string) {
      try {
        this.loadings.deleteLabel = true
        this.errors.deleteLabel = null
        await useAPI().adminService.delete(
          `/v2/label/${key}/tenants/${tenant_id}/env/${env_id}`
        )
        this.labels = this.labels.filter(label => label.key !== key)
        return true
      } catch (error: any) {
        this.errors.deleteLabel = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteLabel = false
      }
    }
  }
})

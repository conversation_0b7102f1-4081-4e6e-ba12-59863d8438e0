import type { Chat } from '~/types'

export const useRagsStore = defineStore('ragsStore', {
  persist: {
    pick: [],
    storage: window?.localStorage
  },
  state: () => ({
    chats: [] as Chat[],
    session_id: undefined,
    helpful_flag: false,
    last_turn: false,
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    tenantTokens: {} as Record<string, any>,
    chatSettings: {} as Record<string, any>,
    llmRagChatbot: null as any
  }),
  getters: {},
  actions: {
    // async ragTest() {
    //   const { tenantId } = useOperator()
    //   try {
    //     this.loadings.fetchAllUsers = true
    //     this.errors.fetchAllUsers = null
    //     const url = tenantId.value ? `/v2/users/all/tenants/${tenantId.value}` : '/v2/users/all'
    //     const response = await useAPI().adminService.get(url)
    //     this.users = response.data?.users || []
    //     return true
    //   } catch (error: any) {
    //     this.errors.fetchAllUsers = error?.response?.data || error
    //     return false
    //   } finally {
    //     this.loadings.fetchAllUsers = false
    //   }
    // },
    async guestLogin() {
      const { selectedTenantId } = useApp()
      try {
        this.loadings.guestLogin = true
        this.errors.guestLogin = null
        const response = await useAPI().ragService.post('/login/guest', {
          tenant_id: selectedTenantId.value
        })
        this.tenantTokens[selectedTenantId.value] = response.data
        return true
      } catch (error: any) {
        this.errors.guestLogin = error?.response?.data || error
        return false
      } finally {
        this.loadings.guestLogin = false
      }
    },
    async ragTenantLogin() {
      const authStore = useAuthStore()
      const { selectedTenantId } = useApp()
      // check if tenant token is already available
      if (this.tenantTokens[selectedTenantId.value]) {
        this.embedChatbotForCurrentTenantAndEnv(
          this.tenantTokens[selectedTenantId.value]
        )
        return true
      }
      // if not operator, set auth token to tenant token
      if (!authStore.isOperator) {
        this.tenantTokens[selectedTenantId.value] = {
          token: authStore.token,
          refresh_token: authStore.refresh_token
        }
        return true
      }
      try {
        this.loadings.ragTenantLogin = true
        this.errors.ragTenantLogin = null
        const response = await useAPI().authService.post(
          '/login/tenants/' + selectedTenantId.value,
          {
            tenant_id: selectedTenantId.value
          }
        )
        this.tenantTokens[selectedTenantId.value] = response.data
        this.embedChatbotForCurrentTenantAndEnv(response.data)
        return response.data
      } catch (error: any) {
        this.errors.ragTenantLogin = error?.response?.data || error
        return false
      } finally {
        this.loadings.ragTenantLogin = false
      }
    },
    async refreshToken() {
      const { selectedTenantId } = useApp()
      try {
        this.loadings.refreshToken = true
        this.errors.refreshToken = null
        const response = await useAPI().ragService.post('/login/refresh', {})
        this.tenantTokens[selectedTenantId.value] = response.data
        return response.data
      } catch (error: any) {
        this.errors.refreshToken = error?.response?.data || error
        return false
      } finally {
        this.loadings.refreshToken = false
      }
    },
    async logout() {
      const { selectedTenantId } = useApp()
      this.tenantTokens[selectedTenantId.value] = null
    },
    async chatInit() {
      try {
        const { selectedEnvId } = useApp()
        this.loadings.chatInit = true
        this.errors.chatInit = null
        const response = await useAPI().ragService.get(
          '/v1/basicSettings/env/' + selectedEnvId.value
        )
        this.chatSettings = response.data
        this.chats = []
        this.chats.push({
          chat_id: '0',
          message: response.data?.basic?.welcome_message,
          type: 'ai'
        })
        return true
      } catch (error: any) {
        this.errors.chatInit = error?.response?.data || error
        return false
      } finally {
        this.loadings.chatInit = false
      }
    },
    async chat(query: string) {
      const { selectedEnvId } = useApp()
      try {
        this.loadings.chat = true
        this.errors.chat = null
        this.chats.push({
          chat_id: '0',
          message: query,
          type: 'human'
        })
        const response = await useAPI().ragService.post(
          '/v1/chatbot/test/env/' + selectedEnvId.value,
          {
            session_id: this.session_id,
            query
          }
        )
        if (response.data?.response) {
          const chat_id = this.chats.length + 1
          this.chats.push({
            chat_id: '' + chat_id,
            message: response.data?.response,
            type: 'ai',
            prompt_tokens: response.data?.prompt_tokens,
            completion_tokens: response.data?.completion_tokens,
            token_count: response.data?.token_count
          })
        }
        // this.session_id = response.data?.session_id
        // this.helpful_flag = response.data?.helpful_flag
        // this.last_turn = response.data?.last_turn
        return true
      } catch (error: any) {
        this.errors.chat = error?.response?.data || error
        return false
      } finally {
        this.loadings.chat = false
      }
    },

    embedChatbotForCurrentTenantAndEnv(payload?: any) {
      console.log('🚀 ~ embedChatbotForCurrentTenantAndEnv ~ payload:', payload)
      const { selectedTenantId, selectedEnvId } = useApp()
      this.llmRagChatbot?.reload({
        refresh_token: payload?.refresh_token,
        tenantId: selectedTenantId.value,
        envId: selectedEnvId.value,
        token: payload?.token
      })
    }
  }
})

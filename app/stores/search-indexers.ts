export const useSearchIndexersStore = defineStore('searchIndexersStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    indexerStatus: null as any,
    indexerUpdateHistories: [] as any[],
    indexerUpdateHistoriesPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    indexerUpdateHistoriesTotal: 0
  }),
  actions: {
    async runIndexer(tenant_id: string, env_id: string) {
      const trainingDatasStore = useTrainingDatasStore()
      try {
        this.loadings.runIndexer = true
        this.errors.runIndexer = null
        await useAPI().adminService.post(
          `/v2/searchIndexer/run/tenants/${tenant_id}/env/${env_id}`
        )
        this.indexerStatus = 2
        trainingDatasStore.hasAnyUnreflected = false
        return true
      } catch (error: any) {
        this.errors.runIndexer = error?.response?.data || error
        return false
      } finally {
        this.loadings.runIndexer = false
      }
    },

    async getIndexerStatus(tenant_id: string, env_id: string) {
      try {
        this.loadings.getIndexerStatus = true
        this.errors.getIndexerStatus = null
        const response = await useAPI().adminService.get(
          `/v2/searchIndexer/status/tenants/${tenant_id}/env/${env_id}`
        )
        this.indexerStatus = response?.data?.status
        return true
      } catch (error: any) {
        this.errors.getIndexerStatus = error?.response?.data || error
        return false
      } finally {
        this.loadings.getIndexerStatus = false
      }
    },

    async searchIndexerHistories(tenant_id: string, env_id: string) {
      try {
        this.loadings.searchIndexerHistories = true
        this.errors.searchIndexerHistories = null
        this.indexerUpdateHistories = []
        const response = await useAPI().adminService.get(
          `/v2/searchIndexer/histories/tenants/${tenant_id}/env/${env_id}`,
          {
            params: {
              page: this.indexerUpdateHistoriesPagination.page,
              page_size: this.indexerUpdateHistoriesPagination.pageCount,
              order: this.indexerUpdateHistoriesPagination.asc
            },
            paramsSerializer: {
              indexes: null
            }
          }
        )
        this.indexerUpdateHistories = response?.data?.histories || []
        // header: x-pagination: {"page": 1, "page_size": 3, "total_pages": 3}
        const pagination = tryParseJson(response?.headers['x-pagination'])

        this.indexerUpdateHistoriesTotal
          = pagination?.total_pages
            * this.indexerUpdateHistoriesPagination.pageCount

        return true
      } catch (error: any) {
        this.errors.searchIndexerHistories = error?.response?.data || error
        return false
      } finally {
        this.loadings.searchIndexerHistories = false
      }
    }
  }
})

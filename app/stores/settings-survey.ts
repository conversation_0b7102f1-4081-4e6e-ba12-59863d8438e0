import { cloneDeep, orderBy } from 'lodash'

export const useSettingsSurveyStore = defineStore('settingsSurveyStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    settingsSurveyOptions: [] as any[],
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    selectedSurveyOption: null as any
  }),
  getters: {
    getSurveyOptionByValue: state => (value: number) => {
      return state.settingsSurveyOptions.find(row => row.value === value)
    }
  },
  actions: {
    async fetchAllSettingsSurveyOptions(
      tenant_id: string,
      env_id: string,
      force = false
    ) {
      // check if settingsSurveyOptions are already fetched and tenant_id and env_id are same
      if (this.settingsSurveyOptions.length > 0 && !force) {
        if (
          this.settingsSurveyOptions.every(
            row => row.tenant_id === tenant_id && row.env_id === env_id
          )
        ) {
          return this.settingsSurveyOptions
        }
      }
      try {
        this.loadings.fetchAllSettingsSurveyOptions = true
        this.errors.fetchAllSettingsSurveyOptions = null
        this.settingsSurveyOptions = []

        // Use role-based API calls
        const { custom } = useRoleBasedApiCalls()
        const response = await custom({
          // For operators, use the all endpoint
          operator: '/v2/survey/optionSettings/all/tenants/{tenantId}/env/{envId}',
          // For admins and staff, use the tenant-specific endpoint
          admin: '/v2/survey/optionSettings/all/tenants/{tenantId}/env/{envId}',
          staff: '/v2/survey/optionSettings/all/tenants/{tenantId}/env/{envId}',
          default: '/v2/survey/optionSettings/all/tenants/{tenantId}/env/{envId}',
          params: {
            tenantId: tenant_id,
            envId: env_id
          },
          transform: (data) => {
            return data
          }
        }, 'get')

        this.settingsSurveyOptions = orderBy(
          response?.options || [],
          ['value'],
          ['asc']
        )
        this.selectedSurveyOption = cloneDeep(this.settingsSurveyOptions?.[0] || {})
        return response?.settingsSurveyOptions
      } catch (error: any) {
        console.log('🚀 ~ fetchAllSettingsSurveyOptions ~ error:', error)
        this.errors.fetchAllSettingsSurveyOptions
          = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchAllSettingsSurveyOptions = false
      }
    },
    async createSurveyOption(tenant_id: string, env_id: string, payload: any) {
      try {
        this.loadings.createSurveyOption = true
        this.errors = {}

        // Use role-based API calls
        const { custom } = useRoleBasedApiCalls()
        const response = await custom({
          operator: '/v2/survey/optionSettings/tenants/{tenantId}/env/{envId}',
          admin: '/v2/survey/optionSettings/tenants/{tenantId}/env/{envId}',
          staff: '/v2/survey/optionSettings/tenants/{tenantId}/env/{envId}/request',
          default: '/v2/survey/optionSettings/tenants/{tenantId}/env/{envId}',
          params: {
            tenantId: tenant_id,
            envId: env_id
          }
        }, 'post', payload)

        this.settingsSurveyOptions.unshift(response)
        this.settingsSurveyOptions = orderBy(
          this.settingsSurveyOptions || [],
          ['value'],
          ['asc']
        )
        return response
      } catch (error: any) {
        this.errors.createSurveyOption = error?.response?.data || error
        return false
      } finally {
        this.loadings.createSurveyOption = false
      }
    },
    async updateSurveyOption(tenant_id: string, env_id: string, payload: any) {
      try {
        this.loadings.updateSurveyOption = true
        this.errors = {}
        const value = payload.value

        // Use role-based API calls
        const { custom } = useRoleBasedApiCalls()
        const response = await custom({
          operator: '/v2/survey/optionSettings/{value}/tenants/{tenantId}/env/{envId}',
          admin: '/v2/survey/optionSettings/{value}/tenants/{tenantId}/env/{envId}',
          staff: '/v2/survey/optionSettings/{value}/tenants/{tenantId}/env/{envId}/request-update',
          default: '/v2/survey/optionSettings/{value}/tenants/{tenantId}/env/{envId}',
          params: {
            tenantId: tenant_id,
            envId: env_id,
            value
          }
        }, 'put', {
          text: payload.text,
          reply_message: payload.reply_message
        })

        this.settingsSurveyOptions = this.settingsSurveyOptions.map((row) => {
          if (row.value === response.value) {
            return response
          }
          return row
        })
        return true
      } catch (error: any) {
        this.errors.updateSurveyOption = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateSurveyOption = false
      }
    },
    async deleteSurveyOption(tenant_id: string, env_id: string, value: number) {
      try {
        this.loadings.deleteSurveyOption = true
        this.errors.deleteSurveyOption = null

        // Use role-based API calls
        const { custom } = useRoleBasedApiCalls()
        await custom({
          operator: '/v2/survey/optionSettings/{value}/tenants/{tenantId}/env/{envId}',
          admin: '/v2/survey/optionSettings/{value}/tenants/{tenantId}/env/{envId}',
          staff: '/v2/survey/optionSettings/{value}/tenants/{tenantId}/env/{envId}/request-deletion',
          default: '/v2/survey/optionSettings/{value}/tenants/{tenantId}/env/{envId}',
          params: {
            tenantId: tenant_id,
            envId: env_id,
            value
          }
        }, 'delete')

        this.settingsSurveyOptions = this.settingsSurveyOptions.filter(
          row => row.value !== value
        )
        return true
      } catch (error: any) {
        this.errors.deleteSurveyOption = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteSurveyOption = false
      }
    }
  }
})

import type { UserAccount, GetAllUserAccounts } from '~/types/user'
import type {
  UserGroup,
  UserGroupBase,
  GetAllUserGroups
} from '~/types/user-group'

export const useUserGroupsStore = defineStore('userGroupsStore', {
  persist: {
    pick: [],
    storage: window?.localStorage
  },
  state: () => ({
    // Store Groups
    userGroupsFilter: {} as Record<string, any>,
    userGroups: [] as UserGroup[],
    userGroupsPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    userGroupTotalCount: 0,
    selectedGroup: null as UserGroup | null,
    // Store Group Users
    userGroupUsers: [] as UserAccount[],
    usersNotInGroup: [] as UserAccount[],
    selectedRemoveUsers: [] as any[],
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>
  }),
  getters: {},
  actions: {
    async fetchUserGroups(tenant_id: string) {
      try {
        this.loadings.fetchUserGroups = true
        this.userGroups = []
        this.errors.fetchUserGroups = null
        this.selectedRemoveUsers = []
        const params = {
          page: this.userGroupsPagination.page,
          page_size: this.userGroupsPagination.pageCount,
          order: this.userGroupsPagination.asc
        } as Record<string, any>
        if (this.userGroupsFilter?.status?.length === 1) {
          params.enabled = this.userGroupsFilter?.status?.some(
            (status: any) => status?.value === 'enabled'
          )
        }
        if (this.userGroupsFilter?.name?.length > 0) {
          params.name = this.userGroupsFilter?.name
        }

        const response = await useAPI().adminService.get(
          `/v2/userGroups/all/tenants/${tenant_id}`,
          {
            params,
            paramsSerializer: {
              indexes: null
            }
          }
        )
        const responseData = response.data as GetAllUserGroups
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.userGroupTotalCount = pagination?.total_count
        this.userGroups = responseData.groups
        return true
      } catch (error: any) {
        this.errors.fetchUserGroups = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchUserGroups = false
      }
    },
    async fetchUserGroup(tenant_id: string, group_id: string) {
      try {
        this.loadings.fetchUserGroup = true
        this.userGroups = []
        this.errors.fetchUserGroup = null
        this.selectedRemoveUsers = []
        const response = await useAPI().adminService.get(
          `/v2/userGroups/${group_id}/tenants/${tenant_id}`
        )
        const responseData = response.data as UserGroup
        this.selectedGroup = responseData
        return true
      } catch (error: any) {
        this.errors.fetchUserGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchUserGroup = false
      }
    },
    async createUserGroup(tenant_id: string, payload: UserGroupBase) {
      try {
        this.loadings.createUserGroup = true
        this.errors.createUserGroup = null
        await useAPI().adminService.post(
          `/v2/userGroups/tenants/${tenant_id}`,
          payload
        )
        return true
      } catch (error: any) {
        this.errors.createUserGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.createUserGroup = false
      }
    },
    async updateUserGroup(
      tenant_id: string,
      group_id: string,
      payload: UserGroupBase
    ) {
      try {
        this.loadings.updateUserGroup = true
        this.errors.updateUserGroup = null
        const response = await useAPI().adminService.put(
          `/v2/userGroups/${group_id}/tenants/${tenant_id}`,
          payload
        )
        this.userGroups = this.userGroups.map(
          (userGroup) => {
            if (userGroup.id == group_id) {
              return {
                ...response.data
              }
            } else {
              return userGroup
            }
          }
        )
        return true
      } catch (error: any) {
        this.errors.updateUserGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateUserGroup = false
      }
    },
    async patchUserGroup(
      tenant_id: string,
      group_id: string,
      payload: Record<string, any>
    ) {
      try {
        this.loadings.updateUserGroup = true
        this.errors.updateUserGroup = null
        const response = await useAPI().adminService.patch(
          `/v2/userGroups/${group_id}/tenants/${tenant_id}`,
          payload
        )
        this.userGroups = this.userGroups.map(
          (userGroup) => {
            if (userGroup.id == group_id) {
              return {
                ...response.data
              }
            } else {
              return userGroup
            }
          }
        )
        return true
      } catch (error: any) {
        this.errors.updateUserGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateUserGroup = false
      }
    },
    async deleteUserGroup(tenant_id: string, group_id: string) {
      try {
        this.loadings.deleteUserGroup = true
        this.errors.deleteUserGroup = null
        await useAPI().adminService.delete(
          `/v2/userGroups/${group_id}/tenants/${tenant_id}`
        )
        this.userGroups = this.userGroups.filter(
          userGroup => userGroup.id != group_id
        )
        return true
      } catch (error: any) {
        this.errors.deleteUserGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteUserGroup = false
      }
    },
    async addUserToGroup(
      tenant_id: string,
      group_id: string,
      username: string
    ) {
      try {
        this.loadings.addUserToGroup = true
        this.errors.addUserToGroup = null
        await useAPI().adminService.post(
          `/v2/userGroups/${group_id}/users/add/${username}/tenants/${tenant_id}`
        )
        return true
      } catch (error: any) {
        this.errors.addUserToGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.addUserToGroup = false
      }
    },
    async addUsersToGroup(
      tenant_id: string,
      group_id: string,
      usernames: string[]
    ) {
      try {
        this.loadings.addUsersToGroup = true
        this.errors.addUsersToGroup = null
        const payload = {
          usernames: usernames
        }
        await useAPI().adminService.post(
          `/v2/userGroups/${group_id}/users/add/tenants/${tenant_id}`,
          payload
        )
        return true
      } catch (error: any) {
        this.errors.addUsersToGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.addUsersToGroup = false
      }
    },
    async fetchGroupUsers(tenant_id: string, group_id: string) {
      try {
        this.loadings.fetchGroupUsers = true
        this.errors.fetchGroupUsers = null
        this.userGroupUsers = []
        this.selectedRemoveUsers = []
        const response = await useAPI().adminService.get(
          `/v2/userGroups/${group_id}/users/tenants/${tenant_id}`
        )
        const responseData = response.data as GetAllUserAccounts
        this.userGroupUsers = responseData.users
        return true
      } catch (error: any) {
        this.errors.fetchGroupUsers = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchGroupUsers = false
      }
    },
    async fetchUsersNotInGroup(tenant_id: string, group_id: string) {
      try {
        this.loadings.fetchUsersNotInGroup = true
        this.errors.fetchUsersNotInGroup = null
        this.usersNotInGroup = []
        const response = await useAPI().adminService.get(
          `/v2/userGroups/${group_id}/users/not/tenants/${tenant_id}`
        )
        const responseData = response.data as GetAllUserAccounts
        this.usersNotInGroup = responseData.users
        return true
      } catch (error: any) {
        this.errors.fetchUsersNotInGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchUsersNotInGroup = false
      }
    },
    async removeUserFromGroup(
      tenant_id: string,
      group_id: string,
      username: string
    ) {
      try {
        this.loadings.removeUserFromGroup = true
        this.errors.removeUserFromGroup = null
        await useAPI().adminService.delete(
          `/v2/userGroups/${group_id}/users/${username}/tenants/${tenant_id}`
        )
        return true
      } catch (error: any) {
        this.errors.removeUserFromGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.removeUserFromGroup = false
      }
    },
    async removeUsersFromGroup(
      tenant_id: string,
      group_id: string,
      usernames: string[]
    ) {
      try {
        this.loadings.removeUsersFromGroup = true
        this.errors.removeUsersFromGroup = null
        const payload = {
          usernames: usernames
        }
        await useAPI().adminService.post(
          `/v2/userGroups/${group_id}/users/delete/tenants/${tenant_id}`,
          payload
        )
        return true
      } catch (error: any) {
        this.errors.removeUsersFromGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.removeUsersFromGroup = false
      }
    },
    async removeAllUsersFromGroup(
      tenant_id: string,
      group_id: string
    ) {
      try {
        this.loadings.removeAllUsersFromGroup = true
        this.errors.removeAllUsersFromGroup = null
        await useAPI().adminService.delete(
          `/v2/userGroups/${group_id}/users/all/tenants/${tenant_id}`
        )
        return true
      } catch (error: any) {
        this.errors.removeAllUsersFromGroup = error?.response?.data || error
        return false
      } finally {
        this.loadings.removeAllUsersFromGroup = false
      }
    }
  }
})

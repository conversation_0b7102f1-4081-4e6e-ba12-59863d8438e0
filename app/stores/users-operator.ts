export const useUsersOperatorStore = defineStore('usersOperatorStore', {
  persist: {
    pick: [],
    storage: window?.localStorage
  },
  state: () => ({
    operatorUsers: [] as any[],
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    operatorUsersPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    operatorUsersTotal: 0,
    operatorUsersFilter: {} as Record<string, any>,
    selectedOperatorUsers: [] as any[]
  }),
  getters: {},
  actions: {
    resetFilters() {
      this.operatorUsersFilter = {}
    },
    async fetchAllOperatorUsers() {
      try {
        this.loadings.fetchAllOperatorUsers = true
        this.errors.fetchAllOperatorUsers = null
        const url = '/v2/users/operators/all'

        const params = {
          page: this.operatorUsersPagination.page,
          page_size: this.operatorUsersPagination.pageCount,
          order: this.operatorUsersPagination.asc
        } as Record<string, any>

        if (this.operatorUsersFilter?.username) {
          params.username = this.operatorUsersFilter?.username
        }
        if (this.operatorUsersFilter?.status) {
          params.user_type = this.operatorUsersFilter?.status.value
        }

        const response = await useAPI().adminService.get(url, {
          params
        })

        this.operatorUsers = response?.data?.users || []
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.operatorUsersTotal = pagination?.total_count || 0
        return true
      } catch (error: any) {
        this.errors.fetchAllOperatorUsers = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchAllOperatorUsers = false
      }
    },
    async createOperatorUser(payload: any) {
      try {
        this.loadings.createOperatorUser = true
        this.errors = {}
        const url = '/v2/users/operator'
        const response = await useAPI().adminService.post(url, payload)
        this.operatorUsers.unshift(response.data as any)
        return response.data
      } catch (error: any) {
        this.errors.createOperatorUser = error?.response?.data || error
        return false
      } finally {
        this.loadings.createOperatorUser = false
      }
    },
    async deleteOperatorUser(username: string) {
      try {
        this.loadings.deleteOperatorUser = true
        this.errors.deleteOperatorUser = null
        const url = `/v2/users/operator/${username}`
        await useAPI().adminService.delete(url)
        this.operatorUsers = this.operatorUsers.filter(user => user.username !== username)
        return true
      } catch (error: any) {
        this.errors.deleteOperatorUser = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteOperatorUser = false
      }
    },
    async deleteManyOperatorUsers(usernames: string[]) {
      try {
        this.loadings.deleteManyOperatorUsers = true
        this.errors.deleteManyOperatorUsers = null
        const url = '/v2/users/operator/delete'
        const response = await useAPI().adminService.post(url, {
          usernames
        })
        if (response.data?.success?.length > 0) {
          this.operatorUsers = this.operatorUsers.filter(user => !usernames.includes(user.username))
          this.selectedOperatorUsers = []
          return true
        } else {
          this.errors.deleteManyOperatorUsers = response.data?.failure
          return false
        }
      } catch (error: any) {
        this.errors.deleteManyOperatorUsers = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteManyOperatorUsers = false
      }
    },
    async updateOperatorUser(username: string, payload: any) {
      try {
        this.loadings.updateOperatorUser = true
        this.errors = {}
        const url = `/v2/users/operator/${username}`
        const response = await useAPI().adminService.put(url, {
          email: payload.email,
          display_name: payload.display_name
        })
        this.operatorUsers = this.operatorUsers.map((user) => {
          if (user.username === response.data.username) {
            return response.data
          }
          return user
        })
        return response.data
      } catch (error: any) {
        this.errors.updateOperatorUser = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateOperatorUser = false
      }
    }
  }
})

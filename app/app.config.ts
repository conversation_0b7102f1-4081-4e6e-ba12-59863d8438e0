export default defineAppConfig({
  ui: {
    primary: 'sky',
    gray: 'cool',
    button: {
      default: {
        size: 'md'
      }
    },
    input: {
      default: {
        size: 'md'
      }
    },
    card: {
      rounded: 'rounded-xl'
    },
    footer: {
      top: {
        wrapper: 'border-t border-gray-200 dark:border-gray-800',
        container: 'py-8 lg:py-16'
      },
      bottom: {
        wrapper: 'border-t border-gray-200 dark:border-gray-800'
      }
    },
    page: {
      hero: {
        wrapper: 'lg:py-24'
      }
    },

    dashboard: {
      sidebar: {
        links: {
          active: 'before:bg-primary-100 dark:before:bg-primary-800 text-primary-900 dark:text-white'
        }
      }
    },

    table: {
      default: {
        emptyState: {
          icon: 'ix:box-open',
          label: 'データがありません'
        },
        loadingState: {
          label: '読み込み中',
          icon: 'eos-icons:loading'
        }
      }
    },

    formGroup: {
      help: 'text-xs text-gray-500 dark:text-gray-400'
    }
  }
})

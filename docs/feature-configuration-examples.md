# Feature Configuration Examples

## Environment Variable Examples

### Development Environment (.env.development)
```bash
# Development - All features in preview mode
FEATURES_IP_ADDRESS_CONTROL=false
FEATURES_FAQ_IMPORT_MODE=false
FEATURES_USER_PASSWORD_CHANGE=false
FEATURES_SUPPORT=false

# Or simply omit all feature flags (defaults to false)
```

### Staging Environment (.env.staging)
```bash
# Staging - Some features enabled for testing
FEATURES_IP_ADDRESS_CONTROL=true
FEATURES_FAQ_IMPORT_MODE=true
FEATURES_USER_PASSWORD_CHANGE=false
FEATURES_SUPPORT=true
```

### Production Environment (.env.production)
```bash
# Production - Stable features enabled
FEATURES_IP_ADDRESS_CONTROL=true
FEATURES_FAQ_IMPORT_MODE=false  # Still in preview
FEATURES_USER_PASSWORD_CHANGE=true
FEATURES_SUPPORT=true
```

## Component Implementation Examples

### 1. Simple Conditional Rendering
```vue
<template>
  <div>
    <!-- Always visible content -->
    <h1>Dashboard</h1>
    
    <!-- Feature-gated content -->
    <div v-if="canUseFeature('ipAddressControl')">
      <IPControlPanel :badge="previewBadge('ipAddressControl')" />
    </div>
  </div>
</template>

<script setup>
const { canUseFeature, previewBadge } = useFeatures()
</script>
```

### 2. Navigation Menu with Features
```vue
<template>
  <nav>
    <ul>
      <li v-for="item in menuItems" :key="item.label">
        <UButton 
          :icon="item.icon"
          :badge="item.badge"
          @click="item.action"
        >
          {{ item.label }}
        </UButton>
      </li>
    </ul>
  </nav>
</template>

<script setup>
const { canUseFeature, previewBadge } = useFeatures()

const menuItems = computed(() => [
  {
    label: 'Dashboard',
    icon: 'i-heroicons-home',
    action: () => navigateTo('/dashboard')
  },
  {
    label: 'FAQ Management',
    icon: 'i-heroicons-question-mark-circle',
    action: () => navigateTo('/faq'),
    hide: !canUseFeature('faqImportMode'),
    badge: previewBadge('faqImportMode')
  },
  {
    label: 'IP Settings',
    icon: 'i-heroicons-shield-check',
    action: () => navigateTo('/ip-settings'),
    hide: !canUseFeature('ipAddressControl'),
    badge: previewBadge('ipAddressControl')
  }
].filter(item => !item.hide))
</script>
```

### 3. Settings Page with Feature Sections
```vue
<template>
  <div class="settings-page">
    <h1>Settings</h1>
    
    <!-- Always available settings -->
    <SettingsSection title="General">
      <GeneralSettings />
    </SettingsSection>
    
    <!-- Feature-gated settings -->
    <SettingsSection 
      v-if="canUseFeature('userPasswordChange')"
      title="Security"
      :badge="previewBadge('userPasswordChange')"
    >
      <PasswordChangeForm />
    </SettingsSection>
    
    <SettingsSection 
      v-if="canUseFeature('ipAddressControl')"
      title="Access Control"
      :badge="previewBadge('ipAddressControl')"
    >
      <IPAddressSettings />
    </SettingsSection>
  </div>
</template>

<script setup>
const { canUseFeature, previewBadge } = useFeatures()
</script>
```

### 4. Dropdown Menu with Features (Real Example)
```vue
<template>
  <UDropdown :items="dropdownItems">
    <UButton>Actions</UButton>
    
    <template #item="{ item }">
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center gap-2">
          <UIcon :name="item.icon" />
          <span>{{ item.label }}</span>
        </div>
        <UBadge v-if="item.badge" v-bind="item.badge" />
      </div>
    </template>
  </UDropdown>
</template>

<script setup>
const { canUseFeature, previewBadge } = useFeatures()

const dropdownItems = computed(() => [
  [
    {
      label: 'Profile',
      icon: 'i-heroicons-user',
      click: () => navigateTo('/profile')
    }
  ],
  [
    {
      label: 'Change Password',
      icon: 'i-heroicons-key',
      click: () => openPasswordModal(),
      hide: !canUseFeature('userPasswordChange'),
      badge: previewBadge('userPasswordChange')
    },
    {
      label: 'Support',
      icon: 'i-heroicons-question-mark-circle',
      click: () => navigateTo('/support'),
      hide: !canUseFeature('support'),
      badge: previewBadge('support')
    }
  ],
  [
    {
      label: 'Logout',
      icon: 'i-heroicons-arrow-left-on-rectangle',
      click: () => logout()
    }
  ]
].map(group => group.filter(item => !item.hide)).filter(group => group.length > 0))
</script>
```

## API Integration Examples

### 1. Conditional API Calls
```typescript
// composables/useAPI.ts
export const useAPI = () => {
  const { canUseFeature } = useFeatures()
  
  const getAdvancedData = async () => {
    if (!canUseFeature('advancedAnalytics')) {
      throw new Error('Feature not available')
    }
    
    return await $fetch('/api/advanced-data')
  }
  
  const exportFAQ = async () => {
    if (!canUseFeature('faqImportMode')) {
      throw new Error('FAQ export feature not available')
    }
    
    return await $fetch('/api/faq/export')
  }
  
  return {
    getAdvancedData,
    exportFAQ
  }
}
```

### 2. Feature-aware Store Actions
```typescript
// stores/features.ts
export const useFeatureStore = defineStore('features', () => {
  const { canUseFeature } = useFeatures()
  
  const performAdvancedAction = async () => {
    if (!canUseFeature('advancedMode')) {
      console.warn('Advanced mode not available')
      return null
    }
    
    // Perform advanced action
    return await advancedAPI.process()
  }
  
  return {
    performAdvancedAction
  }
})
```

## Testing Examples

### 1. Component Testing with Features
```typescript
// tests/components/FeatureComponent.test.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import FeatureComponent from '~/components/FeatureComponent.vue'

// Mock useFeatures composable
vi.mock('~/composables/useFeatures', () => ({
  useFeatures: () => ({
    canUseFeature: vi.fn((feature: string) => feature === 'enabledFeature'),
    previewBadge: vi.fn(() => ({ label: 'プレビュー', color: 'red' }))
  })
}))

describe('FeatureComponent', () => {
  it('shows feature when enabled', () => {
    const wrapper = mount(FeatureComponent)
    expect(wrapper.find('[data-testid="enabled-feature"]').exists()).toBe(true)
  })
  
  it('hides feature when disabled', () => {
    const wrapper = mount(FeatureComponent)
    expect(wrapper.find('[data-testid="disabled-feature"]').exists()).toBe(false)
  })
})
```

### 2. E2E Testing with Feature Flags
```typescript
// tests/e2e/features.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Feature Flags', () => {
  test('shows preview badge for disabled features', async ({ page }) => {
    // Set environment with feature disabled
    await page.goto('/?features=disabled')
    
    // Check for preview badge
    await expect(page.locator('[data-testid="preview-badge"]')).toBeVisible()
    await expect(page.locator('[data-testid="preview-badge"]')).toHaveText('プレビュー')
  })
  
  test('hides features for non-operators', async ({ page }) => {
    // Login as regular user
    await page.goto('/login')
    await page.fill('[data-testid="username"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password')
    await page.click('[data-testid="login-button"]')
    
    // Feature should be hidden
    await expect(page.locator('[data-testid="admin-feature"]')).not.toBeVisible()
  })
})
```

## Docker/Deployment Examples

### 1. Docker Compose with Feature Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    environment:
      - FEATURES_IP_ADDRESS_CONTROL=true
      - FEATURES_FAQ_IMPORT_MODE=false
      - FEATURES_USER_PASSWORD_CHANGE=true
      - FEATURES_SUPPORT=true
    ports:
      - "3000:3000"
```

### 2. Kubernetes ConfigMap
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-features
data:
  FEATURES_IP_ADDRESS_CONTROL: "true"
  FEATURES_FAQ_IMPORT_MODE: "false"
  FEATURES_USER_PASSWORD_CHANGE: "true"
  FEATURES_SUPPORT: "true"
```

### 3. GitHub Actions with Environment-specific Features
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Deploy to Staging
        if: github.ref == 'refs/heads/develop'
        env:
          FEATURES_IP_ADDRESS_CONTROL: true
          FEATURES_FAQ_IMPORT_MODE: true
          FEATURES_USER_PASSWORD_CHANGE: false
          FEATURES_SUPPORT: true
        run: npm run deploy:staging
        
      - name: Deploy to Production
        if: github.ref == 'refs/heads/main'
        env:
          FEATURES_IP_ADDRESS_CONTROL: true
          FEATURES_FAQ_IMPORT_MODE: false
          FEATURES_USER_PASSWORD_CHANGE: true
          FEATURES_SUPPORT: true
        run: npm run deploy:production
```

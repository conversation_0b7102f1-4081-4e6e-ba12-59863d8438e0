# Feature Configuration System

## Overview
This application uses a comprehensive feature flag system that allows for granular control over feature availability through environment variables. Features can be enabled/disabled dynamically and include preview functionality for experimental features.

## Architecture

### Core Components
- **Feature Configuration**: Defined in `nuxt.config.ts`
- **Feature Composable**: `useFeatures()` composable for runtime feature checking
- **Runtime Access Control**: Automatic hiding/showing of UI elements based on feature flags
- **Preview System**: Automatic badge display for experimental features
- **Operator Override**: Operators can access all features regardless of configuration

## Available Features

### Currently Configured Features

| Feature | Environment Variable | Default | Description |
|---------|---------------------|---------|-------------|
| IP Address Control | `FEATURES_IP_ADDRESS_CONTROL` | `false` | Controls IP-based access restrictions |
| FAQ Import Mode | `FEATURES_FAQ_IMPORT_MODE` | `false` | Enables FAQ bulk import functionality |
| User Password Change | `FEATURES_USER_PASSWORD_CHANGE` | `false` | Allows users to change their passwords |
| Support | `FEATURES_SUPPORT` | `false` | Access to support documentation and tours |

### Environment Configuration

```bash
# Enable all features
FEATURES_IP_ADDRESS_CONTROL=true
FEATURES_FAQ_IMPORT_MODE=true
FEATURES_USER_PASSWORD_CHANGE=true
FEATURES_SUPPORT=true

# Mixed configuration (some enabled, some disabled)
FEATURES_IP_ADDRESS_CONTROL=true
FEATURES_FAQ_IMPORT_MODE=false
FEATURES_USER_PASSWORD_CHANGE=true
FEATURES_SUPPORT=false

# All disabled (default state - all features are preview)
# Simply omit all environment variables
```

## How to Use Features

### 1. Checking Feature Availability
```typescript
const { canUseFeature } = useFeatures()

// Check if a feature is available
if (canUseFeature('support')) {
  // Feature is available
}
```

### 2. Preview Badge System
```typescript
const { previewBadge } = useFeatures()

// Get preview badge configuration
const badge = previewBadge('support')
// Returns: { label: 'プレビュー', color: 'red', variant: 'outline', size: 'xs', ... }
```

### 3. Conditional UI Rendering
```vue
<template>
  <!-- Menu item with feature flag -->
  <UButton 
    v-if="canUseFeature('support')"
    :badge="previewBadge('support')"
  >
    Support
  </UButton>
</template>
```

## Implementation Patterns

### 1. Adding a New Feature

#### Step 1: Add to Configuration
```typescript
// nuxt.config.ts
features: {
  // ...existing features...
  newFeature: process.env.FEATURES_NEW_FEATURE || false
}
```

#### Step 2: Update TypeScript Types
```typescript
// app/types/nuxt.d.ts
declare module '@nuxt/schema' {
  interface RuntimeConfig {
    public: {
      features: {
        // ...existing features...
        newFeature: boolean
      }
    }
  }
}
```

#### Step 3: Use in Components
```vue
<script setup>
const { canUseFeature, previewBadge } = useFeatures()
</script>

<template>
  <div v-if="canUseFeature('newFeature')">
    <UBadge v-bind="previewBadge('newFeature')" />
    <!-- Feature content -->
  </div>
</template>
```

### 2. Menu Integration Pattern
```typescript
// For dropdown menus
const items = computed(() => [
  {
    label: 'Feature Name',
    icon: 'icon-name',
    click: handleClick,
    hide: !canUseFeature('featureName'),
    badge: previewBadge('featureName')
  }
].filter(item => !item.hide))
```

## Feature Behavior

### Default State (All `false`)
- All features show "プレビュー" (Preview) badge
- Features are hidden from regular users
- Only operators can access features
- Ideal for development/testing environments

### Production State (Selective `true`)
- Enabled features appear normally for all users
- Disabled features remain in preview mode
- Gradual feature rollout capability

### Full Enable State (All `true`)
- All features available to all users
- No preview badges shown
- Full production mode

## Operator Privileges

Operators have special access rights:
- Can see ALL features regardless of environment configuration
- Useful for testing and administration
- Determined by `isOperator` value from auth store

## Best Practices

### 1. Feature Naming
- Use descriptive, kebab-case names
- Prefix environment variables with `FEATURES_`
- Use consistent naming across config, types, and usage

### 2. Default Values
- Start with `false` for new features (preview mode)
- Enable gradually in production environments
- Document expected behavior for each state

### 3. UI Integration
- Always use `canUseFeature()` for conditional rendering
- Include preview badges for better UX
- Hide features completely when disabled (don't just disable)

### 4. Testing
- Test both enabled and disabled states
- Verify operator override functionality
- Check preview badge appearance

## Troubleshooting

### Common Issues
1. **Feature not appearing**: Check environment variable and operator status
2. **TypeScript errors**: Ensure feature is added to type definitions
3. **Preview badge not showing**: Verify `previewBadge()` is properly applied

### Debug Tips
```typescript
// Check current feature state
const { canUseFeature, isPreviewFeature } = useFeatures()
console.log('Feature enabled:', canUseFeature('featureName'))
console.log('Is preview:', isPreviewFeature('featureName'))
```

## Migration Guide

When adding new features to existing components:
1. Add feature configuration to `nuxt.config.ts`
2. Update TypeScript types in `app/types/nuxt.d.ts`
3. Wrap existing functionality with feature flags
4. Add preview badges where appropriate
5. Test with different environment configurations

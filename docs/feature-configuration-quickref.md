# Feature Configuration Quick Reference

## Quick Setup Checklist

### ✅ Adding a New Feature
1. **Config** - Add to `nuxt.config.ts` features
2. **Types** - Update `app/types/nuxt.d.ts` 
3. **Usage** - Use `canUseFeature()` and `previewBadge()`
4. **Test** - Verify both enabled/disabled states

### ✅ Environment Variables
```bash
# Pattern: FEATURES_<FEATURE_NAME>=true/false
FEATURES_IP_ADDRESS_CONTROL=true
FEATURES_FAQ_IMPORT_MODE=false
FEATURES_USER_PASSWORD_CHANGE=true
FEATURES_SUPPORT=false
```

### ✅ Common Code Patterns

#### Component Usage
```vue
<script setup>
const { canUseFeature, previewBadge } = useFeatures()
</script>

<template>
  <!-- Hide completely when disabled -->
  <div v-if="canUseFeature('featureName')">
    <UBadge v-bind="previewBadge('featureName')" />
    <!-- Feature content -->
  </div>
</template>
```

#### Menu Integration
```typescript
{
  label: 'Feature Name',
  icon: 'icon-name',
  click: handleClick,
  hide: !canUseFeature('featureName'),
  badge: previewBadge('featureName')
}
```

## Available Features Reference

| Feature | Env Var | Default | Preview Badge |
|---------|---------|---------|---------------|
| IP Address Control | `FEATURES_IP_ADDRESS_CONTROL` | `false` | ✅ |
| FAQ Import Mode | `FEATURES_FAQ_IMPORT_MODE` | `false` | ✅ |
| User Password Change | `FEATURES_USER_PASSWORD_CHANGE` | `false` | ✅ |
| Support | `FEATURES_SUPPORT` | `false` | ✅ |

## Composable Functions

```typescript
const { 
  canUseFeature,    // (featureName: string) => boolean
  isPreviewFeature, // (featureName: string) => boolean  
  previewBadge      // (featureName: string) => BadgeProps
} = useFeatures()
```

## Operator Override
- Operators see ALL features regardless of config
- Controlled by `isOperator` from auth store
- Useful for testing and administration

## Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| Feature not showing | Check env var and operator status |
| TypeScript errors | Add feature to `app/types/nuxt.d.ts` |
| Preview badge missing | Use `previewBadge('featureName')` |
| Feature always visible | Check if user is operator |

## File Locations
- **Config**: `nuxt.config.ts`
- **Types**: `app/types/nuxt.d.ts`
- **Composable**: `app/composables/useFeatures.ts`
- **Documentation**: `docs/feature-configuration-guide.md`

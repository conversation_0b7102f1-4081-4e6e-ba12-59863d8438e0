import { test, expect } from '@playwright/test'
import { navigateAndWait, commonLogin } from '../utils/helpers'

test.describe('アンケート設定画面テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    // 設定 > チャットボット設定に遷移
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.getByText('アンケート', { exact: true }).click())
  })

  test('レイアウトが正しく表示されること', async ({ page }) => {
    // 設定 > アンケート画面に遷移している前提

    // タイトル系
    await expect(page.getByText('アンケート設定', { exact: true })).toBeVisible()
    await expect(page.getByText('チャットボットの回答後に表示するアンケートの設定を行います。')).toBeVisible()

    // フィールドラベル確認
    await expect(page.getByText('アンケート選択肢数')).toBeVisible()
    await expect(page.getByText('アンケート選択', { exact: true })).toBeVisible()
    await expect(page.getByText('選択ラベル')).toBeVisible()
    await expect(page.getByText('返信メッセージ', { exact: true })).toBeVisible()

    // ボタン確認
    await expect(page.getByRole('button', { name: 'リセット' })).toBeVisible()
    await expect(page.getByRole('button', { name: '更新' })).toBeVisible()
    await expect(page.getByRole('button', { name: '削除' })).toBeVisible()
  })

  test('アンケート選択肢数を変更できること', async ({ page }) => {
    // セレクトボックス（アンケート選択肢数）を取得
    const selectBox = page.getByLabel('アンケート選択肢数')

    // "5" を選択
    await selectBox.selectOption('5')
    // 少し待ってからチェック
    await page.waitForTimeout(3000)

    await expect(page.locator('div.flex.flex-col.justify-start:has(div:text("0"))').getByRole('button')).toBeVisible()
    await expect(page.locator('div.flex.flex-col.justify-start:has(div:text("1"))').getByRole('button')).toBeVisible()
    await expect(page.locator('div.flex.flex-col.justify-start:has(div:text("2"))').getByRole('button')).toBeVisible()
    await expect(page.locator('div.flex.flex-col.justify-start:has(div:text("3"))').getByRole('button')).toBeVisible()
    await expect(page.locator('div.flex.flex-col.justify-start:has(div:text("4"))').getByRole('button')).toBeVisible()
  })

  const surveyOptions = [
    { index: 0, label: 'はい', message: 'ありがとうございます。他にご不明な点はございますか？別の会話を始める場合は、画面上部の丸い矢印ボタンを押してください。' },
    { index: 1, label: 'いいえ', message: 'ご協力ありがとうございます。必要であれば、再度お知らせください。' },
    { index: 2, label: '微妙', message: 'わかりにくかった点などがあれば、お知らせください。改善に努めます。' },
    { index: 3, label: 'わからない', message: '不明な点はお気軽にご相談ください。できる限りお手伝いいたします。' },
    { index: 4, label: 'その他', message: '他にお伝えしたいことがあれば、ぜひ教えてください。' }
  ]

  surveyOptions.forEach(({ index, label, message }) => {
    test.describe(`アンケート選択：${index}`, () => {
      test(`アンケート選択${index}の内容を更新できること`, async ({ page }) => {
        const choiceButton = page.locator(`div.flex.flex-col.justify-start:has(div:text("${index}")) button`)
        await choiceButton.click()
        // 少し待ってからチェック
        await page.waitForTimeout(3000)

        const labelInput = page.getByLabel('選択ラベル')
        await expect(labelInput).toBeVisible({ timeout: 5000 })
        await labelInput.fill(label)

        const messageEditor = page.locator('.cm-content[contenteditable="true"]')
        await expect(messageEditor).toBeVisible({ timeout: 5000 })
        await messageEditor.click()
        await messageEditor.press('Meta+A')
        await messageEditor.press('Backspace')
        await messageEditor.type(message)
        await page.getByRole('button', { name: '更新' }).click()

        await expect(labelInput).toHaveValue(label)
        await expect(messageEditor).toContainText(message)
      })

      test(`アンケート選択${index}の内容をリセットできること`, async ({ page }) => {
        const choiceButton = page.locator(`div.flex.flex-col.justify-start:has(div:text("${index}")) button`)
        await choiceButton.click()

        const labelInput = page.getByLabel('選択ラベル')
        const messageEditor = page.locator('.cm-content[contenteditable="true"]')

        await labelInput.fill('一時変更')
        await messageEditor.click()
        await messageEditor.press('Meta+A')
        await messageEditor.press('Backspace')
        await messageEditor.type('一時メッセージ')

        await page.getByRole('button', { name: 'リセット' }).click()

        await expect(labelInput).toHaveValue(label)
        await expect(messageEditor).toContainText(message)
      })

      test(`アンケート選択${index}のプレビューが表示されること`, async ({ page }) => {
        const choiceButton = page.locator(`div.flex.flex-col.justify-start:has(div:text("${index}")) button`)
        await choiceButton.click()

        const previewButton = page.getByRole('button', { name: 'プレビュー' })
        await previewButton.scrollIntoViewIfNeeded()
        await expect(previewButton).toBeEnabled()
        await previewButton.click()

        const previewMessage = page.locator('div.bg-primary-100.dark\\:bg-primary-900').getByText(message)
        await expect(previewMessage).toBeVisible({ timeout: 5000 })
        await expect(previewMessage).toContainText(message)
      })

      test(`アンケート削除${index}ポップアップをキャンセルボタンで閉じられること`, async ({ page }) => {
        const choiceButton = page.locator(`div.flex.flex-col.justify-start:has(div:text("${index}")) button`)
        await choiceButton.click()
        await page.getByRole('button', { name: '削除' }).click()
        await page.getByRole('button', { name: 'キャンセル' }).click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
      })

      test(`アンケート削除${index}ポップアップを✖️ボタンで閉じられること`, async ({ page }) => {
        const choiceButton = page.locator(`div.flex.flex-col.justify-start:has(div:text("${index}")) button`)
        await choiceButton.click()
        await page.getByRole('button', { name: '削除' }).click()
        await page.locator('button[aria-label="Close"]').click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
      })

      test(`アンケート選択${index}を削除できること`, async ({ page }) => {
        const choiceButton = page.locator(`div.flex.flex-col.justify-start:has(div:text("${index}")) button`)
        await choiceButton.click()

        await page.getByRole('button', { name: '削除' }).click()
        await expect(page.getByText('このアンケート選択を削除しますか？')).toBeVisible({ timeout: 5000 })

        const confirmDialog = page.getByRole('dialog')
        await confirmDialog.getByRole('button', { name: '削除' }).click()

        const labelInput = page.getByLabel('選択ラベル')
        await expect(labelInput).toHaveValue('未設定')

        const placeholder = page.locator('.cm-placeholder')
        await expect(placeholder).toHaveText('開始時メッセージ...')
      })
    })
  })

  test('アンケート選択肢数を1に設定して2件目を更新するとエラーが出ること', async ({ page }) => {
    // アンケート選択肢数を「1」に設定
    const selectBox = page.getByLabel('アンケート選択肢数')
    await selectBox.selectOption('1')
    await page.waitForTimeout(3000) // UI反映待ち

    // アンケート選択 0 を更新
    const firstChoice = page.locator('div.flex.flex-col.justify-start:has(div:text("0")) button')
    await firstChoice.click()

    const labelInput0 = page.getByLabel('選択ラベル')
    const messageEditor0 = page.locator('.cm-content[contenteditable="true"]')

    await labelInput0.fill('テスト0')
    await messageEditor0.click()
    await messageEditor0.press('Meta+A')
    await messageEditor0.press('Backspace')
    await messageEditor0.type('テストメッセージ0')

    await page.getByRole('button', { name: '更新' }).click()

    // 一旦完了を待機
    await expect(labelInput0).toHaveValue('テスト0')

    // アンケート選択 1 を選んで更新（←ここでエラーを想定）
    const secondChoice = page.locator('div.flex.flex-col.justify-start:has(div:text("1")) button')
    await secondChoice.click()

    const labelInput1 = page.getByLabel('選択ラベル')
    const messageEditor1 = page.locator('.cm-content[contenteditable="true"]')

    await labelInput1.fill('テスト1')
    await messageEditor1.click()
    await messageEditor1.press('Meta+A')
    await messageEditor1.press('Backspace')
    await messageEditor1.type('テストメッセージ1')

    // エラー発生を期待して更新
    await page.getByRole('button', { name: '更新' }).click()

    // エラートーストが表示されることを確認（status code 400）
    const errorToast = page.getByText('エラーが発生しました。Request failed with status code 400')
    await expect(errorToast).toBeVisible({ timeout: 5000 })
  })
})

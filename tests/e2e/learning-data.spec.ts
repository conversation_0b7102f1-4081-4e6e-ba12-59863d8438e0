import { test, expect, type Page } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait,
  fillFileUploadForm,
  checkFilePreview,
  submitFormAndCheckList,
  clickRefreshButton,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage,
  openConfirmPopup
} from '../utils/helpers'

test.describe('データソース管理画面テスト', () => {
  async function clickSidebarItem(page: Page, label: string) {
    const button = page.getByRole('button', { name: label, exact: false })
    await expect(button).toBeVisible({ timeout: 10000 })
    await button.click()
  }

  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => clickSidebarItem(page, 'データソース'))
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'データソース' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'データソース登録' })).toBeVisible()
    await expect(page.getByPlaceholder('データソースの検索')).toBeVisible()
    await expect(page.locator('div[role="button"]', { hasText: 'ステータス' }).first()).toBeVisible()
    await expect(page.locator('div[role="button"]', { hasText: 'コンテキストの種類' }).first()).toBeVisible()

    const headers = ['データソース名', 'ファイル名/URL', '登録日時', '更新日時', 'ステータス']
    for (const header of headers) {
      await expect(page.getByRole('columnheader', { name: header, exact: true })).toBeVisible()
    }
  })

  test('登録画面の表示（ファイル）', async ({ page }) => {
    await page.getByRole('button', { name: 'データソース登録' }).click()

    await expect(
      page.locator('a span.truncate').filter({ hasText: 'ファイル' }).first()
    ).toBeVisible()

    await expect(page.getByText('ファイルのアップロード', { exact: true })).toBeVisible()
    await expect(page.getByText('データソース名', { exact: true })).toBeVisible()
    await expect(page.getByText('ファイル', { exact: true })).toBeVisible()
    await expect(page.getByText('優先度', { exact: true })).toBeVisible()
    await expect(page.getByText('ラベル', { exact: true })).toBeVisible()
    await expect(page.getByRole('button', { name: 'データソースを追加' })).toBeVisible()

    await expect(page.getByText('データソースソース', { exact: true })).toBeVisible()
    await expect(page.getByRole('button', { name: 'プレビュー' })).toBeVisible()
    await page.getByRole('button', { name: '登録' }).click()
  })

  test('データソース項目の追加・削除', async ({ page }) => {
    await page.getByRole('button', { name: 'データソース登録' }).click()
    await page.getByRole('button', { name: 'データソースを追加' }).click()
    const inputs = page.locator('input[placeholder="例：税金に関するデータ"]')
    await expect(inputs.nth(1)).toBeVisible() // 2つ目が追加されたもの
    await page.locator('button:has-text("データソースを削除")').last().click()
    await expect(inputs.nth(1)).not.toBeVisible()
  })

  test('対象外ファイル選択時のエラーメッセージ確認', async ({ page }) => {
    await page.getByRole('button', { name: 'データソース登録' }).click()
    await fillFileUploadForm(page, '不正ファイル', 'test.jpeg')
    await expect(page.locator('text=0 ファイル (0 Byte) ')).toBeVisible() // 0 ファイル (0 Byte) のまま選択されないこと
  })

  test('ファイルサイズ超過時のエラー表示確認', async ({ page }) => {
    await page.getByRole('button', { name: 'データソース登録' }).click()
    await fillFileUploadForm(page, 'サイズオーバー', 'too_large.txt')
    await page.waitForTimeout(1000)
    await page.getByRole('button', { name: '登録', exact: true }).click()
    await expect(page.locator('text=登録失敗')).toBeVisible()
  })

  test('ファイルプレビュー確認', async ({ page }) => {
    await page.getByRole('button', { name: 'データソース登録' }).click()
    await fillFileUploadForm(page, 'プレビュー用', 'sample.csv')
    await checkFilePreview(page)
  })

  test('必須項目未入力時のバリデーション表示', async ({ page }) => {
    await page.getByRole('button', { name: 'データソース登録' }).click()
    await page.getByRole('button', { name: '登録' }).click()
    await expect(page.locator('text=データソース名は必須です。')).toBeVisible()
  })

  const fileTests = [
    { label: 'CSV', file: 'sample.csv' },
    { label: 'Excel', file: 'sample.xlsx' },
    { label: 'DOCX', file: 'sample.docx' },
    { label: 'PPTX', file: 'sample.pptx' },
    { label: 'TXT', file: 'sample.txt' },
    { label: 'PDF', file: 'sample.pdf' },
    { label: 'JSON', file: 'sample.json' },
    { label: 'MD', file: 'sample.md' }
  ]

  for (const { label, file } of fileTests) {
    test(`${label}ファイルを使って正常に登録できること`, async ({ page }) => {
      await page.getByRole('button', { name: 'データソース登録' }).click()
      const title = `テスト_${label}`
      await fillFileUploadForm(page, title, file)
      await submitFormAndCheckList(page, title)
    })
  }

  const goToWebsiteTab = async (page: Page) => {
    await page.getByRole('button', { name: 'データソース登録' }).click()
    await page.locator('a span.truncate').filter({ hasText: 'ウェブサイト' }).click()
  }

  test('ウェブサイトタブのレイアウト表示', async ({ page }) => {
    await goToWebsiteTab(page)
    await expect(page.getByText('ウェブサイトのURLの登録')).toBeVisible()
    await expect(page.getByText('データソース名', { exact: true })).toBeVisible()
    await expect(page.getByText('URL', { exact: true })).toBeVisible()
    await expect(page.getByText('無視するタグ', { exact: true })).toBeVisible()
    await expect(page.getByText('無視するクラス', { exact: true })).toBeVisible()
    await expect(page.getByText('優先度', { exact: true })).toBeVisible()
    await expect(page.getByText('ラベル', { exact: true })).toBeVisible()
    await expect(page.getByRole('button', { name: '他のURLを追加' })).toBeVisible()

    await expect(page.getByText('データソースソース', { exact: true })).toBeVisible()
    await expect(page.getByRole('button', { name: 'プレビュー' })).toBeVisible()
    await expect(page.getByRole('button', { name: '登録' })).toBeVisible()
  })

  test('データソース項目の追加・削除（ウェブサイト）', async ({ page }) => {
    await goToWebsiteTab(page)
    // 入力エリアの描画待ち（これが必要）
    await expect(page.locator('input[placeholder="例: https://example.com"]')).toBeVisible()

    const urlInputs = page.locator('input[placeholder="例: https://example.com"]')
    const initialCount = await urlInputs.count()

    // URL項目を追加
    await page.getByRole('button', { name: '他のURLを追加' }).click()
    await expect(urlInputs.nth(initialCount)).toBeVisible() // 新規追加分が表示されること

    // 削除処理（追加された方）
    const deleteButtons = page.locator('button', { hasText: 'データソースを削除' })
    await deleteButtons.last().click()

    // 要素数が減ったことを確認
    const afterCount = await urlInputs.count()
    expect(afterCount).toBe(initialCount)
  })

  test('正常なデータソースをウェブサイトで登録できること', async ({ page }) => {
    await goToWebsiteTab(page)

    const testTitle = 'テストウェブサイトplaynext'
    const testURL = 'https://www.playnext-lab.co.jp/'
    const ignoreTag = 'a'
    const ignoreClass = 'body'

    // 入力
    await page.locator('input[placeholder="例：税金に関するデータ"]').fill(testTitle)
    await page.locator('input[placeholder="例: https://example.com"]').fill(testURL)
    await page.keyboard.press('Tab')
    // await page.keyboard.press('Tab')

    // 無視するタグの選択
    const tagSelectBtn = page.locator('button:has-text("タグを選択・入力してください。")')
    await expect(tagSelectBtn).toBeVisible()
    await tagSelectBtn.click()

    const tagOption = page.getByRole('option', { name: ignoreTag, exact: true })
    await expect(tagOption).toBeVisible()
    await tagOption.click()
    // カーソルアウトするように画面上の何かクリック
    await page.getByText('ウェブサイトのURLの登録').click()

    // 無視するクラスの選択
    const classSelectBtn = page.locator('button:has-text("クラスを選択・入力してください。")')
    await expect(classSelectBtn).toBeVisible()
    await classSelectBtn.click()
    const classOption = page.getByRole('option', { name: ignoreClass, exact: true })
    await expect(classOption).toBeVisible()
    await classOption.click()
    // 選択ボックスを閉じるため画面上の何かクリック
    await page.getByText('ウェブサイトのURLの登録').click()

    // 成功メッセージと遷移先を確認
    await submitFormAndCheckList(page, testTitle)
  })

  test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
    await goToWebsiteTab(page)
    await page.getByRole('button', { name: '登録' }).click()
    await expect(page.getByText('必須項目です')).toBeVisible()
  })

  test('リフレッシュボタンで再表示されること', async ({ page }) => {
    await clickRefreshButton(page)
    await expect(page.locator('tbody')).toBeVisible()
  })

  test('存在しないキーワードでの検索結果確認', async ({ page }) => {
    await page.getByPlaceholder('データソースの検索').fill('存在しないデータXYZ')
    await expect(page.getByText('データがありません')).toBeVisible({ timeout: 30000 })
  })

  test('部分一致検索ができること(ナレッジ一覧)', async ({ page }) => {
    await page.getByPlaceholder('データソースの検索').fill('テスト')
    const rows = page.locator('tbody tr')
    expect(rows.nth(0).locator('td').nth(0)).toHaveText('テスト') // 1列目にデータソース名がある
  })

  test.describe('ステータス検索ができること', () => {
    // const statuses = ['有効', '無効']
    const statuses = ['無効']
    for (const status of statuses) {
      test(`${status} で検索できること`, async ({ page }) => {
        await page.locator('div[role="button"]', { hasText: 'ステータス' }).click()
        await page.getByRole('option', { name: status }).click()
        // 選択ボックスを閉じるため画面上の何かクリック
        await page.getByText('データソース').click()

        // 少し待ってデータのロードを待機
        await page.waitForTimeout(3000)

        // 検索結果の「データがありません」判定
        const noDataLocator = page.locator('td', { hasText: 'データがありません' })
        console.log(noDataLocator)
        if (await noDataLocator.isVisible()) {
          console.warn(`⚠️ ステータス「 ${status} に該当するデータがありません。スキップします。`)
          return
        }

        // 検索結果の行を取得
        const rows = page.locator('tbody tr')
        const rowCount = await rows.count()
        for (let i = 0; i < rowCount; i++) {
          const cell = rows.nth(i).locator('td').nth(4) // ステータス列（5列目 = nth(4)）
          await expect(cell).toHaveText(status)
        }
      })
    }
  })

  test.describe('コンテキスト検索ができること', () => {
    const contexts = [
      { label: 'PDF', expected: 'pdf' },
      { label: 'TXT', expected: 'txt' },
      { label: 'JSON', expected: 'json' },
      { label: 'CSV', expected: 'csv' },
      { label: 'Excel', expected: 'xlsx' },
      { label: 'DOCX,PPTX,Markdown', expected: ['docx', 'pptx', 'md'] }
    ]

    for (const ctx of contexts) {
      test(`${ctx.label} で検索できること`, async ({ page }) => {
        await page.locator('div[role="button"]', { hasText: 'コンテキスト' }).click()

        const targets = ctx.label.split(',')
        for (const item of targets) {
          await page.getByRole('option', { name: item, exact: true }).click()
        }

        // 選択ボックスを閉じるため画面上の何かクリック
        await page.getByText('データソース').click()

        // 検索結果の「データがありません」判定
        const noDataLocator = page.locator('td', { hasText: 'データがありません' })
        if (await noDataLocator.isVisible()) {
          console.warn(`⚠️ コンテキスト「 ${ctx.label} に該当するデータがありません。スキップします。`)
          return
        }

        const rows = page.locator('tbody tr')
        const rowCount = await rows.count()

        for (let i = 0; i < rowCount; i++) {
          const fileName = await rows.nth(i).locator('td').nth(1).innerText() // ファイル名列（2列目 = nth(1)）
          const extMatch = fileName.match(/\.([a-zA-Z0-9]+)$/)
          const ext = extMatch?.[1]?.toLowerCase()

          if (!ext) {
            console.warn(`⚠️ ${i + 1}行目のファイル名 "${fileName}" から拡張子が取得できませんでした`)
            continue
          }

          if (Array.isArray(ctx.expected)) {
            expect(ctx.expected).toContain(ext)
          } else {
            expect(ext).toBe(ctx.expected)
          }
        }
      })
    }

    test('表示件数を変更できること', async ({ page }) => {
      await changePageSize(page, '10')
    })

    const activePageSelector = 'button.text-white'

    test('ページ送りと戻る操作ができること', async ({ page }) => {
      await moveToNextAndPreviousPage(page, '10', activePageSelector)
    })

    test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
      await moveToSpecificPage(page, '10', '2', activePageSelector)
    })
  })

  /* データソース明細画面：ナレッジ単体操作テスト */
  const knowledgeDetail = 'テストウェブサイトplaynext'

  test('ナレッジ編集画面に遷移できること', async ({ page }) => {
    await openConfirmPopup(page, knowledgeDetail, 'ナレッジ編集')
    // 少し待ってデータのロードを待機
    await page.waitForTimeout(3000)
    // パンくずで画面遷移確認
    await expect(
      page.locator('nav[aria-label="Breadcrumb"]').locator('span', { hasText: 'ナレッジ一覧' }).first()
    ).toBeVisible()
  })

  /* ナレッジ詳細画面・1件操作テスト */
  test('サイドメニューからナレッジ詳細画面へ遷移できること', async ({ page }) => {
    // サイドメニューから対象データソースをクリック
    await page.getByRole('link', { name: knowledgeDetail }).click()

    // パンくずで画面遷移確認
    await expect(
      page.locator('nav[aria-label="Breadcrumb"]').locator('span', { hasText: 'ナレッジ一覧' }).first()
    ).toBeVisible()

    await expect(page.getByPlaceholder('ナレッジの検索')).toBeVisible()
    await expect(page.locator('div[role="button"]', { hasText: 'ステータス' }).first()).toBeVisible()
    await expect(page.locator('div[role="button"]', { hasText: 'ラベルを選択' }).first()).toBeVisible()

    const headers = ['ナレッジ', '登録日時', '更新日時', '優先度', 'ラベル', 'ステータス']
    for (const header of headers) {
      await expect(page.getByRole('columnheader', { name: header, exact: true })).toBeVisible()
    }
  })

  const goToKnowledgeDetailPage = async (page: Page) => {
    // サイドメニューから対象データソースをクリック
    await page.getByRole('link', { name: knowledgeDetail }).click()
    await page.waitForTimeout(5000)
  }

  test('ナレッジ編集詳細画面に遷移できること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await openConfirmPopup(page, knowledgeDetail, '編集')
    // 「ナレッジ詳細」というpタグが表示されていることを確認
    // 画面ロード完了をちゃんと待つ
    await page.waitForSelector('p.font-semibold', { timeout: 10000 })
    await expect(page.locator('p.font-semibold', { hasText: 'ナレッジ詳細' })).toBeVisible()
  })

  test('ナレッジ編集詳細画面でキャンセルボタン押下で戻れること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await openConfirmPopup(page, knowledgeDetail, '編集')

    // 「ナレッジ詳細」というpタグが表示されていることを確認
    await page.waitForSelector('p.font-semibold', { timeout: 10000 })
    await expect(page.locator('p.font-semibold', { hasText: 'ナレッジ詳細' })).toBeVisible()

    await page.getByRole('button', { name: 'キャンセル' }).click()
    await page.waitForTimeout(3000)
    // パンくずで画面遷移確認
    await expect(
      page.locator('nav[aria-label="Breadcrumb"]').locator('span', { hasText: 'ナレッジ一覧' }).first()
    ).toBeVisible()
  })

  test('ナレッジ編集詳細画面で保存ボタン押下で更新できること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await openConfirmPopup(page, knowledgeDetail, '編集')

    const updatedContent = 'ナレッジ内容テスト更新'
    const updatedPriority = '200'

    // ナレッジ内容を更新（contenteditableなエリアにtypeする）
    const editor = page.locator('div[contenteditable="true"][role="textbox"]')
    await expect(editor).toBeVisible({ timeout: 10000 })
    await editor.click() // 一旦focusを当てる
    await editor.fill('') // 既存内容をクリア（必要に応じて）
    await editor.type(updatedContent)

    // 優先度を更新 (name属性で指定)
    await page.locator('input[name="priority"]').fill(updatedPriority)
    // focus outしたいため。タイトルをクリック
    await page.getByText('ナレッジ詳細').click()

    // // ラベル選択
    // const labelSelectBtn = page.locator('button:has-text("ラベルを選択")')
    // await labelSelectBtn.click()

    // const labelOption = page.getByRole('option', { name: 'テスト', exact: true })
    // await expect(labelOption).toBeVisible()
    // await labelOption.click()

    // // 選択ボックスを閉じるため適当にクリック
    // await page.getByText('ナレッジ詳細').click()

    // 保存
    await page.getByRole('button', { name: '保存' }).click()

    // パンくずリストも確認
    await expect(
      page.locator('nav[aria-label="Breadcrumb"]').locator('span', { hasText: 'ナレッジ一覧' }).first()
    ).toBeVisible()

    // 一覧1行目に更新内容が含まれているか確認
    const firstRowLabel = page.locator('table tbody tr').first().locator('div.cursor-pointer.line-clamp-2')
    await expect(firstRowLabel).toContainText(updatedContent)
  })

  test('ナレッジのCSV出力ができること', async ({ page }) => {
    // サイドメニューから対象データソースをクリック
    await goToKnowledgeDetailPage(page)
    const row = page.locator('table tbody tr').filter({
      has: page.locator('td', { hasText: knowledgeDetail })
    }).first()

    await row.hover()
    // 3点ボタンをクリック
    await row.locator('button').click()

    // CSV出力を監視
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      await page.getByRole('menuitem', { name: 'CSV出力' }).click()
    ])

    // ファイル名やパスを一応確認（使わなくてもOK）
    const path = await download.path()
    console.log('CSVがダウンロードされました: ', path)

    // 確認だけでよければ、ここで完了
    expect(download.suggestedFilename()).toMatch(/\.csv$/)
  })

  test('ナレッジ無効化ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await openConfirmPopup(page, knowledgeDetail, '無効化')
    await page.locator('button[aria-label="Close"]').click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('ナレッジステータスを有効⇔無効切り替えできること', async ({ page }) => {
    // サイドメニューから対象データソースをクリック
    await goToKnowledgeDetailPage(page)

    // テーブルの全行を取得
    const rows = page.locator('table tbody tr')
    const count = await rows.count()

    for (let i = 0; i < count; i++) {
      const row = rows.nth(i)
      const statusText = await row.locator('td').nth(6).innerText()

      if (statusText.includes('有効')) {
        // 有効なら無効化する
        await row.hover()
        await row.locator('button.row-menu').click()
        await page.getByRole('menuitem', { name: '無効化' }).click()

        await expect(page.getByText('このナレッジのステータスを変更しますか？')).toBeVisible()
        await page.getByRole('button', { name: '変更' }).click()

        // ステータスが無効に変わったことを確認
        await page.waitForTimeout(1000)
        // ソートが「有効」優先で並んでいるから、ステータスを無効化した後に、
        // その行の位置がテーブル内でズレてしまうのでこの確認はできない
        // await expect(row.locator('td').nth(6)).toContainText('無効')
        // エラーがないで画面が表示されるだけ確認
        await expect(
          page.locator('nav[aria-label="Breadcrumb"]').locator('span', { hasText: 'ナレッジ一覧' }).first()
        ).toBeVisible()

        break // 1件だけやればOKなのでbreak
      } else if (statusText.includes('無効')) {
        // 無効なら有効化する
        await row.hover()
        await row.locator('button.row-menu').click()
        await page.getByRole('menuitem', { name: '有効化' }).click()

        await expect(page.getByText('このナレッジのステータスを変更しますか？')).toBeVisible()
        await page.getByRole('button', { name: '変更' }).click()

        // ステータスが有効に変わったことを確認
        await page.waitForTimeout(1000)
        await expect(row.locator('td').nth(6)).toContainText('有効')

        break // 1件だけやればOKなのでbreak
      }
    }
  })

  test('ナレッジ削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await openConfirmPopup(page, knowledgeDetail, '削除')
    await page.locator('button[aria-label="Close"]').click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('ナレッジ削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await openConfirmPopup(page, knowledgeDetail, '削除')
    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('ナレッジ削除ポップアップのレイアウトが正しいこと', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await openConfirmPopup(page, knowledgeDetail, '削除')
    await expect(page.getByText('削除確認')).toBeVisible()
    await expect(page.getByText('このナレッジを削除しますか？')).toBeVisible()
    await page.getByRole('button', { name: '削除' }).click()

    // エラーがないで画面が表示されること
    await expect(
      page.locator('nav[aria-label="Breadcrumb"]').locator('span', { hasText: 'ナレッジ一覧' }).first()
    ).toBeVisible()
  })

  test('ヘッダーの一括チェック・非チェックができること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    // locatorでヘッダーのチェックボックスを取得する（th内にあるinput[type="checkbox"]）
    const headerCheckbox = page.locator('input[type="checkbox"][aria-label="Select all"]')
    const rowCheckboxes = page.locator('tbody input[type="checkbox"]')

    // ヘッダーのチェックボックスが表示されるまで待つ
    await expect(headerCheckbox).toBeVisible()

    // 一括チェック
    await headerCheckbox.click()
    const count = await rowCheckboxes.count()
    for (let i = 0; i < count; i++) {
      await expect(rowCheckboxes.nth(i)).toBeChecked()
    }

    // 一括非チェック
    await headerCheckbox.click()
    for (let i = 0; i < count; i++) {
      await expect(rowCheckboxes.nth(i)).not.toBeChecked()
    }
  })

  const multiRowCheckAndClickAction = async (page: Page, action: string) => {
    const rowCheckboxes = page.locator('tbody input[type="checkbox"]')
    for (let i = 0; i < 2; i++) {
      await rowCheckboxes.nth(i).click()
    }
    await page.getByRole('button', { name: /^一括操作（\d+件）$/, exact: true }).first().click()
    await page.getByRole('menuitem', { name: action }).click()
  }

  test('一括編集ポップアップが表示できること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await multiRowCheckAndClickAction(page, '一括編集')

    await expect(page.getByText('ナレッジを一括編集')).toBeVisible()
  })

  test('一括編集ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await multiRowCheckAndClickAction(page, '一括編集')

    // await page.getByRole('button', { name: 'キャンセル' }).click()
    await page.getByRole('button', { name: 'キャセル' }).click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('一括編集ポップアップで保存できること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await multiRowCheckAndClickAction(page, '一括編集')

    const dialog = page.locator('div[role="dialog"]')

    // 優先度入力後、カーソル移動
    await dialog.getByPlaceholder('優先度').fill('500')
    await page.keyboard.press('Tab')

    // ラベル選択
    const labelDropdown = dialog.locator('button:has-text("ラベルを選択")')
    await labelDropdown.click()
    await page.getByRole('option').first().click()
    await dialog.locator('text=ナレッジを一括編集').click()

    // ステータス選択
    await dialog.locator('select[name="enabled"]').selectOption('有効')
    await dialog.locator('text=ナレッジを一括編集').click()

    // 保存
    await dialog.getByRole('button', { name: '確定' }).click()
    // const labelOption = page.getByRole('option', { name: 'テスト', exact: true })

    // ダイアログが閉じられたことを確認
    await expect(dialog).toHaveCount(0)
  })

  test('一括CSV出力ができること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)

    const rowCheckboxes = page.locator('tbody input[type="checkbox"]')
    for (let i = 0; i < 2; i++) {
      await rowCheckboxes.nth(i).click()
    }
    await page.getByRole('button', { name: /^一括操作（\d+件）$/, exact: true }).first().click()

    // CSV出力を監視
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      await page.getByRole('menuitem', { name: 'CSV出力' }).click()
    ])

    // ファイル名やパスを一応確認（使わなくてもOK）
    const path = await download.path()
    console.log('CSVがダウンロードされました: ', path)

    // 確認だけでよければ、ここで完了
    expect(download.suggestedFilename()).toMatch(/\.csv$/)

    await page.waitForTimeout(3000)
  })

  test('一括削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await multiRowCheckAndClickAction(page, '削除')

    await page.locator('button[aria-label="Close"]').click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('一括削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await multiRowCheckAndClickAction(page, '削除')

    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('一括でナレッジ削除できること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await multiRowCheckAndClickAction(page, '削除')

    await expect(page.getByText('削除確認')).toBeVisible()
    await expect(page.getByText('このナレッジを削除しますか？')).toBeVisible()

    await page.getByRole('button', { name: '削除' }).click()
    await page.waitForTimeout(2000)
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('リフレッシュボタンで最新状態になること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await clickRefreshButton(page)
    await page.waitForTimeout(3000)
    await expect(page.locator('tbody')).toBeVisible()
  })

  test('表示件数を変更できること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await changePageSize(page, '50')
  })

  test('ページング（次ページ）できること', async ({ page }) => {
    // const activePageSelector = 'button:has(span.text-white)'
    await goToKnowledgeDetailPage(page)
    await moveToNextAndPreviousPage(page, '10')
  })

  test('ページング（指定ページ）できること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await moveToSpecificPage(page, '10', '2')
  })

  test('存在しないキーワードで「データがありません」になること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await page.getByPlaceholder('ナレッジの検索').fill('存在しないキーワード')
    await page.getByRole('button', { name: '検索' }).click()

    await expect(page.getByText('データがありません')).toBeVisible({ timeout: 30000 })
  })

  test('部分一致検索ができること', async ({ page }) => {
    await goToKnowledgeDetailPage(page)
    await page.getByPlaceholder('ナレッジの検索').fill('テスト')
    await page.getByRole('button', { name: '検索' }).click()

    const rows = page.locator('tbody tr')
    await expect(rows.first().locator('td').nth(1)).toContainText('テスト') // 2列目がナレッジ名の場合
  })

  const statuses = ['有効', '無効']
  for (const status of statuses) {
    test(`ステータス「${status}」で絞り込みできること`, async ({ page }) => {
      await goToKnowledgeDetailPage(page)

      const filterArea = page.locator('div.flex.items-stretch.gap-1\\.5')

      // ステータスドロップダウンを開く（修正）
      await filterArea.locator('button:has-text("ステータス")').click()
      await page.getByRole('option', { name: status, exact: true }).click()

      await page.waitForTimeout(3000)

      const noDataLocator = page.locator('td', { hasText: 'データがありません' })
      if (await noDataLocator.isVisible()) {
        console.warn(`⚠️ ステータス「${status}」に該当するデータがありません。スキップします。`)
        return
      }

      // ステータスを結果リスト内で確認（7列目）
      const rows = page.locator('tbody tr')
      const rowCount = await rows.count()

      for (let i = 0; i < rowCount; i++) {
        const cell = rows.nth(i).locator('td').nth(6)
        await expect(cell).toHaveText(status)
      }
    })
  }

  const labels = ['社員就業規則', 'テスト', '子育て']
  for (const label of labels) {
    test(`ラベル「${label}」で絞り込みできること`, async ({ page }) => {
      await goToKnowledgeDetailPage(page)

      const filterArea = page.locator('div.flex.items-stretch.gap-1\\.5')

      // ラベルドロップダウンを開く（修正）
      await filterArea.locator('button:has-text("ラベルを選択")').click()
      await page.getByRole('option', { name: label, exact: true }).click()

      await page.waitForTimeout(3000)

      const noDataLocator = page.locator('td', { hasText: 'データがありません' })
      if (await noDataLocator.isVisible()) {
        console.warn(`⚠️ ラベル「${label}」に該当するデータがありません。スキップします。`)
        return
      }

      // ラベルを結果リスト内で確認（6列目）
      const rows = page.locator('tbody tr')
      const rowCount = await rows.count()

      for (let i = 0; i < rowCount; i++) {
        const cell = rows.nth(i).locator('td').nth(5)
        await expect(cell).toContainText(label)
      }
    })
  }

  /* ナレッジ編集・無効化/有効化、再アップロード、削除、インデックスを更新を最後にする */
  const knowledgeTitle = 'テスト_JSON'

  test.describe('ナレッジ編集・無効化/有効化機能テスト', () => {
    test('ポップアップを✖️ボタンで閉じられること（無効化/有効化）', async ({ page }) => {
      await openConfirmPopup(page, knowledgeTitle, '無効化/有効化')
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ポップアップをキャンセルボタンで閉じられること（無効化/有効化）', async ({ page }) => {
      await openConfirmPopup(page, knowledgeTitle, '無効化/有効化')
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('有効状態から無効状態へ切り替えできること', async ({ page }) => {
      const row = page.locator('tr', { hasText: knowledgeTitle })
      await expect(row.locator('td >> text=有効')).toBeVisible()

      await openConfirmPopup(page, knowledgeTitle, '無効化/有効化')
      await expect(page.getByText('このデータソースのステータスを無効に変更しますか？')).toBeVisible()
      await page.getByRole('button', { name: '変更' }).click()

      await expect(row.getByText('無効')).toBeVisible({ timeout: 5000 })
    })

    test('無効状態から有効状態へ切り替えできること', async ({ page }) => {
      const row = page.locator('tr', { hasText: knowledgeTitle })
      await expect(row.locator('td >> text=無効')).toBeVisible()

      await openConfirmPopup(page, knowledgeTitle, '無効化/有効化')
      await expect(page.getByText('このデータソースのステータスを有効に変更しますか？')).toBeVisible()
      await page.getByRole('button', { name: '変更' }).click()

      await expect(row.getByText('有効')).toBeVisible({ timeout: 5000 })
    })
  })

  // 学習ファイル編集画面の初期表示（CSVを例にする）
  test('学習ファイル編集画面の初期表示（CSV）', async ({ page }) => {
    // 一覧から編集画面へ遷移
    const dataTitle = 'テスト_CSV'
    await openConfirmPopup(page, dataTitle, '再アップロード')

    // 編集画面の初期表示確認
    await expect(page.getByLabel('データソース名')).toHaveValue(dataTitle)
    await expect(page.getByText('ファイル', { exact: true })).toBeVisible()
    await expect(page.getByText('優先度', { exact: true })).toBeVisible()
    await expect(page.getByText('ラベル', { exact: true })).toBeVisible()
    // await expect(page.getByLabel('ラベル')).not.toBeEmpty()
  })

  // 学習ファイル編集画面で修正して編集
  test('学習ファイル編集画面で修正して編集できること（CSV）', async ({ page }) => {
    const originalTitle = 'テスト_CSV'
    const newTitle = 'テスト_CSV_更新'

    // 一覧から編集画面へ遷移
    await openConfirmPopup(page, originalTitle, '再アップロード')

    // タイトルを修正
    await page.getByLabel('データソース名').fill(newTitle)
    await fillFileUploadForm(page, newTitle, 'sample_edit.csv')

    // 更新処理と一覧の確認
    await submitFormAndCheckList(page, newTitle)
  })

  // 学習Website編集画面の初期表示
  test('学習Website編集画面の初期表示', async ({ page }) => {
    const dataTitle = 'テストウェブサイトplaynext'

    // 一覧から編集画面へ遷移
    await openConfirmPopup(page, dataTitle, '再アップロード')

    // 編集画面の初期表示確認
    await expect(page.getByLabel('データソース名')).toHaveValue(dataTitle)
    await expect(page.getByLabel('URL')).toHaveValue('https://www.playnext-lab.co.jp/')
    await expect(page.locator('button:has-text("タグを選択・入力してください。")')).toContainText('a')
    await expect(page.locator('button:has-text("クラスを選択・入力してください。")')).toContainText('body')
    await expect(page.getByLabel('ラベル')).not.toBeEmpty()
  })

  // 学習Website編集画面で修正して編集
  test('学習Website編集画面で修正して編集できること', async ({ page }) => {
    const originalTitle = 'テストウェブサイトplaynext'
    const newTitle = 'テストウェブサイトplaynext_更新'
    const newURL = 'https://www.playnext-lab.co.jp/'

    // 編集画面へ遷移
    await openConfirmPopup(page, originalTitle, '再アップロード')

    // 値を修正
    await page.getByLabel('データソース名').fill(newTitle)
    await page.getByLabel('URL').fill(newURL)

    // 更新処理と一覧の確認
    await submitFormAndCheckList(page, newTitle)
  })

  test.describe('ナレッジ削除機能テスト', () => {
    test('ポップアップを✖️ボタンで閉じられること（削除）', async ({ page }) => {
      await openConfirmPopup(page, knowledgeTitle, '削除')
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ポップアップをキャンセルボタンで閉じられること（削除）', async ({ page }) => {
      await openConfirmPopup(page, knowledgeTitle, '削除')
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('削除確認ポップアップのレイアウトが正しく表示されること', async ({ page }) => {
      await openConfirmPopup(page, knowledgeTitle, '削除')

      await expect(page.getByText('削除確認')).toBeVisible()
      await expect(page.getByText('このデータソースを削除しますか？')).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
      await expect(page.getByRole('button', { name: '削除' })).toBeVisible()
    })

    test('ナレッジが正常に削除されること', async ({ page }) => {
      const row = page.locator('tr', { hasText: knowledgeTitle })
      await expect(row).toBeVisible()

      await openConfirmPopup(page, knowledgeTitle, '削除')
      await page.getByRole('button', { name: '削除' }).click()

      // 削除ボタン押したあとで、対象ユーザが消えるまで待つ
      await expect(page.getByText(knowledgeTitle)).toHaveCount(0, { timeout: 5000 })
    })
  })

  // test('インデックスを更新できること', async ({ page }) => {
  //   await page.getByRole('button', { name: 'インデックスを更新' }).click()

  //   // 更新成功メッセージの確認（仮にトースト表示等ある場合）
  //   const successMsg = page.locator('text=データソースが登録されました')
  //   await expect(successMsg).toBeVisible({ timeout: 10000 })
  //   // await expect(successMsg).toBeHidden({ timeout: 10000 })
  // })
})

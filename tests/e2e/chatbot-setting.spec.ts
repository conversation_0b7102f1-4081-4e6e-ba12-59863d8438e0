import { test, expect } from '@playwright/test'
import { navigateAndWait, commonLogin } from '../utils/helpers'

test.describe('チャットボット設定画面テスト', () => {
  const correctedMessage = `
<p>
  こんにちは！宮若市AIコンシェルジュです！😆
  子育てや税金、宮若市に関する情報を勉強中です！
  お気軽にご質問ください！
  単語ではなく、会話のように質問していただくと、より具体的なご案内ができるかもしれません！
  <small>
    ※提供する情報はあくまで一般的なものです。手続き等の参考にしてください。なお、状況や条件により回答が異なる場合がありますので、詳細な情報が必要な場合は、担当部署にご確認ください。
  </small>
</p>
`

  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    // 設定 > チャットボット設定に遷移
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=チャットボット設定'))
  })

  test('レイアウト表示確認', async ({ page }) => {
    await expect(page.getByText('チャットボットの設定')).toBeVisible()
    await expect(page.getByText('チャットボットの基本設定を行います。')).toBeVisible()
    await expect(page.getByText('チャットボット名')).toBeVisible()
    await expect(page.locator('label', { hasText: '一次色' })).toBeVisible()
    await expect(page.getByText('対応言語')).toBeVisible()
    await expect(page.getByText('開始時メッセージ')).toBeVisible()
  })

  test('チャットボット名を変更できること', async ({ page }) => {
    const newName = 'autotest-宮前市'

    // チャットボット名のinputをname属性で特定
    const chatbotNameInput = page.locator('input[name="name"]')
    await expect(chatbotNameInput).toBeVisible()

    // 既存の名前をクリア
    await chatbotNameInput.fill('')

    // 新しい名前を入力
    await chatbotNameInput.fill(newName)

    // フォーカスアウトして保存トリガ（他の場所をクリック）
    await page.getByText('チャットボットの設定').click()

    // 保存完了まで少し待つ
    await page.waitForTimeout(3000)

    // 入力値がちゃんと入ってるか確認
    await expect(chatbotNameInput).toHaveValue(newName)
  })

  test('一次色変更できること', async ({ page }) => {
    const colorSelectButton = page.locator('label:has-text("一次色")')
      .locator('xpath=ancestor::div[contains(@class, "grid-cols-12")]')
      .locator('button')

    await expect(colorSelectButton).toBeVisible({ timeout: 5000 })

    await colorSelectButton.click()

    const listbox = page.getByRole('listbox')
    await expect(listbox).toBeVisible({ timeout: 5000 })

    const newColor = 'sky'
    await page.getByText(newColor, { exact: true }).click()

    // ボタンに新しい色が表示されることを確認
    await expect(colorSelectButton).toContainText(newColor)
  })

  test('対応言語を追加できること', async ({ page }) => {
    // 「言語追加」ボタンをクリック
    await page.getByText('言語追加', { exact: true }).click()

    // 追加モーダル or ドロップダウンが出るはずなので、例えば「スペイン語」を選ぶ
    await page.getByRole('option', { name: 'スペイン語' }).click()

    // 追加にちょっと時間かかるかもしれないので少し待つ
    await page.waitForTimeout(2000)

    // 対応言語一覧に「スペイン語」が追加されていることを確認
    await expect(page.getByText('スペイン語')).toBeVisible()
  })

  test('対応言語を削除できること', async ({ page }) => {
    // 削除したい言語（例：英語）の行を探す
    const languageRow = page.locator('li').filter({ hasText: 'スペイン語' })

    // その中の縦三点ボタンを押す
    await languageRow.locator('button').click()

    // 「言語削除」ボタンを押す
    await page.getByRole('menuitem', { name: '言語削除' }).click()

    // 削除処理に時間かかるので待つ
    await page.waitForTimeout(2000)

    // 英語がリストに存在しないことを確認
    await expect(page.getByText('スペイン語')).not.toBeVisible()
  })

  test('開始時メッセージを変更できること', async ({ page }) => {
    const editor = page.locator('.cm-content[contenteditable="true"]')

    await expect(editor).toBeVisible({ timeout: 5000 })

    // エディタをクリックしてフォーカス
    await editor.click()

    // 全選択してクリア
    await editor.press('Meta+A')
    await editor.press('Backspace')

    // 新しいメッセージを入力
    await editor.type(correctedMessage)

    // カーソルアウトして保存トリガ
    await page.getByText('チャットボットの設定').click()

    await page.waitForTimeout(3000)

    // 入力内容が反映されたか簡易チェック
    await expect(editor).toContainText('こんにちは！宮若市AIコンシェルジュです！')
  })

  test('開始時メッセージのプレビューが表示されること', async ({ page }) => {
    const previewButton = page.getByRole('button', { name: 'プレビュー' })

    await expect(previewButton).toBeVisible({ timeout: 5000 })
    await previewButton.click()

    // id="llm-rag-chatbot" の中にあるプレビューのメッセージを探す
    const previewMessage = page.locator('#llm-rag-chatbot').getByText('こんにちは！宮若市AIコンシェルジュです！😆')

    await expect(previewMessage).toBeVisible({ timeout: 5000 })

    // 内容を確認（キーワードチェック）
    await expect(previewMessage).toContainText('こんにちは！宮若市AIコンシェルジュです！😆')
    await expect(previewMessage).toContainText('子育てや税金、宮若市に関する情報を勉強中です！')
    await expect(previewMessage).toContainText('担当部署にご確認ください')
  })
})

import { test, expect } from '@playwright/test'
import { navigateAndWait, commonLogin, openConfirmPopup, clickRefreshButton } from '../utils/helpers'

let newLabel: string

test.describe.serial('ラベル管理機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    // 設定 > ラベル管理画面に遷移
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=ラベル管理'))
  })

  test.describe('ラベル管理画面 表示テスト', () => {
    test('画面要素が正しく表示されること', async ({ page }) => {
      await expect(page.getByText('ラベル一覧')).toBeVisible()
      await expect(page.getByRole('button', { name: 'ラベル追加' })).toBeVisible()
      await expect(page.getByPlaceholder('検索...')).toBeVisible()
      await expect(page.getByRole('columnheader', { name: 'ラベル' })).toBeVisible() // ヘッダー：ラベル列
      await expect(page.getByRole('columnheader', { name: 'キー' })).toBeVisible() // ヘッダー：キー列
      await expect(page.getByRole('columnheader', { name: '作成日時' })).toBeVisible() // ヘッダー：作成日時列
      await expect(page.getByRole('columnheader', { name: '更新日時' })).toBeVisible() // ヘッダー：更新日時列
      await expect(page.getByRole('columnheader', { name: '操作' })).toBeVisible() // ヘッダー：操作列
    })
  })

  test.describe('ラベル追加機能テスト', () => {
    test('ラベル追加ポップアップが表示されること', async ({ page }) => {
      await page.getByRole('button', { name: 'ラベル追加' }).click()

      await expect(page.getByText('新規ラベル作成')).toBeVisible()
      await expect(page.getByText('新しいラベルを作成します。')).toBeVisible()
      await expect(page.getByPlaceholder('例: 税金')).toBeVisible()
      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('ラベル追加時に必須項目未入力エラーが出ること', async ({ page }) => {
      await page.getByRole('button', { name: 'ラベル追加' }).click()
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText('ラベルが必要です。')).toBeVisible() // 正しいエラー文に合わせてください
    })

    test('ラベル追加ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'ラベル追加' }).click()
      await page.locator('button[aria-label="Close"]').click() // ✖️ボタンのaria-label

      // ポップアップが閉じたことを確認（タイトルがなくなったことを確認）
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ラベル追加ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'ラベル追加' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()

      // ポップアップが閉じたことを確認
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常にラベルを追加できること', async ({ page }) => {
      newLabel = `autotest-label-${Date.now()}`

      await page.getByRole('button', { name: 'ラベル追加' }).click()
      await page.getByPlaceholder('例: 税金').fill(newLabel)
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText(newLabel)).toBeVisible()
    })
  })

  test.describe('ラベル検索・ページングテスト', () => {
    test('存在しないラベルで検索するとデータなし表示', async ({ page }) => {
      await page.getByPlaceholder('検索...').fill('存在しないラベル名XYZ')
      await expect(page.getByText('データがありません')).toBeVisible()
    })

    test('存在するラベルで検索できること', async ({ page }) => {
      await page.getByPlaceholder('検索...').fill(newLabel)
      await expect(page.getByText(newLabel)).toBeVisible()
    })

    test('リフレッシュボタンでラベル一覧が更新されること', async ({ page }) => {
      await clickRefreshButton(page)
      await expect(page.getByText('ラベル一覧')).toBeVisible()
    })

    test('表示件数を変更できること', async ({ page }) => {
      await page.getByRole('combobox').selectOption('5')
      const rows = await page.locator('table tbody tr').count()
      expect(rows).toBeLessThanOrEqual(5)
    })

    test('ページ送りと戻る操作ができること', async ({ page }) => {
      // 表示件数を5件にしてページを増やす
      await page.getByRole('combobox').selectOption('5')

      const nextButton = page.locator('button[aria-label="Next"]')
      const prevButton = page.locator('button[aria-label="Prev"]')

      if (await nextButton.isDisabled()) {
        console.log('次ページボタンが無効です。データが1ページしかありません。')
      } else {
        // 次ページへ移動
        await nextButton.click()
        const activePageButton2 = page.locator('button.text-primary-500 span', { hasText: '2' })
        await expect(activePageButton2).toBeVisible()

        if (await prevButton.isDisabled()) {
          console.log('前ページボタンが無効です。データが1ページしかありません。')
        } else {
          // 前ページへ戻る
          await prevButton.click()
          const activePageButton1 = page.locator('button.text-primary-500 span', { hasText: '1' })
          await expect(activePageButton1).toBeVisible()
        }
      }
    })

    test('指定ページへ移動できること', async ({ page }) => {
      // 表示件数を5件に設定してページ数を増やす
      await page.getByRole('combobox').selectOption('5')

      const page2Button = page.getByRole('button', { name: '2', exact: true })

      if (await page2Button.count() === 0) {
        console.log('2ページ目ボタンが存在しません。データが1ページしかありません。')
      } else {
        await page2Button.click()

        const activePageButton = page.locator('button.text-primary-500 span', { hasText: '2' })
        await expect(activePageButton).toBeVisible()
      }
    })
  })

  test.describe('ラベル編集機能テスト', () => {
    test('ラベル編集ポップアップが表示されること', async ({ page }) => {
      await openConfirmPopup(page, newLabel, 'ラベル編集')

      await expect(page.getByText('ラベルを編集')).toBeVisible()
      await expect(page.getByText('ラベルの情報を編集します。')).toBeVisible()

      const labelInput = page.getByPlaceholder('例: 税金')

      // 編集ポップアップに追加したラベルが初期表示されているか確認
      await expect(labelInput).toHaveValue(newLabel)

      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('ラベル編集ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newLabel, 'ラベル編集')
      await page.locator('button[aria-label="Close"]').click()

      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ラベル編集ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newLabel, 'ラベル編集')
      await page.getByRole('button', { name: 'キャンセル' }).click()

      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常にラベルを編集できること', async ({ page }) => {
      await openConfirmPopup(page, newLabel, 'ラベル編集')

      const labelInput = page.getByPlaceholder('例: 税金')

      // 編集ポップアップに追加したラベルが初期表示されているか確認
      await expect(labelInput).toHaveValue(newLabel)

      // ラベルを編集
      const editedLabel = `auto-edited-${newLabel}`
      await labelInput.fill(editedLabel)
      await page.getByRole('button', { name: '確定' }).click()

      // 編集後のラベルが表示され、元のラベルが存在しないことを確認
      await expect(page.getByText(editedLabel)).toBeVisible()
    })

    test('ラベル編集時に必須項目未入力エラーが出ること', async ({ page }) => {
      await openConfirmPopup(page, newLabel, 'ラベル編集')

      const labelInput = page.getByPlaceholder('例: 税金')
      await labelInput.fill('') // クリア
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText('ラベルが必要です')).toBeVisible()
    })
  })

  test.describe('ラベル削除機能テスト', () => {
    test('ラベル削除ポップアップが表示されること', async ({ page }) => {
      const deletedLabel = `auto-edited-${newLabel}` // ★ テストの中で定義

      await openConfirmPopup(page, deletedLabel, 'ラベル削除')

      await expect(page.getByText('ラベル削除の確認')).toBeVisible()
      await expect(page.getByText(`ラベル「${deletedLabel}」を削除しますか？`)).toBeVisible()
      await expect(page.getByRole('button', { name: '削除' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('ラベル削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      const deletedLabel = `auto-edited-${newLabel}`

      await openConfirmPopup(page, deletedLabel, 'ラベル削除')
      await page.locator('button[aria-label="Close"]').click()

      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ラベル削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      const deletedLabel = `auto-edited-${newLabel}`

      await openConfirmPopup(page, deletedLabel, 'ラベル削除')
      await page.getByRole('button', { name: 'キャンセル' }).click()

      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常にラベルを削除できること', async ({ page }) => {
      const deletedLabel = `auto-edited-${newLabel}`

      await openConfirmPopup(page, deletedLabel, 'ラベル削除')

      await expect(page.getByText(`ラベル「${deletedLabel}」を削除しますか？`)).toBeVisible()

      await page.getByRole('button', { name: '削除' }).click()

      // 削除後、ラベルが一覧に存在しないことを確認
      await expect(page.getByText(deletedLabel)).not.toBeVisible()
    })
  })
})

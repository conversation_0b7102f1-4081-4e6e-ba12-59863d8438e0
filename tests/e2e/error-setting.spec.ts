import { test, expect } from '@playwright/test'
import { navigateAndWait, commonLogin } from '../utils/helpers'

const code = '101'
const message = 'これは自動追加テストです'

test.describe('エラーメッセージ設定画面テスト（新仕様対応）', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=エラーメッセージ'))
  })

  test('レイアウト表示確認', async ({ page }) => {
    await expect(page.getByText('エラーメッセージ設定')).toBeVisible()
    await expect(page.getByRole('button', { name: '新規エラーコード追加' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'デフォルト設定にリセット' })).toBeVisible()
    await page.waitForTimeout(1000) // ← 追加
    await expect(page.getByText('エラーメッセージ設定')).toBeVisible({ timeout: 10000 })
  })

  test.describe('新規エラーコード追加機能', () => {
    test('ポップアップ表示確認', async ({ page }) => {
      await page.getByRole('button', { name: '新規エラーコード追加' }).click()

      await expect(
        page.locator('[role="dialog"]').getByText('新規エラーコード追加')
      ).toBeVisible()

      await expect(page.getByText('新しいエラーコードとメッセージを追加します')).toBeVisible()

      await expect(
        page.locator('button').filter({ hasText: 'エラーコードを選択してください' })
      ).toBeVisible()

      await expect(
        page.locator('button').filter({ hasText: 'Current page: エディタ' }).nth(0)
      ).toBeVisible()

      await expect(
        page.getByRole('button', { name: 'プレビュー' })
      ).toBeVisible()

      await expect(
        page.locator('[aria-placeholder="開始時メッセージ..."]').nth(0)
      ).toBeVisible()
      
      await expect(page.getByRole('button', { name: '追加' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('必須項目未入力時にエラーメッセージ表示', async ({ page }) => {
      await page.getByRole('button', { name: '新規エラーコード追加' }).click()
      await page.getByRole('button', { name: '追加' }).click()
      await expect(page.getByText('エラーコードは必須です')).toBeVisible()
      await expect(page.getByText('メッセージは必須です')).toBeVisible()
    })

    test('プレビューボタンでメッセージが表示されること', async ({ page }) => {
      await page.getByRole('button', { name: '新規エラーコード追加' }).click()
    
      const dialog = page.locator('[role="dialog"]')
      const editor = dialog.locator('.cm-content').first()
      await editor.click()
      await editor.type('これはプレビューテスト用メッセージです')
    
      await dialog.getByRole('button', { name: 'プレビュー' }).click()
    
      await expect(dialog.getByText('これはプレビューテスト用メッセージです')).toBeVisible()
    })
    
    test('正常に新規エラーコードを追加できること', async ({ page }) => {
    
      await page.getByRole('button', { name: '新規エラーコード追加' }).click()
    
      const dialog = page.locator('[role="dialog"]')
    
      await dialog.locator('button').filter({ hasText: 'エラーコードを選択してください' }).click()
      await dialog.getByText(code, { exact: true }).click()
    
      const editor = dialog.locator('.cm-content').first()
      await editor.click()
      await editor.type(message)
    
      await dialog.getByRole('button', { name: '追加' }).click()
    
      // 追加された内容の確認（画面全体から検索でOK）
      const card = page.locator('div').filter({ hasText: `エラーコード: ${code}` })
      await expect(card.getByText(message)).toBeVisible()
    })

    test('ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: '新規エラーコード追加' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: '新規エラーコード追加' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })
  })

  test.describe('エラーメッセージ編集・プレビュー・リセット・削除機能', () => {
    const updatedMessage = 'これは自動編集されたテストメッセージです'

    test('エラーメッセージ変更が正しく保存されること', async ({ page }) => {
      const editor = page.locator(`xpath=//label[contains(., "エラーコード: ${code}")]/ancestor::div[contains(@class, "grid-cols-12")]//div[contains(@class, "cm-content")]`)

      await editor.click()
      await editor.press('Meta+A')
      await editor.press('Backspace')
      await editor.type(updatedMessage)

      // フォーカスアウトによる保存トリガ
      await page.getByText('エラーメッセージ設定').click()
      await page.waitForTimeout(3000)

      await expect(editor).toContainText(updatedMessage)
    })

    test('プレビューで編集後の内容が表示されること', async ({ page }) => {
      const previewButton = page.locator(`xpath=//label[contains(., "エラーコード: ${code}")]/ancestor::div[contains(@class, "grid-cols-12")]//button[contains(., "プレビュー")]`)
      await previewButton.scrollIntoViewIfNeeded()
      await previewButton.click()

      await expect(page.getByText(updatedMessage)).toBeVisible()
    })

    test('リセットポップアップが表示されること', async ({ page }) => {
      await page.getByRole('button', { name: 'デフォルト設定にリセット' }).click()
      await expect(page.getByText('すべてのエラーコードとメッセージをデフォルト設定にリセットしますか？')).toBeVisible()
      await expect(page.getByRole('button', { name: 'リセット' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('正常にリセットされること', async ({ page }) => {
      await page.getByRole('button', { name: 'デフォルト設定にリセット' }).click()
      await page.getByRole('button', { name: 'リセット' }).click()
      await page.waitForTimeout(3000)

      await expect(page.getByText(updatedMessage)).not.toBeVisible()
    })

    test('リセットポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'デフォルト設定にリセット' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('リセットポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'デフォルト設定にリセット' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    // 削除ポップアップが表示されること
    test('削除ポップアップが表示されること', async ({ page }) => {
      await page.getByRole('button', { name: '新規エラーコード追加' }).click()
    
      const dialog = page.locator('[role="dialog"]')
    
      await dialog.locator('button').filter({ hasText: 'エラーコードを選択してください' }).click()
      await dialog.getByText(code, { exact: true }).click()
    
      const editor = dialog.locator('.cm-content').first()
      await editor.click()
      await editor.type(message)
    
      await dialog.getByRole('button', { name: '追加' }).click()
      await page.waitForTimeout(5000)

      // エラーコード表示カード（"エラーコード: 101" を含む要素）を特定
      const card = page.locator('div.grid').filter({ hasText: `エラーコード: ${code}` }).first()
      await card.locator('button:has(.i-heroicons\\:trash)').click()

      const dialogDelete = page.locator('div[role="dialog"]')
      await expect(dialogDelete.getByText(`エラーコード「${code}」を削除しますか？`)).toBeVisible()
      await expect(dialogDelete.getByRole('button', { name: '削除' })).toBeVisible()
      await expect(dialogDelete.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    // 削除ポップアップを✖️ボタンで閉じられること
    test('削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      const card = page.locator('div.grid').filter({ hasText: `エラーコード: ${code}` }).first()
      await card.locator('button:has(.i-heroicons\\:trash)').click()

      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    // 削除ポップアップをキャンセルボタンで閉じられること
    test('削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      const card = page.locator('div.grid').filter({ hasText: `エラーコード: ${code}` }).first()
      await card.locator('button:has(.i-heroicons\\:trash)').click()

      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    // 正常に削除できること
    test('正常に削除できること', async ({ page }) => {
      const card = page.locator('div.grid').filter({ hasText: `エラーコード: ${code}` }).first()
      await card.locator('button:has(.i-heroicons\\:trash)').click()

      await page.getByRole('button', { name: '削除' }).click()

      // 確認：カードが画面からなくなること
      await expect(page.getByText(`エラーコード: ${code}`)).toHaveCount(0)
      await expect(page.getByText(message)).toHaveCount(0)
    })
  })
})

import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage,
  assertSortIcon
} from '../utils/helpers'

test.describe('インデックス更新履歴画面テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=インデックス更新履歴'))
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'インデックス更新履歴' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'ID' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'インデクサー名' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'アクション' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '結果' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '登録日時' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '更新日時' })).toBeVisible()
  })

  test('初期表示でインデックス更新履歴一覧が表示されること', async ({ page }) => {
    const rows = await page.locator('tbody tr').count()
    expect(rows).toBeGreaterThan(0)
  })

  test('表示件数を変更できること', async ({ page }) => {
    await changePageSize(page, '3')
  })

  test('ページ送りと戻る操作ができること', async ({ page }) => {
    // await moveToNextAndPreviousPage(page, '3', 'button:has(span.text-white)')
    await moveToNextAndPreviousPage(page, '3', 'button.text-white')
  })

  test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
    await moveToSpecificPage(page, '3', '2', 'button.text-white')
  })

  test('ID列を昇順ソートできること', async ({ page }) => {
    await assertSortIcon(page, 'ID', 'bars-arrow-up-20-solid', 1)
  })

  test('ID列を降順ソートできること', async ({ page }) => {
    await assertSortIcon(page, 'ID', 'bars-arrow-down-20-solid', 2)
  })
})

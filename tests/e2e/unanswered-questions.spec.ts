// tests/e2e/unanswered-question.spec.ts
import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait,
  selectDateRange,
  verifySearchResultDates,
  clickRefreshButton,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage,
  assertSortIcon
} from '../utils/helpers'

test.describe('未回答の質問画面テスト', () => {
  // const dateSelector = 'tbody tr td input'

  const dateSelector = 'tbody tr td:nth-child(5) input' // 質問日時列
  const sortColumnName = '質問日時'
  const activePageSelector = 'button:has(span.text-white)'

  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    // await navigateAndWait(page, () => page.click('text=統計'))
    await navigateAndWait(page, () => page.click('text=未回答の質問'))
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByRole('heading', { name: '未回答の質問' })).toBeVisible()

    // カレンダー表示内容の取得
    const calendarDisplay = await page.locator('button:has(div:has-text("年")) >> div').innerText()

    // 今日と2週間前の日付を取得して整形
    const today = new Date()
    const twoWeeksAgo = new Date()
    twoWeeksAgo.setDate(today.getDate() - 14)

    const formatDate = (d: Date) =>
      `${d.getFullYear()}年${String(d.getMonth() + 1).padStart(2, '0')}月${String(d.getDate()).padStart(2, '0')}日`

    const expectedRange = `${formatDate(twoWeeksAgo)} → ${formatDate(today)}`
    // console.log('🟢 calendarDisplay:', calendarDisplay)
    // console.log('🔴 expectedRange:', expectedRange)
    expect(calendarDisplay.trim()).toBe(expectedRange)

    // テーブルのカラム表示確認
    await expect(page.getByRole('columnheader', { name: 'セッションID' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'チャットID' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '質問', exact: true })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '質問日時' })).toBeVisible()
  })

  const dateLabels = ['最近の3日', '最近の7日', '最近の30日', '最近の90日', '最近の1年', '昨日']
  // const dateLabels = ['最近の30日']
  dateLabels.forEach((label) => {
    test(`${label} で検索できること`, async ({ page }) => {
      await page.locator('button:has(div:has-text("年"))').click()
      await page.getByRole('button', { name: label, exact: true }).click()
      await verifySearchResultDates(page, sortColumnName, dateSelector, 'iso', 'input')
    })
  })

  test('任意の期間で検索できること', async ({ page }) => {
    await selectDateRange(page)
    await verifySearchResultDates(page, sortColumnName, dateSelector, 'iso', 'input')
  })

  test('リフレッシュで一覧が更新されること', async ({ page }) => {
    await clickRefreshButton(page)
    await expect(page.getByRole('heading', { name: '未回答の質問' })).toBeVisible()
  })

  test('表示件数を変更できること', async ({ page }) => {
    await changePageSize(page, '3')
  })

  test('ページ送りと戻る操作ができること', async ({ page }) => {
    await moveToNextAndPreviousPage(page, '3', activePageSelector)
  })

  test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
    await moveToSpecificPage(page, '3', '2', activePageSelector)
  })

  test.describe('ソート機能の確認', () => {
    const columns = [
      { name: 'セッションID', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: 'チャットID', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '質問', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '質問日時', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' }
    ]

    for (const col of columns) {
      test(`${col.name} を昇順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.asc, 1)
      })

      test(`${col.name} を降順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.desc, 2)
      })
    }
  })

  test('CSV出力ボタンが動作すること', async ({ page }) => {
    // 任意の行を hover
    const row = page.locator('tbody tr').first()
    await row.hover()
    // 3点ボタンをクリック
    await row.locator('button').click()

    // CSV出力を監視
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      await page.getByRole('menuitem', { name: 'CSV出力' }).click()
    ])

    // ファイル名やパスを一応確認（使わなくてもOK）
    const path = await download.path()
    console.log('CSVがダウンロードされました: ', path)

    // 確認だけでよければ、ここで完了
    expect(download.suggestedFilename()).toMatch(/\.csv$/)
  })
})

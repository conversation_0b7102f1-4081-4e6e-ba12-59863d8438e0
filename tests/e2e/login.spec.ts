import { test, expect } from '@playwright/test'

test('ログイン画面が表示される', async ({ page }) => {
  await page.goto('/login')

  // 見出し確認
  // await expect(page.getByRole('heading', { name: 'おかえりなさい' })).toBeVisible()
  await expect(page.getByText('おかえりなさい')).toBeVisible()
  await expect(page.getByText('LLM RAG管理へログイン')).toBeVisible()

  // 入力欄の placeholder 確認
  await expect(page.getByPlaceholder('Ex: playnextlab')).toBeVisible() // テナントID
  await expect(page.getByPlaceholder('Ex: john.doe')).toBeVisible() // ユーザ名
  await expect(page.getByPlaceholder('パスワードを入力してください')).toBeVisible() // パスワード

  // リンク確認
  await expect(page.getByRole('link', { name: 'パスワードをお忘れですか？' })).toBeVisible()

  // ボタン確認
  await expect(page.getByRole('button', { name: 'ログイン' })).toBeVisible()
})

test.describe('パスワード目ボタン機能', () => {
  test('パスワードが表示できる', async ({ page }) => {
    await page.goto('/login')
    await page.fill('[placeholder="パスワードを入力してください"]', 'Pnl2024!')
    const eyeButton = page.locator('button:has(span.i-heroicons\\:eye)')
    await eyeButton.click()
    const passwordInput = page.getByPlaceholder('パスワードを入力してください')
    await expect(passwordInput).toHaveAttribute('type', 'text')
  })

  test('パスワードが非表示に戻る', async ({ page }) => {
    await page.goto('/login')
    await page.fill('[placeholder="パスワードを入力してください"]', 'testpassword')
    const eyeButton = page.locator('button:has(span.i-heroicons\\:eye)')
    await eyeButton.click() // 1回目押して表示する

    // 1回目押したあと、"eye-slash" ボタンを探しなおす
    const eyeSlashButton = page.locator('button:has(span.i-heroicons\\:eye-slash)')
    await eyeSlashButton.click() // 2回目押して非表示に戻す
    const passwordInput = page.getByPlaceholder('パスワードを入力してください')
    await expect(passwordInput).toHaveAttribute('type', 'password') // マスク状態確認
  })
})

test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
  await page.goto('/login')
  await page.getByRole('button', { name: 'ログイン' }).click()
  await expect(page.getByText('ユーザ名は必須です')).toBeVisible({ timeout: 10000 })
  await expect(page.getByText('パスワードは必須です')).toBeVisible({ timeout: 10000 })
})

test('存在しないアカウントでログイン試行', async ({ page }) => {
  await page.goto('/login')
  await page.getByPlaceholder('Ex: john.doe').fill('notfounduser')
  await page.getByPlaceholder('パスワードを入力してください').fill('Pnl2023!')
  await page.getByRole('button', { name: 'ログイン' }).click()
  // await expect(page.getByText('ログイン失敗')).toBeVisible()
  await expect(page.getByText('ログイン失敗')).toBeVisible({ timeout: 10000 })
})

test('初回ログイン（PNL管理者）パスワード設定画面に遷移', async ({ page }) => {
  await page.goto('/login')
  await page.getByPlaceholder('Ex: john.doe').fill('auto-test-for-initial-login')
  await page.getByPlaceholder('パスワードを入力してください').fill('Pnl2024!')
  await page.getByRole('button', { name: 'ログイン' }).click()
  await expect(page.getByText('パスワードの設定')).toBeVisible()
})

test('通常ログイン（PNL管理者）ダッシュボードに遷移', async ({ page }) => {
  await page.goto('/login')
  await page.getByPlaceholder('Ex: john.doe').fill('pnl_win_thandarlwin')
  await page.getByPlaceholder('パスワードを入力してください').fill('Pnl2024!')
  await page.getByRole('button', { name: 'ログイン' }).click()
  await expect(page.locator('h1 span:has-text("ダッシュボード")')).toBeVisible()
})

test('パスワードをお忘れですか？」リンク動作', async ({ page }) => {
  await page.goto('/login')
  await page.getByRole('link', { name: 'パスワードをお忘れですか？' }).click()
  await expect(page.getByText('パスワードを忘れた場合')).toBeVisible()
})

test('ログイン後にログイン画面アクセスするとダッシュボードにリダイレクトされる', async ({ page }) => {
  await page.goto('/login')
  await page.getByPlaceholder('Ex: john.doe').fill('pnl_win_thandarlwin')
  await page.getByPlaceholder('パスワードを入力してください').fill('Pnl2024!')
  await page.getByRole('button', { name: 'ログイン' }).click()

  // ちゃんとログインできてることを確認
  await expect(page.locator('h1 span:has-text("ダッシュボード")')).toBeVisible()

  // 再度 /login を開く
  await page.goto('/login')

  // 自動でリダイレクトされてまたダッシュボードにいるはず
  await expect(page.locator('h1 span:has-text("ダッシュボード")')).toBeVisible()
})

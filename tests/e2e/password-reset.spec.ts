import { test, expect } from '@playwright/test'

const username = 'pnl_win_thandarlwin'
const newPassword = 'Pnl2024!'
const wrongCode = '999999'
const nonExistentUser = 'nonexistent_user'

test.describe('パスワードリセット機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
    await page.getByRole('link', { name: 'パスワードをお忘れですか？' }).click()
  })

  test('「パスワードを忘れた場合」画面のレイアウト表示', async ({ page }) => {
    await expect(page.getByText('パスワードを忘れた場合')).toBeVisible()
    await expect(page.getByText('パスワードをリセットするためにメールアドレスにパスワードリセットリンクを送信します。')).toBeVisible()
    await expect(page.getByPlaceholder('Ex: username123')).toBeVisible()

    await expect(page.getByRole('button', { name: '送信' })).toBeVisible()
    await expect(page.getByRole('link', { name: 'ログイン画面に戻る' })).toBeVisible()
  })

  test('「パスワードを忘れた場合」画面からログイン画面に戻る', async ({ page }) => {
    await page.getByRole('link', { name: 'ログイン画面に戻る' }).click()

    await expect(page.getByText('おかえりなさい')).toBeVisible()
  })

  test('「パスワードリセット」画面からログイン画面に戻る', async ({ page }) => {
    await page.getByPlaceholder('Ex: username123').fill(nonExistentUser)
    await page.getByRole('button', { name: '送信' }).click()

    await page.getByRole('link', { name: 'ログイン画面に戻る' }).click()
    await expect(page.getByText('おかえりなさい')).toBeVisible()
  })

  test('存在しないユーザー名を入力した場合、メール送信されないこと', async ({ page }) => {
    await page.getByPlaceholder('Ex: username123').fill(nonExistentUser)
    await page.getByRole('button', { name: '送信' }).click()
    await expect(page.getByText('パスワードのリセット')).toBeVisible()
  })

  test('存在するユーザー名でコード送信できること', async ({ page }) => {
    await page.getByPlaceholder('Ex: username123').fill(username)
    await page.getByRole('button', { name: '送信' }).click()

    await expect(page.getByText('パスワードのリセット')).toBeVisible()
    await expect(page.getByText('パスワードをリセットするためにメールに送られたコードと新しいパスワードを入力してください。')).toBeVisible()
    await expect(page.getByPlaceholder('Ex: 123456')).toBeVisible()
    await expect(page.getByPlaceholder('新しいパスワードを入力してください')).toBeVisible()
    await expect(page.getByPlaceholder('新しいパスワードを再度入力してください')).toBeVisible()

    await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
    await expect(page.getByRole('link', { name: 'ログイン画面に戻る' })).toBeVisible()
  })

  test('パスワード入力欄の目ボタンで表示・非表示を切り替えできる', async ({ page }) => {
    await page.getByPlaceholder('Ex: username123').fill(nonExistentUser)
    await page.getByRole('button', { name: '送信' }).click()

    const passwordInput = page.getByPlaceholder('新しいパスワードを入力してください')
    await passwordInput.fill('TestPassword123!')

    // パスワード入力欄に対応する目ボタン（1個目）
    const passwordEyeButton = page.locator('button').nth(0) // 1番目のボタン
    await passwordEyeButton.waitFor()

    // (eye) をクリックして表示状態にする
    await passwordEyeButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'text')

    // (eye-slash) に切り替わったはずなので、再びクリックして非表示に戻す
    await passwordEyeButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'password')
  })

  test('パスワード確認欄の目ボタンで表示・非表示を切り替えできる', async ({ page }) => {
    await page.getByPlaceholder('Ex: username123').fill(nonExistentUser)
    await page.getByRole('button', { name: '送信' }).click()

    const confirmPasswordInput = page.getByPlaceholder('新しいパスワードを再度入力してください')
    await confirmPasswordInput.fill('TestPassword123!')

    // パスワード確認欄に対応する目ボタン（2個目）
    const confirmEyeButton = page.locator('button').nth(1) // 2番目のボタン
    await confirmEyeButton.waitFor()

    // (eye) をクリックして表示状態にする
    await confirmEyeButton.click()
    await expect(confirmPasswordInput).toHaveAttribute('type', 'text')

    // (eye-slash) に切り替わったはずなので、再びクリックして非表示に戻す
    await confirmEyeButton.click()
    await expect(confirmPasswordInput).toHaveAttribute('type', 'password')
  })

  test('必須項目未入力時にエラー表示されること', async ({ page }) => {
    await page.getByPlaceholder('Ex: username123').fill(nonExistentUser)
    await page.getByRole('button', { name: '送信' }).click()

    await page.getByRole('button', { name: '確定' }).click()

    await expect(page.getByText('コードは必須です')).toBeVisible()
    // await expect(page.getByText('パスワードは必須です')).toBeVisible()
    // await expect(page.getByText('パスワードの確認は必須です')).toBeVisible()
    await expect(page.locator('text=パスワードの確認は必須です')).toHaveCount(2)
  })

  test('パスワード不一致時のエラーメッセージ表示', async ({ page }) => {
    await page.getByPlaceholder('Ex: username123').fill(nonExistentUser)
    await page.getByRole('button', { name: '送信' }).click()

    await page.getByPlaceholder('新しいパスワードを入力してください').fill('TestPassword123!')
    await page.getByPlaceholder('新しいパスワードを再度入力してください').fill('DifferentPassword!')
    await page.getByRole('button', { name: '確定' }).click()
    await expect(page.getByText('パスワードが一致しません')).toBeVisible()
  })

  test('確認コードが誤っているときエラーメッセージが表示されること', async ({ page }) => {
    await page.getByPlaceholder('Ex: username123').fill(username)
    await page.getByRole('button', { name: '送信' }).click()

    await page.getByPlaceholder('新しいパスワードを入力してください').fill(newPassword)
    await page.getByPlaceholder('新しいパスワードを再度入力してください').fill(newPassword)
    await page.getByPlaceholder('Ex: 123456').fill(wrongCode)
    await page.getByRole('button', { name: '確定' }).click()
    await expect(page.getByText('パスワード変更失敗')).toBeVisible()
  })
})

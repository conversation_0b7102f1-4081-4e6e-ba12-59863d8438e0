import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait,
  verifyLogItemsInRange,
  selectDateRange
} from '../utils/helpers'

test.describe('ログ情報画面テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=ログ情報'))
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'ログ情報' })).toBeVisible()

    const calendarButton = page.locator('button', { hasText: '→' }).first()
    const calendarDisplay = await calendarButton.locator('div').innerText()

    const today = new Date()
    const twoWeeksAgo = new Date()
    twoWeeksAgo.setDate(today.getDate() - 3) // ← 7日だけ遡る設定

    const formatDate = (d: Date) =>
      `${d.getMonth() + 1}月${d.getDate()}日` // ← ゼロ埋めを除外

    const expectedRange = `${formatDate(twoWeeksAgo)} → ${formatDate(today)}`
    // console.log('🟢 calendarDisplay:', calendarDisplay)
    // console.log('🔴 expectedRange:', expectedRange)
    expect(calendarDisplay.trim()).toBe(expectedRange)

    await expect(page.getByPlaceholder('キーワード検索...')).toBeVisible()
    // strict violation を防ぐ
    await expect(page.locator('text=CSV出力')).toBeVisible()
  })

  const dateLabels = ['最近の3日', '最近の7日', '最近の30日', '最近の90日', '今日']
  // 検索結果の日付が「MM/DD」形式で年の情報がないため、
  // 直近1年以内かどうかを判別できず、自動化ができません。
  dateLabels.forEach((label) => {
    test(`${label} で検索できること`, async ({ page }) => {
      await page.locator('button', { hasText: '→' }).first().click()
      // await page.locator('button:has(div:has-text("年"))').click()
      await page.getByRole('button', { name: label, exact: true }).click()
      await verifyLogItemsInRange(page)
    })
  })

  test('任意の期間で検索できること', async ({ page }) => {
    await selectDateRange(page, 'button:has-text("→")') // ← カスタムセレクタを渡す
    await verifyLogItemsInRange(page)
  })

  test('存在しないキーワードで「データがありません」が表示される', async ({ page }) => {
    await page.getByPlaceholder('キーワード検索...').fill('存在しないキーワード12345')
    await page.keyboard.press('Enter')
    await expect(page.getByText('ログが見つかりませんでした。', { exact: true })).toBeVisible()
  })

  test('部分一致キーワード検索で結果が表示される', async ({ page }) => {
    await page.getByPlaceholder('キーワード検索...').fill('プレイネクストラボ')
    await page.keyboard.press('Enter')
    const count = await page.locator('.vue-recycle-scroller__item-view div.min-w-16').count()
    expect(count).toBeGreaterThan(0)
  })

  test('メッセージ単位でログが表示される', async ({ page }) => {
    await page.locator('button:has-text("メッセージ単位")').click()
    await expect(page.locator('button:has-text("メッセージ単位")')).toHaveAttribute('aria-selected', 'true')
  })

  test('セッション単位でログが表示される', async ({ page }) => {
    await page.locator('button:has-text("セッション単位")').click()
    await expect(page.locator('button:has-text("セッション単位")')).toHaveAttribute('aria-selected', 'true')
  })

  test('ログ詳細が正しく表示される', async ({ page }) => {
    const row = page.locator('.vue-recycle-scroller__item-view').first()
    await row.click()

    const detailPanel = page.locator('.flex-col.items-stretch.relative.w-full.flex-1')

    await expect(detailPanel.getByText(/リクエストID:/)).toBeVisible()
    await expect(detailPanel.getByText('ユーザ')).toBeVisible()
    await expect(detailPanel.getByText('チャットボット')).toBeVisible()
  })
})

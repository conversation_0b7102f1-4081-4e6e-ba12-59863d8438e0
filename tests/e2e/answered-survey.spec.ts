import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait,
  assertSortIcon,
  clickRefreshButton,
  selectDateRange,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage,
  verifySearchResultDates
} from '../utils/helpers'

test.describe('回答後のアンケート画面テスト', () => {
  const dateSelector = 'tbody td:nth-child(5)' // 1始まり、5列目
  const sortColumnName = 'アンケート回答日時'
  const activePageSelector = 'button:has(span.text-white)'

  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    // await navigateAndWait(page, () => page.click('text=統計'))
    await navigateAndWait(page, () => page.click('text=回答後のアンケート'))
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByRole('heading', { name: '回答後のアンケート' })).toBeVisible()
    const calendarDisplay = await page.locator('button:has(div:has-text("年")) >> div').innerText()

    // 今日と2週間前の日付を取得して整形
    const today = new Date()
    const twoWeeksAgo = new Date()
    twoWeeksAgo.setDate(today.getDate() - 14)

    const formatDate = (d: Date) =>
      `${d.getFullYear()}年${String(d.getMonth() + 1).padStart(2, '0')}月${String(d.getDate()).padStart(2, '0')}日`

    const expectedRange = `${formatDate(twoWeeksAgo)} → ${formatDate(today)}`
    expect(calendarDisplay.trim()).toBe(expectedRange)

    const headers = ['ID', '質問', '回答', '選択肢', 'アンケート回答日時']
    for (const header of headers) {
      await expect(page.getByRole('columnheader', { name: header, exact: true })).toBeVisible()
    }
  })

  const dateLabels = ['最近の3日', '最近の7日', '最近の30日', '最近の90日', '最近の1年', '昨日']
  for (const label of dateLabels) {
    test(`${label} で検索できること`, async ({ page }) => {
      await page.locator('button:has(div:has-text("年"))').click()
      await page.getByRole('button', { name: label, exact: true }).click()
      await verifySearchResultDates(page, sortColumnName, dateSelector, 'iso')
    })
  }

  test('任意の期間で検索できること', async ({ page }) => {
    await selectDateRange(page)
    await verifySearchResultDates(page, sortColumnName, dateSelector, 'iso')
  })

  test('リフレッシュボタンで再表示されること', async ({ page }) => {
    await clickRefreshButton(page)
    await expect(page.locator('tbody')).toBeVisible()
  })

  test('表示件数を変更できること', async ({ page }) => {
    await changePageSize(page, '3')
  })

  test('ページ送りと戻る操作ができること', async ({ page }) => {
    await moveToNextAndPreviousPage(page, '3', activePageSelector)
  })

  test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
    await moveToSpecificPage(page, '3', '2', activePageSelector)
  })

  test.describe('ソート機能の確認', () => {
    const columns = [
      { name: 'ID', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '質問', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '回答', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '選択肢', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: 'アンケート回答日時', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' }
    ]

    for (const col of columns) {
      test(`${col.name} を昇順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.asc, 1)
      })

      test(`${col.name} を降順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.desc, 2)
      })
    }
  })

  const rates = ['100%', '80%', '60%', '40%', '20%']

  for (const rate of rates) {
    test(`${rate} で絞り込んで検索できること`, async ({ page }) => {
    // 評価ドロップダウンを開く
      await page.locator('div[role="button"]', { hasText: '評価' }).first().click()

      // オプションから指定の評価を選択
      await page.getByRole('option', { name: rate, exact: true }).click()

      // 少し待ってデータのロードを待機
      await page.waitForTimeout(3000)

      // 検索結果の「データがありません」判定
      const noDataLocator = page.locator('td', { hasText: 'データがありません' })
      if (await noDataLocator.isVisible()) {
        console.warn(`⚠️ 評価 ${rate} に該当するデータがありません。スキップします。`)
        return
      }

      // 検索結果の行を取得
      const rows = page.locator('tbody tr')
      const rowCount = await rows.count()

      for (let i = 0; i < rowCount; i++) {
        const cell = rows.nth(i).locator('td').nth(3) // 「選択肢」は4列目（index 3）
        await expect(cell).toHaveText(rate)
      }
    })
  }
})

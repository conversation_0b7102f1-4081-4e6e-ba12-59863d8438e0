import { test, expect } from '@playwright/test'
import { navigateAndWait, commonLogin } from '../utils/helpers'

const errorMessages = [
  { code: 1001, message: '<div>申し訳ありません。現在リクエストを処理できません。しばらくしてから、もう一度お試しください。</div>' },
  { code: 1002, message: '会話が長くなってきましたね。いったんリセットしますので、再度お尋ねしてもらえますか？' },
  { code: 8001, message: 'すみませんが、その質問は答えづらいですね。他におたずねはありますか？' },
  { code: 8002, message: '一度にたくさん質問されると説明しづらいです。一つずつ聞いてもらえますか？' },
  { code: 8003, message: '質問内容が確認できませんでした。もう一度お願いします。' },
  { code: 8004, message: '不明なリクエストを受信しました。他にご質問はありますか？' },
  { code: 9990, message: 'すみません。もう一度、お尋ねになりたいことをうかがっていいですか？' },
  { code: 9991, message: 'もう一度お尋ねください。お待ちしています。' },
  { code: 9992, message: 'ごめんなさい、もう一度お伺いしてもいいでしょうか？' },
  { code: 9994, message: '恐れ入りますが、質問をもう一度お願いします。' },
  { code: 9998, message: 'すみません、質問内容を再送してもらえますか？' },
  { code: 9999, message: 'もう一度だけ、質問を聞かせてください。' }
]

test.describe('エラーメッセージ設定画面テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    // 設定 > エラーメッセージ画面に遷移
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=エラーメッセージ'))
  })

  test('レイアウト表示確認', async ({ page }) => {
    await expect(page.getByText('エラーメッセージ設定')).toBeVisible()
    await expect(page.getByText('エラー発生時に表示されるメッセージの設定を行います。')).toBeVisible()
  })

  for (const { code, message } of errorMessages) {
    test(`エラーコード ${code} にメッセージを入力して保存できること`, async ({ page }) => {
      // 🛠️ エディタを正しく特定（エラーコード: 1001）→ そこから下にスクロールして contenteditable を探す
      const editorContainer = page.locator(`xpath=//label[contains(., "エラーコード: ${code}")]/ancestor::div[contains(@class, "grid-cols-12")]//div[contains(@class, "cm-content")]`)

      await expect(editorContainer).toBeVisible({ timeout: 5000 })

      // エディタをクリックしてフォーカス
      await editorContainer.click()

      // 全選択してクリア
      await editorContainer.press('Meta+A')
      await editorContainer.press('Backspace')

      // 新しいエラーメッセージを入力
      await editorContainer.type(message)

      // カーソルアウトして保存トリガ
      await page.getByText('エラーメッセージ設定').click()

      // 保存に時間かかるので待機
      await page.waitForTimeout(3000)

      // 入力した内容を確認
      await expect(editorContainer).toContainText(message.replace(/<[^>]*>/g, ''))
    })

    test(`エラーコード ${code} のプレビュー機能確認`, async ({ page }) => {
      const previewButton = page.locator(`xpath=//label[contains(., "エラーコード: ${code}")]/ancestor::div[contains(@class, "grid-cols-12")]//button[contains(., "プレビュー")]`)

      // プレビューボタンが画面に表示されるまで待つ
      await expect(previewButton).toBeVisible({ timeout: 5000 })

      // プレビューボタンをスクロールして、画面内に持ってくる
      await previewButton.scrollIntoViewIfNeeded()

      // ボタンが押せる状態になるまで待機
      await expect(previewButton).toBeEnabled({ timeout: 5000 })

      // プレビューボタンを押す
      await previewButton.click()

      // プレビューの中のメッセージを探す
      const previewMessage = page.locator(`text=${message.replace(/<[^>]*>/g, '')}`)

      await expect(previewMessage).toBeVisible({ timeout: 5000 })

      // メッセージの一部が表示されていることを確認
      await expect(previewMessage).toContainText(message.replace(/<[^>]*>/g, ''))
    })
  }
})

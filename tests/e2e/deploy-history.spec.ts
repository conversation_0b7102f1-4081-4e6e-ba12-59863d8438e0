import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait,
  clickRefreshButton,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage,
  assertSortIcon,
  verifySearchResultDates,
  selectDateRange
} from '../utils/helpers'

const sortColumnName = '作成日時'
const dateSelector = 'tbody td:nth-child(3) > div > div:first-child'

test.describe('デプロイ履歴画面テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=デプロイ履歴'))
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'デプロイ履歴' })).toBeVisible()

    const calendarDisplay = await page.locator('button:has(div:has-text("年")) >> div').innerText()
    const today = new Date()
    const yyyy = today.getFullYear()
    const mm = String(today.getMonth() + 1).padStart(2, '0')
    const dd = String(today.getDate()).padStart(2, '0')
    const todayStr = `${yyyy}年${mm}月${dd}日 → ${yyyy}年${mm}月${dd}日`

    expect(calendarDisplay.trim()).toBe(todayStr)

    await expect(page.getByRole('columnheader', { name: '種別' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '作成日時' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '内容' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '開始・終了日時' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'ステータス' })).toBeVisible()
  })

  const dateLabels = ['最近の3日', '最近の7日', '最近の30日', '最近の90日', '最近の1年', '今日']
  // const dateLabels = ['最近の30日']
  dateLabels.forEach((label) => {
    test(`${label} で検索できること`, async ({ page }) => {
      await page.locator('button:has(div:has-text("年"))').click()
      await page.getByRole('button', { name: label, exact: true }).click()
      await verifySearchResultDates(page, sortColumnName, dateSelector)
    })
  })

  test('任意の期間で検索できること', async ({ page }) => {
    await selectDateRange(page)
    await verifySearchResultDates(page, sortColumnName, dateSelector)
  })

  test('リフレッシュ動作確認', async ({ page }) => {
    await clickRefreshButton(page)
    await expect(page.getByRole('heading', { name: 'デプロイ履歴' })).toBeVisible()
  })

  test('表示件数変更', async ({ page }) => {
    await changePageSize(page, '3')
  })

  test('ページング（次・前ページ）操作確認', async ({ page }) => {
    await moveToNextAndPreviousPage(page, '3')
  })

  test('ページング（指定ページ）操作確認', async ({ page }) => {
    await moveToSpecificPage(page, '3', '2')
  })

  test.describe('ソート機能の確認', () => {
    const columns = [
      { name: '種別', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '作成日時', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '内容', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '開始・終了日時', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: 'ステータス', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' }
    ]

    for (const col of columns) {
      test(`${col.name} を昇順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.asc, 1)
      })

      test(`${col.name} を降順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.desc, 2)
      })
    }
  })

  test('行の展開・折りたたみが正常に動作すること', async ({ page }) => {
    const firstToggleButton = page.locator('tbody tr >> nth=0').locator('button').first()
    const icon = firstToggleButton.locator('span[class*="i-heroicons:chevron-down"]')

    // ▼ 初期状態の down アイコンが表示されていること
    await expect(icon).toBeVisible()

    // ▼ 展開
    await firstToggleButton.click()
    await page.waitForTimeout(300)

    // 詳細行が表示されること
    const detailRow = page.locator('pre', {
      hasText: /Success knowledge count = \d+, Failed knowledge count = \d+/
    })
    await expect(detailRow).toBeVisible()

    // ▲ 折りたたみ
    await firstToggleButton.click()
    await page.waitForTimeout(300)

    // 詳細行が非表示になっていること
    await expect(detailRow).toHaveCount(0)
  })
})

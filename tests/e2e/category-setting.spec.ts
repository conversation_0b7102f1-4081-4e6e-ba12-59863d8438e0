import { test, expect } from '@playwright/test'
import { navigateAndWait, commonLogin, openConfirmPopup, clickRefreshButton } from '../utils/helpers'

let newCategory: string

test.describe.serial('カテゴリ管理機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=カテゴリ管理'))
  })

  test.describe('カテゴリ管理画面 表示テスト', () => {
    test('画面要素が正しく表示されること', async ({ page }) => {
      await expect(page.getByText('カテゴリ一覧')).toBeVisible()
      await expect(page.getByRole('button', { name: 'カテゴリ追加' })).toBeVisible()
      await expect(page.getByPlaceholder('検索...')).toBeVisible()
      await expect(page.getByRole('columnheader', { name: 'カテゴリ' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '作成日時' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '更新日時' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '操作' })).toBeVisible()
    })
  })

  test.describe('カテゴリ追加機能テスト', () => {
    test('カテゴリ追加ポップアップが表示されること', async ({ page }) => {
      await page.getByRole('button', { name: 'カテゴリ追加' }).click()
      await expect(page.getByText('新規カテゴリ作成')).toBeVisible()
      await expect(page.getByText('新しいカテゴリを作成します。')).toBeVisible()
      await expect(page.getByPlaceholder('例: 税金')).toBeVisible()
      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('カテゴリ追加ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'カテゴリ追加' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('カテゴリ追加ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'カテゴリ追加' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('必須項目未入力エラーが表示されること', async ({ page }) => {
      await page.getByRole('button', { name: 'カテゴリ追加' }).click()
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText('カテゴリが必要です。')).toBeVisible()
    })

    test('正常にカテゴリを追加できること', async ({ page }) => {
      newCategory = `autotest-category-${Date.now()}`
      await page.getByRole('button', { name: 'カテゴリ追加' }).click()
      await page.getByPlaceholder('例: 税金').fill(newCategory)
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText(newCategory)).toBeVisible()
    })
  })

  test.describe('カテゴリ検索・ページングテスト', () => {
    test('存在しないカテゴリで検索するとデータなし表示', async ({ page }) => {
      await page.getByPlaceholder('検索...').fill('存在しないカテゴリXYZ')
      await expect(page.getByText('データがありません')).toBeVisible()
    })

    test('部分一致で検索できること', async ({ page }) => {
      await page.getByPlaceholder('検索...').fill(newCategory)
      await expect(page.getByText(newCategory)).toBeVisible()
    })

    test('リフレッシュボタンでカテゴリ一覧が更新されること', async ({ page }) => {
      await clickRefreshButton(page)
      await expect(page.getByText('カテゴリ一覧')).toBeVisible()
    })

    test('表示件数を変更できること', async ({ page }) => {
      await page.getByRole('combobox').selectOption('5')
      const rows = await page.locator('table tbody tr').count()
      expect(rows).toBeLessThanOrEqual(5)
    })

    test('ページ送りと戻る操作ができること', async ({ page }) => {
      // 表示件数を5件にしてページを増やす
      await page.getByRole('combobox').selectOption('5')

      const nextButton = page.locator('button[aria-label="Next"]')
      const prevButton = page.locator('button[aria-label="Prev"]')

      if (await nextButton.isDisabled()) {
        console.log('次ページボタンが無効です。データが1ページしかありません。')
      } else {
        // 次ページへ移動
        await nextButton.click()
        const activePageButton2 = page.locator('button.text-primary-500 span', { hasText: '2' })
        await expect(activePageButton2).toBeVisible()

        if (await prevButton.isDisabled()) {
          console.log('前ページボタンが無効です。データが1ページしかありません。')
        } else {
          // 前ページへ戻る
          await prevButton.click()
          const activePageButton1 = page.locator('button.text-primary-500 span', { hasText: '1' })
          await expect(activePageButton1).toBeVisible()
        }
      }
    })

    test('指定ページへ移動できること', async ({ page }) => {
      // 表示件数を5件に設定してページ数を増やす
      await page.getByRole('combobox').selectOption('5')

      const page2Button = page.getByRole('button', { name: '2', exact: true })

      if (await page2Button.count() === 0) {
        console.log('2ページ目ボタンが存在しません。データが1ページしかありません。')
      } else {
        await page2Button.click()

        const activePageButton = page.locator('button.text-primary-500 span', { hasText: '2' })
        await expect(activePageButton).toBeVisible()
      }
    })
  })

  test.describe('カテゴリ編集機能テスト', () => {
    test('カテゴリ編集ポップアップが表示されること', async ({ page }) => {
      await openConfirmPopup(page, newCategory, 'カテゴリ編集')

      await expect(page.getByText('カテゴリを編集')).toBeVisible()
      await expect(page.getByPlaceholder('例: 税金')).toHaveValue(newCategory)

      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('カテゴリ編集ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newCategory, 'カテゴリ編集')

      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('カテゴリ編集ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newCategory, 'カテゴリ編集')

      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('カテゴリ編集時に必須項目未入力エラーが表示されること', async ({ page }) => {
      await openConfirmPopup(page, newCategory, 'カテゴリ編集')

      const categoryInput = page.getByPlaceholder('例: 税金')
      await categoryInput.fill('')
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText('カテゴリが必要です。')).toBeVisible()
    })

    test('正常にカテゴリを編集できること', async ({ page }) => {
      await openConfirmPopup(page, newCategory, 'カテゴリ編集')

      const categoryInput = page.getByPlaceholder('例: 税金')
      const editedCategory = `auto-edited-${newCategory}`
      await categoryInput.fill(editedCategory)
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText(editedCategory)).toBeVisible()
    })
  })

  test.describe('カテゴリ削除機能テスト', () => {
    test('カテゴリ削除ポップアップが表示されること', async ({ page }) => {
      const deletedCategory = `auto-edited-${newCategory}`

      await openConfirmPopup(page, deletedCategory, 'カテゴリ削除')

      await expect(page.getByText('カテゴリ削除の確認')).toBeVisible()
      await expect(page.getByText(`カテゴリ「${deletedCategory}」を削除しますか？`)).toBeVisible()
      await expect(page.getByRole('button', { name: '削除' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('カテゴリ削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      const deletedCategory = `auto-edited-${newCategory}`
      await openConfirmPopup(page, deletedCategory, 'カテゴリ削除')
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('カテゴリ削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      const deletedCategory = `auto-edited-${newCategory}`
      await openConfirmPopup(page, deletedCategory, 'カテゴリ削除')
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常にカテゴリを削除できること', async ({ page }) => {
      const deletedCategory = `auto-edited-${newCategory}`
      await openConfirmPopup(page, deletedCategory, 'カテゴリ削除')
      await page.getByRole('button', { name: '削除' }).click()
      await expect(page.getByText(deletedCategory)).not.toBeVisible()
    })
  })
})

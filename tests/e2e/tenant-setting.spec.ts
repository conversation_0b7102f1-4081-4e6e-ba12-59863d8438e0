import { test, expect } from '@playwright/test'
import { navigateAndWait, commonLogin } from '../utils/helpers'

let newTenantId: string
let newTenantName: string
let editedTenantName: string

test.describe('テナント管理機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    // 設定 > テナント管理画面に遷移
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=テナント管理'))
  })

  test.describe('テナント管理画面 表示テスト', () => {
    test('画面要素が正しく表示されること', async ({ page }) => {
      await expect(page.getByText('テナント一覧')).toBeVisible()
      await expect(page.getByText('テナントの一覧を表示します。')).toBeVisible()
      await expect(page.getByRole('button', { name: '新規作成' })).toBeVisible()
    })
  })

  test.describe('テナント追加機能テスト', () => {
    test('テナント追加ポップアップが表示されること', async ({ page }) => {
      await page.getByRole('button', { name: '新規作成' }).click()

      await expect(page.getByText('新規テナント作成')).toBeVisible()
      await expect(page.getByText('新しいテナントを作成します。')).toBeVisible()
      await expect(page.getByPlaceholder('例: playnextlab')).toBeVisible()
      await expect(page.getByPlaceholder('例: プレネクストラボ')).toBeVisible()
      // カラーテーマ確認（role="button" かつ "sky" が表示されている）
      await expect(page.locator('button:has-text("sky")')).toBeVisible()

      // ステータス確認（role="switch" が存在すること）
      await expect(page.getByRole('switch')).toBeVisible()

      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('テナント追加ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: '新規作成' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.getByText('新規テナント作成')).not.toBeVisible()
    })

    test('テナント追加ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: '新規作成' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.getByText('新規テナント作成')).not.toBeVisible()
    })

    test('必須項目未入力時にエラーメッセージが表示されること', async ({ page }) => {
      await page.getByRole('button', { name: '新規作成' }).click()
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText('IDが必要です。')).toBeVisible()
      // await expect(page.getByText('テナント名が必要です。')).toBeVisible()
    })

    test('正常にテナントを追加できること', async ({ page }) => {
      const now = new Date()
      const hhmmss = now.toTimeString().slice(0, 8).replace(/:/g, '') // "141503" みたいな文字列

      newTenantId = `autotest-${hhmmss}`
      newTenantName = `オートテスト-${hhmmss}`

      await page.getByRole('button', { name: '新規作成' }).click()
      await page.getByPlaceholder('例: playnextlab').fill(newTenantId)
      await page.getByPlaceholder('例: プレネクストラボ').fill(newTenantName)
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText(newTenantName)).toBeVisible({ timeout: 50000 })
    })
  })

  test.describe('テナント編集機能テスト', () => {
    test('テナント編集ポップアップが表示されること', async ({ page }) => {
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: newTenantName })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント編集' }).click()

      await expect(page.getByText('テナントを編集')).toBeVisible()
      await expect(page.getByText('テナントの情報を編集します。')).toBeVisible()
      await expect(page.getByPlaceholder('例: playnextlab')).toHaveValue(newTenantId)
      await expect(page.getByPlaceholder('例: プレネクストラボ')).toHaveValue(newTenantName)

      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('テナント編集ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: newTenantName })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント編集' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('テナント編集ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: newTenantName })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント編集' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('必須項目未入力時にエラーメッセージが表示されること', async ({ page }) => {
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: newTenantName })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント編集' }).click()

      const tenantNameInput = page.getByPlaceholder('例: プレネクストラボ')
      await tenantNameInput.fill('')
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText('テナント名が必要です。')).toBeVisible()
    })

    test('正常にテナントを編集できること', async ({ page }) => {
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: newTenantName })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント編集' }).click()

      editedTenantName = `auto-edited-${newTenantName}`
      const tenantNameInput = page.getByPlaceholder('例: プレネクストラボ')
      await tenantNameInput.fill(editedTenantName)
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText(editedTenantName)).toBeVisible()
    })
  })

  test.describe('テナント削除機能テスト', () => {
    test('テナント削除ポップアップが表示されること', async ({ page }) => {
      const deletedTenant = `auto-edited-${newTenantName}`
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: deletedTenant })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント削除' }).click()

      await expect(page.getByText('テナント削除の確認')).toBeVisible()
      await expect(page.getByText(`テナント「${deletedTenant}」を削除しますか？`)).toBeVisible()
      await expect(page.getByRole('button', { name: '削除' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('テナント削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      const deletedTenant = `auto-edited-${newTenantName}`
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: deletedTenant })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント削除' }).click()

      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('テナント削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      const deletedTenant = `auto-edited-${newTenantName}`
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: deletedTenant })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント削除' }).click()

      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常にテナントを削除できること', async ({ page }) => {
      const deletedTenant = `auto-edited-${newTenantName}`
      const row = page.locator('li', { hasText: newTenantId }).filter({ hasText: deletedTenant })
      await row.locator('button').first().click()
      await page.getByRole('menuitem', { name: 'テナント削除' }).click()

      await page.getByRole('button', { name: '削除' }).click()

      // 削除後、一覧にそのテナントが存在しないことを確認
      await expect(page.getByText(deletedTenant)).not.toBeVisible()
    })
  })
})

import { test, expect } from '@playwright/test'
import { navigateAndWait, commonLogin } from '../utils/helpers'

const fieldLabels = [
  '最大ターン数',
  '許容質問の最大数',
  'ゲストのアクセス数上限',
  'ゲストのセッション数上限',
  '使用ナレッジの最大数',
  'ナレッジの最低信頼スコア値',
  'ナレッジの理想信頼スコア値'
]

const toggleLabels = [
  '入力クエリのブラックリスト分析の有効化',
  '翻訳の有効化',
  'ウェブ検索の有効化',
  'ゲストの有効化',
  'RAGキャッシュの有効化'
]

test.describe('RAG設定画面テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.getByText('RAG設定管理', { exact: true }).click())
  })

  test('レイアウトが正しく表示されること', async ({ page }) => {
    await expect(page.getByText('RAGの設定', { exact: true })).toBeVisible()
    await expect(page.getByText('チャットボットのRAG設定を行います。')).toBeVisible()

    for (const label of fieldLabels) {
      await expect(page.getByLabel(label)).toBeVisible()
    }

    for (const label of toggleLabels) {
      await expect(page.getByLabel(label)).toBeVisible()
    }

    await expect(page.getByRole('button', { name: 'リセット' })).toBeVisible()
    await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
  })

  test('必須項目が未入力の場合、各フィールドにエラーメッセージが表示されること', async ({ page }) => {
    for (const label of fieldLabels) {
      const input = page.getByLabel(label)
      await input.fill('')
    }

    await page.getByRole('button', { name: '確定' }).click()

    for (const label of fieldLabels) {
      const input = page.getByLabel(label)
      // .locator('..') で input を含む div を取得 → さらにその親 (col-span-7) に移動して p タグを探す
      const wrapper = input.locator('..').locator('..')
      const errorMsg = wrapper.locator('p:text("数値で入力してください。")')
      await expect(errorMsg).toBeVisible()
    }
  })

  test('入力値をリセットできること', async ({ page }) => {
    for (const label of fieldLabels) {
      const input = page.getByLabel(label)
      await input.fill('99')
    }

    for (const label of toggleLabels) {
      const toggle = page.getByLabel(label)
      await toggle.click()
    }

    await page.getByRole('button', { name: 'リセット' }).click()

    for (const label of fieldLabels) {
      const input = page.getByLabel(label)
      await expect(input).not.toHaveValue('99')
    }
  })

  test('確定ボタンで保存できること', async ({ page }) => {
    await page.getByRole('button', { name: '確定' }).click()

    await expect(page.getByText('設定を更新しますか？')).toBeVisible({ timeout: 3000 })

    const confirmDialog = page.getByRole('dialog')
    await confirmDialog.getByRole('button', { name: '更新' }).click()
    await page.waitForTimeout(5000) // UI反映待ち

    // 保存成功後、モーダルが閉じたことの確認
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('キャンセルボタンでポップアップを閉じられること', async ({ page }) => {
    await page.getByRole('button', { name: '確定' }).click()
    await expect(page.getByText('設定を更新しますか？')).toBeVisible({ timeout: 3000 })
    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('✖️ボタンでポップアップを閉じられること', async ({ page }) => {
    await page.getByRole('button', { name: '確定' }).click()
    await expect(page.getByText('設定を更新しますか？')).toBeVisible({ timeout: 3000 })
    await page.locator('button[aria-label="Close"]').click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })
})

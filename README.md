

# llm-rag-ui

## Env

```
node v20
pnpm 8.9.2 
```

## Setup

Make sure to install the dependencies:

```bash
# pnpm
pnpm install
```

Create your .env

```
cp ./.env.example ./.env
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# pnpm
pnpm run dev
```

## Production

Build the application for production:

```bash
# pnpm
pnpm run build
```

## 利用パッケージ

- [Nuxt3](https://nuxt.com/). Framework that makes full-stack development with Vue.js intuitive
- [NuxtUI2](https://ui2.nuxt.com/getting-started). Set of Vue components and composables built with Tailwind CSS and Headless UI to help you build beautiful and accessible user interfaces.
- [Pinia](https://pinia.vuejs.org/). The intuitive store for Vue.js
- [axios](https://github.com/axios/axios). Promise based HTTP client for the browser and node.js

## Documentation

### Feature Configuration
- 📖 [Feature Configuration Guide](./docs/feature-configuration-guide.md) - Comprehensive guide to the feature flag system
- 📝 [Configuration Examples](./docs/feature-configuration-examples.md) - Practical examples and implementation patterns  
- ⚡ [Quick Reference](./docs/feature-configuration-quickref.md) - Quick setup checklist and common patterns

### Other Guides
- 🎯 [Tour System](./docs/tour-system.md) - Guide for the application tour system
- 🔧 [Multi-ref Tour Guide](./docs/multi-ref-tour-guide.md) - Advanced tour configuration
- 💡 [Highlight Fix Guide](./docs/highlight-fix-guide.md) - UI highlight fixes

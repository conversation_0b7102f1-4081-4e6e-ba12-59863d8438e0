{"name": "llm-rag-ui", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@codemirror/lang-html": "^6.4.9", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.4", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/simple-icons": "^1.2.20", "@nuxt/content": "^2.13.4", "@nuxt/fonts": "^0.10.3", "@nuxt/ui-pro": "^1.7.0", "@nuxtjs/i18n": "9.5.3", "@pinia/nuxt": "^0.9.0", "@unovis/ts": "^1.5.0", "@unovis/vue": "^1.5.0", "@vueuse/nuxt": "^12.4.0", "animate.css": "^4.1.1", "axios": "^1.7.9", "codemirror": "^6.0.1", "date-fns": "^4.1.0", "driver.js": "^1.3.6", "echarts": "^5.6.0", "export-to-csv": "^1.4.0", "file-saver": "^2.0.5", "jwt-decode": "^4.0.0", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "motion-v": "^1.2.1", "nuxt": "^3.15.1", "nuxt-permissions": "^0.2.4", "nuxt-tour": "^0.0.40", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "v-calendar": "^3.1.2", "vue-codemirror": "^6.1.1", "vue-echarts": "^7.0.3", "vue-number-animation": "^2.0.2", "vue-virtual-scroller": "2.0.0-beta.8", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@faker-js/faker": "^9.4.0", "@nuxt/eslint": "^0.7.5", "@playwright/test": "^1.51.1", "eslint": "^9.18.0", "sass-embedded": "^1.83.4", "vue-tsc": "^2.1.10"}, "resolutions": {"vue-tsc": "2.1.10"}, "packageManager": "pnpm@9.15.4", "overrides": {"vue": "latest"}}